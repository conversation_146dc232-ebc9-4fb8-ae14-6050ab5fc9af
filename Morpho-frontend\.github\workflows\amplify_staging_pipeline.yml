name: Staging build and hosting pipeline

on:
  pull_request:
    types: [closed]
    branches: ["staging-branch"]

jobs:
  react-build:
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/s3_staging_job.yml@main
    with:
      NODE_VERSION: ${{ vars.NODE_VERSION }}
      AWS_S3_BUILD_BUCKET: ${{ vars.AWS_S3_BUILD_BUCKET }}
      AWS_FRONTEND_SSM_ROLE_PATH: ${{ vars.AWS_FRONTEND_SSM_ROLE_PATH }}
    secrets: inherit

  amplify-deployment:
    needs: react-build
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/amplify_staging_job.yml@main
    with:
      AWS_FRONTEND_SSM_ROLE_PATH: ${{ vars.AWS_FRONTEND_SSM_ROLE_PATH }}
      AWS_S3_BUILD_BUCKET: ${{ vars.AWS_S3_BUILD_BUCKET }}
      AWS_AMPLIFY_STAGING_APP_NAME: ${{ vars.AWS_AMPLIFY_STAGING_APP_NAME }}
      AWS_AMPLIFY_STAGING_RESOURCE_NAME: ${{ vars.AWS_AMPLIFY_STAGING_RESOURCE_NAME }}
    secrets: inherit
