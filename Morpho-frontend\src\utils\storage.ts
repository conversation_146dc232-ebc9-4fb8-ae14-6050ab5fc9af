const TOKEN_KEY = "accessToken";
const USER_ID_KEY = "userId";
const SHOPIFY_CREDENTIALS_KEY = "shopifyCredentials";
const SUBSCRIPTION_STATUS_KEY = "subscriptionStatus";

interface ShopifyCredentials {
  shopName: string;
  apiKey: string;
  password: string;
}

interface SubscriptionStatus {
  status:
    | "inactive"
    | "active"
    | "past_due"
    | "canceled"
    | "canceling"
    | "none";
  plan: "free" | "basic" | "pro" | "enterprise" | null;
  subscriptionId: string | null;
  productLimit: number;
  productCount?: number;
}

export const storage = {
  setToken: (token: string) => {
    localStorage.setItem(TOKEN_KEY, token);
  },

  getToken: () => {
    return localStorage.getItem(TOKEN_KEY);
  },

  setUserId: (userId: string) => {
    localStorage.setItem(USER_ID_KEY, userId);
  },

  getUserId: () => {
    return localStorage.getItem(USER_ID_KEY);
  },

  clearAuth: () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_ID_KEY);
  },

  isAuthenticated: () => {
    return !!localStorage.getItem(TOKEN_KEY);
  },

  saveShopifyCredentials: (credentials: ShopifyCredentials) => {
    localStorage.setItem(SHOPIFY_CREDENTIALS_KEY, JSON.stringify(credentials));
  },

  getShopifyCredentials: (): ShopifyCredentials | null => {
    const credentialsStr = localStorage.getItem(SHOPIFY_CREDENTIALS_KEY);
    if (!credentialsStr) return null;

    try {
      return JSON.parse(credentialsStr) as ShopifyCredentials;
    } catch (e) {
      console.error("Failed to parse Shopify credentials", e);
      return null;
    }
  },

  clearShopifyCredentials: () => {
    localStorage.removeItem(SHOPIFY_CREDENTIALS_KEY);
  },

  setSubscriptionStatus: (status: SubscriptionStatus) => {
    localStorage.setItem(SUBSCRIPTION_STATUS_KEY, JSON.stringify(status));
  },

  getSubscriptionStatus: (): SubscriptionStatus | null => {
    const statusStr = localStorage.getItem(SUBSCRIPTION_STATUS_KEY);
    if (!statusStr) return null;

    try {
      return JSON.parse(statusStr) as SubscriptionStatus;
    } catch (e) {
      console.error("Failed to parse subscription status", e);
      return null;
    }
  },

  clearSubscriptionStatus: () => {
    localStorage.removeItem(SUBSCRIPTION_STATUS_KEY);
  },
};
