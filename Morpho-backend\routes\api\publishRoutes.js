const router = require("express").Router();
const controller = require("../../controllers/publishController");
const verifyJWT = require("../../middleware/verifyJWT");
const verifyPlanAccess = require("../../middleware/verifyPlanAccess");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
router.post(
  "/save-settings",
  verifyJWT,
  verifyPlanAccess({ checkProductLimit: true }),
  asyncErrorHandler(controller.createPublish)
);
router.get("/get-all", asyncError<PERSON>andler(controller.getAll));
router.post("/get-by-id", asyncError<PERSON><PERSON><PERSON>(controller.getById));
router.post("/get-by-project", asyncError<PERSON>and<PERSON>(controller.getByProject));
router.post("/get-by-glb", asyncError<PERSON><PERSON><PERSON>(controller.getByGlb));
router.post(
  "/generate-link",
  verifyJWT,
  verifyPlanAccess({ checkProductLimit: true }),
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.generateLink)
);
// router.post("/save-settings", controller.saveSettings);
router.put(
  "/update-by-id",
  verifyJWT,
  asyncErrorHandler(controller.updateById)
);
router.delete(
  "/delete-by-id",
  verifyJWT,
  asyncErrorHandler(controller.deleteById)
);
router.get(
  "/get-publish-details/:_id",
  asyncErrorHandler(controller.getPublishDetails)
);
router.post(
  "/generate-link-mobile",
  verifyJWT,
  verifyPlanAccess({ checkProductLimit: true }),
  asyncErrorHandler(controller.generateLinkMobileVersion)
);
module.exports = router;
