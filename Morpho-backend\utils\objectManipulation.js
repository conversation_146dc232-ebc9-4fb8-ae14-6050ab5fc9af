const deepCopyObject = (source) => {
  // Check if the source is not an object or is null
  if (typeof source !== "object" || source === null) {
    return source; // Return the value if it's not an object
  }

  // Create an array or object to hold the values
  const copy = Array.isArray(source) ? [] : {};

  // Recursively copy properties from the source to the copy
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      copy[key] = deepCopyObject(source[key]);
    }
  }

  return copy;
};
module.exports = { deepCopyObject };
