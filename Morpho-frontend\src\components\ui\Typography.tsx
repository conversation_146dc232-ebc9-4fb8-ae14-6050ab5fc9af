import React, { ReactNode } from 'react';

interface TypographyProps {
  children: ReactNode;
  className?: string;
}

// Heading Components with consistent sizing and spacing
export const H1: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight tracking-tight mb-6 ${className}`}>
    {children}
  </h1>
);

export const H2: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <h2 className={`text-3xl md:text-4xl font-bold text-white mb-6 tracking-tight ${className}`}>
    {children}
  </h2>
);

export const H3: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <h3 className={`text-2xl font-semibold text-white mb-4 ${className}`}>
    {children}
  </h3>
);

export const H4: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <h4 className={`text-xl font-semibold text-white mb-3 ${className}`}>
    {children}
  </h4>
);

// Text Components
export const Lead: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <p className={`text-xl text-gray-300 mb-6 leading-relaxed ${className}`}>
    {children}
  </p>
);

export const Body: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <p className={`text-base text-gray-300 mb-4 leading-relaxed ${className}`}>
    {children}
  </p>
);

export const Subtle: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <p className={`text-sm text-gray-400 ${className}`}>
    {children}
  </p>
);

// Special text components
export const Gradient: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <span className={`bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent ${className}`}>
    {children}
  </span>
);

export const HighlightedText: React.FC<TypographyProps> = ({ children, className = '' }) => (
  <span className={`text-blue-400 font-medium ${className}`}>
    {children}
  </span>
);

// Section intro component for consistent section headers
export const SectionIntro: React.FC<TypographyProps & { subtitle?: string }> = ({ 
  children, 
  subtitle,
  className = '' 
}) => (
  <div className={`max-w-3xl mx-auto text-center mb-16 ${className}`}>
    <H2>{children}</H2>
    {subtitle && <Lead className="mt-4">{subtitle}</Lead>}
  </div>
);