import { useState, useEffect, useCallback } from 'react';
import { api } from '../services/api';
import { storage } from '../utils/storage';
import type { Model3D } from '../types/models';

export const useModels = () => {
  const [models, setModels] = useState<Model3D[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState(0);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  const fetchModels = useCallback(async (force = false) => {
    // Check auth and cache
    const token = storage.getToken();
    if (!force && Date.now() - lastFetch < CACHE_DURATION) {
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await api.getModels();
      
      if (!response.success) {
        setError(response.error || 'Failed to fetch models');
        setModels([]);
        return;
      }
      
      setModels(response.data);
      setLastFetch(Date.now());
    } catch (err) {
      const errorMessage = err instanceof Error ? 
        err.message : 
        'An unexpected error occurred';
      console.error('Error fetching models:', errorMessage);
      setError(errorMessage);
      setModels([]); // Reset models on error
    } finally {
      setLoading(false);
    }
  }, [lastFetch]);

  useEffect(() => {
    const token = storage.getToken();
    if (token) {
      fetchModels();
    }
  }, [fetchModels]);

  return { 
    models, 
    loading, 
    error,
    refresh: () => fetchModels(true),
    clearError: () => setError(null)
  };
};