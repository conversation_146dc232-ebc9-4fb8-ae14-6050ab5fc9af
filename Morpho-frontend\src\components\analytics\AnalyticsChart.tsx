import React from 'react';

interface AnalyticsChartProps {
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      color: string;
    }>;
  };
  height?: number;
}

const AnalyticsChart: React.FC<AnalyticsChartProps> = ({ 
  data,
  height = 300
}) => {
  const maxValue = Math.max(...data.datasets.flatMap(d => d.data));

  return (
    <div style={{ height }} className="relative">
      <div className="absolute inset-0 flex items-end justify-between gap-2">
        {data.labels.map((label, index) => (
          <div key={label} className="w-full flex flex-col items-center gap-2">
            <div className="w-full flex flex-col gap-1">
              {data.datasets.map((dataset, datasetIndex) => (
                <div
                  key={dataset.label}
                  className={`w-full ${dataset.color} rounded-sm transition-all duration-300`}
                  style={{ height: `${(dataset.data[index] / maxValue) * 100}%` }}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500">{label}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AnalyticsChart;