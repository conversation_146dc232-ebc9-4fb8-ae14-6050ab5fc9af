const textureSchema = require("../models/Texture");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
class Controller {
  createTexture = async (req, res) => {
    // Get texture details from request body
    const {
      texture_name,
      texture_description,
      texture_imageUrl,
      texture_url,
      customized_material,
      asset_library,
    } = req.body;

    // Validate required fields
    if (!texture_name) {
      throw new expressError(
        "Texture name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!texture_url) {
      throw new expressError(
        "Texture URL is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Create new texture
    const newTexture = new textureSchema({
      texture_name,
      texture_description,
      texture_imageUrl,
      texture_url,
      customized_material,
      asset_library,
    });

    // Save texture to database
    await newTexture.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Texture created successfully",
      true,
      newTexture
    );
  };
  getAllTextures = async (req, res) => {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Validate pagination parameters
    if (page < 1) {
      throw new expressError(
        "Page number must be greater than 0",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    if (limit < 1) {
      throw new expressError(
        "Limit must be greater than 0",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Get total count
    const totalCount = await textureSchema.countDocuments();

    // Check if textures exist
    if (totalCount === 0) {
      throw new expressError(
        "No textures found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch textures with pagination
    const allTextures = await textureSchema.find({}).skip(skip).limit(limit);

    // Return paginated response
    return res.status(200).json({
      message: "Textures retrieved successfully",
      success: true,
      data: allTextures,
      total: totalCount,
      page: page,
      perPage: limit,
    });
  };
  getTextureById = async (req, res) => {
    // Get texture ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Texture ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid texture ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Find texture by ID
      const texture = await textureSchema.findOne({ _id: id });

      // Check if texture exists
      if (!texture) {
        throw new expressError(
          "Texture not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Texture retrieved successfully",
        true,
        texture
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid texture ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  getTextureWithCustomizedMaterial = async (req, res) => {
    // Get texture ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Texture ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid texture ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Find texture by ID and populate customized material
      const texture = await textureSchema
        .findOne({ _id: id })
        .populate("customized_material");

      // Check if texture exists
      if (!texture) {
        throw new expressError(
          "Texture not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Texture retrieved successfully",
        true,
        texture
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid texture ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  getTextureWithAsset = async (req, res) => {
    // Get texture ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Texture ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid texture ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Find texture by ID and populate asset library
      const texture = await textureSchema
        .findOne({ _id: id })
        .populate("asset_library");

      // Check if texture exists
      if (!texture) {
        throw new expressError(
          "Texture not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Texture retrieved successfully",
        true,
        texture
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid texture ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  getTextureWithAllInformation = async (req, res) => {
    // Get texture ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Texture ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid texture ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Find texture by ID and populate all references
      const texture = await textureSchema
        .findOne({ _id: id })
        .populate("customized_material")
        .populate("asset_library");

      // Check if texture exists
      if (!texture) {
        throw new expressError(
          "Texture not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Texture retrieved successfully",
        true,
        texture
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid texture ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  deleteTexture = async (req, res) => {
    // Get texture ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Texture ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid texture ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete texture
    const deletedTexture = await textureSchema.findByIdAndDelete(id);

    // Check if texture exists
    if (!deletedTexture) {
      throw new expressError(
        "Texture not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(res, 200, "Texture deleted successfully", true);
  };
}
const controller = new Controller();
module.exports = controller;
