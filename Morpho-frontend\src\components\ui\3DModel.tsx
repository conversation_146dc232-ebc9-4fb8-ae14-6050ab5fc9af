import React, { useEffect, useRef, useState, useMemo } from 'react';
import * as THREE from 'three';

interface ThreeDModelProps {
  className?: string;
  rotationSpeed?: number;
  modelType?: 'cube' | 'sphere' | 'torus';
  color?: string;
  backgroundColor?: string;
  interactive?: boolean;
}

export const ThreeDModel: React.FC<ThreeDModelProps> = ({
  className = '',
  rotationSpeed = 0.01,
  modelType = 'cube',
  color = '#0066CC',
  backgroundColor = 'transparent',
  interactive = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const modelRef = useRef<THREE.Mesh | null>(null);
  const animationFrameId = useRef<number | null>(null);
  const mousePosition = useRef({ x: 0, y: 0 });
  const initialModelRotation = useRef({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const frameCountRef = useRef(0);

  // Create geometry based on model type - memoize to avoid recreating
  const createGeometry = useMemo(() => {
    switch (modelType) {
      case 'sphere':
        return new THREE.SphereGeometry(1.5, 24, 24); // Reduced segments
      case 'torus':
        return new THREE.TorusGeometry(1, 0.4, 12, 48); // Reduced segments
      case 'cube':
      default:
        return new THREE.BoxGeometry(2, 2, 2);
    }
  }, [modelType]);

  // Material with optimized settings
  const createMaterial = useMemo(() => {
    return new THREE.MeshStandardMaterial({
      color: new THREE.Color(color),
      metalness: 0.3,
      roughness: 0.4,
      emissive: new THREE.Color(color),
      emissiveIntensity: 0.2,
    });
  }, [color]);

  useEffect(() => {
    // Visibility observer to only render when in view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          setIsVisible(entry.isIntersecting);
        });
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!containerRef.current || rendererRef.current) return;

    const container = containerRef.current;
    const width = container.clientWidth;
    const height = container.clientHeight;

    // Initialize scene
    sceneRef.current = new THREE.Scene();
    
    // Initialize camera with optimized settings
    cameraRef.current = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    cameraRef.current.position.z = 5;

    // Initialize renderer with optimized settings
    rendererRef.current = new THREE.WebGLRenderer({ 
      antialias: false, // Disable antialiasing for performance
      alpha: backgroundColor === 'transparent',
      powerPreference: 'high-performance',
    });
    rendererRef.current.setSize(width, height);
    rendererRef.current.setPixelRatio(1); // Use a low pixel ratio for better performance
    
    // Set background
    if (backgroundColor !== 'transparent') {
      sceneRef.current.background = new THREE.Color(backgroundColor);
    }
    
    // Add renderer to DOM
    container.appendChild(rendererRef.current.domElement);

    // Create mesh
    modelRef.current = new THREE.Mesh(createGeometry, createMaterial);
    sceneRef.current.add(modelRef.current);
    
    // Add lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    sceneRef.current.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 5, 5);
    sceneRef.current.add(directionalLight);

    // Store initial rotation
    if (modelRef.current) {
      initialModelRotation.current = {
        x: modelRef.current.rotation.x,
        y: modelRef.current.rotation.y,
      };
    }

    // Mouse interaction setup - throttled for performance
    let isThrottled = false;
    const handleMouseMove = (event: MouseEvent) => {
      if (isThrottled) return;
      
      isThrottled = true;
      setTimeout(() => {
        isThrottled = false;
      }, 32); // ~30fps for mouse tracking
      
      const rect = container.getBoundingClientRect();
      mousePosition.current = {
        x: ((event.clientX - rect.left) / width) * 2 - 1,
        y: -((event.clientY - rect.top) / height) * 2 + 1,
      };
    };

    if (interactive) {
      window.addEventListener('mousemove', handleMouseMove);
    }

    // Handle resize
    const handleResize = () => {
      if (!cameraRef.current || !rendererRef.current || !container) return;
      
      const newWidth = container.clientWidth;
      const newHeight = container.clientHeight;
      
      cameraRef.current.aspect = newWidth / newHeight;
      cameraRef.current.updateProjectionMatrix();
      
      rendererRef.current.setSize(newWidth, newHeight);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (rendererRef.current && container) {
        container.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }
      
      if (animationFrameId.current !== null) {
        cancelAnimationFrame(animationFrameId.current);
      }
      
      window.removeEventListener('resize', handleResize);
      
      if (interactive) {
        window.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }, [backgroundColor, createGeometry, createMaterial, interactive]);

  // Animation loop
  useEffect(() => {
    const animate = () => {
      if (!modelRef.current || !sceneRef.current || !cameraRef.current || !rendererRef.current) return;
      
      // Skip frames for performance
      frameCountRef.current++;
      if (frameCountRef.current % 2 === 0 && isVisible) {
        // Auto-rotation
        modelRef.current.rotation.x += rotationSpeed * 0.5;
        modelRef.current.rotation.y += rotationSpeed;

        // Interactive rotation based on mouse position
        if (interactive) {
          modelRef.current.rotation.x = initialModelRotation.current.x + mousePosition.current.y * 0.5;
          modelRef.current.rotation.y = initialModelRotation.current.y + mousePosition.current.x * 0.5;
        }

        // Render
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
      
      animationFrameId.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationFrameId.current !== null) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [rotationSpeed, interactive, isVisible]);

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
    />
  );
};