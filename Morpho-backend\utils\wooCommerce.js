const axios = require('axios');

const setWooCommerceClient = (req, res, next) => {
  const { baseUrl, consumerKey, consumerSecret } = req.body;

  if (!baseUrl || !consumerKey || !consumerSecret) {
    return res.status(400).json({ success: false, message: 'Missing WooCommerce credentials' });
  }

  // Attach WooCommerce client to the request object
  req.wooCommerceClient = axios.create({
    baseURL: `${baseUrl}/wp-json/wc/v3`,
    auth: {
      username: consumerKey,
      password: consumerSecret,
    },
  });

  next();
};

module.exports = setWooCommerceClient;
