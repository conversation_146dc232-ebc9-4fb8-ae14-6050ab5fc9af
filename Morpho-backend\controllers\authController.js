const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const user = require("../models/User");
const roleSchema = require("../models/Role");
const nodemailerController = require("./verifyOtpController");
const expressError = require("../errors/expressError");
const {
  ACCESS_TOKEN_SECRET,
  REFRESH_TOKEN_SECRET,
  CLIENT_URL,
} = require("../config/envVariables");
const { generateToken } = require("../utils/generateResetToken");
const {
  generateResetToken,
  saveResetLinkToUser,
  verifyResetLink,
} = require("../utils/generateResetToken");
const { sendResetEmail } = require("../utils/sendResetEmail");
const userModel = require("../models/User");

class Controller {
  hashPassword = async (password) => {
    // Use a lower salt round (10 instead of 12) for faster hashing
    // Still secure but significantly faster
    return await bcrypt.hash(password, 10);
  };
  signup = async (req, res) => {
    const { password, role, email } = req.body;

    // Validate required fields
    if (!email) {
      throw new expressError(
        "Email is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!password) {
      throw new expressError(
        "Password is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Check if user already exists - this is a fast operation
    const userFound = await userModel.findOne({ email });
    if (userFound) {
      throw new expressError(
        "An account with this email already exists. Please use a different email or try logging in.",
        409,
        expressError.CODES.RESOURCE_ALREADY_EXISTS
      );
    }

    // Check if role is restricted
    if (role) {
      // Only fetch role if needed
      const roleData = await roleSchema.findById(role).catch(() => null);

      if (
        roleData &&
        (roleData.role === "Developer" || roleData.role === "Admin")
      ) {
        throw new expressError(
          "Cannot assign restricted roles",
          403,
          expressError.CODES.PERMISSION_DENIED
        );
      }
    }

    // Hash password - this is CPU intensive
    const hashedPassword = await this.hashPassword(password);

    // Check if role is provided in the request
    let userRole = req.body.role;

    // If role is not provided or is empty, find and use the Client role
    if (!userRole || userRole === "") {
      // Find the Client role by name
      const clientRole = await roleSchema.findOne({ role: "Client" }).lean();

      // Check if Client role was found
      if (!clientRole) {
        throw new expressError(
          "Error setting up user role. Please contact support.",
          500,
          expressError.CODES.DATABASE_ERROR
        );
      }

      userRole = clientRole._id;
    }

    // Create the user with the assigned role
    const newUser = await user.create({
      ...req.body,
      verified: false,
      password: hashedPassword,
      subscriptionStatus: "inactive",
      role: userRole, // Use the role from request or default to Client role
    });

    // Send OTP verification asynchronously - don't wait for email to be sent
    // This is the key optimization - we respond to the client before the email is sent
    const userId = newUser._id;

    // Respond to the client immediately
    res.status(200).json({
      status: "Pending",
      message: "Verification OTP email sent",
      data: {
        userId,
        email,
      },
    });

    // Send the OTP email in the background
    nodemailerController.sendOTPVerificationAsync(newUser);
  };

  signUpDeveloper = async (req, res) => {
    const { password, email } = req.body;

    // Validate required fields
    if (!email) {
      throw new expressError(
        "Email is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!password) {
      throw new expressError(
        "Password is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Check if user already exists
    const userFound = await userModel.findOne({ email });
    if (userFound) {
      throw new expressError(
        "An account with this email already exists. Please use a different email.",
        409,
        expressError.CODES.RESOURCE_ALREADY_EXISTS
      );
    }

    // Check if role is provided in the request
    let userRole = req.body.role;

    // If role is not provided or is empty, find and use the Client role
    if (!userRole || userRole === "") {
      // Find the Client role by name
      const clientRole = await roleSchema.findOne({ role: "Client" }).lean();

      // Check if Client role was found
      if (!clientRole) {
        throw new expressError(
          "Error setting up user role. Please contact support.",
          500,
          expressError.CODES.DATABASE_ERROR
        );
      }

      userRole = clientRole._id;
    }

    // Hash password directly using our optimized method
    const hashedPassword = await this.hashPassword(password);

    // Create the user with the assigned role
    const newUser = await user.create({
      ...req.body,
      verified: false,
      password: hashedPassword,
      subscriptionStatus: "inactive",
      role: userRole, // Use the role from request or default to Client role
    });

    // Respond to the client immediately
    res.status(200).json({
      status: "Pending",
      message: "Verification OTP email sent",
      data: {
        userId: newUser._id,
        email,
      },
    });

    // Send the OTP email in the background
    nodemailerController.sendOTPVerificationAsync(newUser);
  };
  login = async (req, res) => {
    const { email, password } = req.body;

    // Validate required fields
    if (!email) {
      throw new expressError(
        "Email is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!password) {
      throw new expressError(
        "Password is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find user by email
    const userLogIn = await user.findOne({ email }).populate("role");

    // Check if user exists
    if (!userLogIn) {
      throw new expressError(
        "Account not found. Please check your email or sign up.",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Check if account is deleted
    if (userLogIn.isDeleted) {
      throw new expressError(
        "This account has been deactivated",
        403,
        expressError.CODES.PERMISSION_DENIED
      );
    }

    // Get role object
    let roleObject = null;
    if (userLogIn.role) {
      roleObject = await roleSchema.findById(userLogIn.role).catch(() => null);
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, userLogIn.password);
    if (!isPasswordValid) {
      throw new expressError(
        "Invalid email or password",
        401,
        expressError.CODES.INVALID_CREDENTIALS
      );
    }

    // Create tokens
    const accessToken = generateToken(
      userLogIn,
      ACCESS_TOKEN_SECRET,
      "30d",
      roleObject
    );

    const refreshToken = generateToken(
      userLogIn,
      REFRESH_TOKEN_SECRET,
      "40d",
      roleObject
    );

    // Update user with refresh token
    userLogIn.refreshToken = refreshToken;

    // Ensure subscription status is valid
    if (userLogIn.subscriptionStatus === "none") {
      userLogIn.subscriptionStatus = "inactive";
    }

    await userLogIn.save();

    // Set refresh token cookie
    res.cookie("jwt", refreshToken, {
      httpOnly: true,
      sameSite: "None",
      secure: true,
      maxAge: **********,
    });

    // Check if account is verified
    if (!userLogIn.verified) {
      throw new expressError(
        "Your account is not verified. Please check your email for verification instructions.",
        403,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Determine admin status
    let isAdmin = false;
    if (userLogIn.role.role === "Designer" || userLogIn.role.role === "Admin") {
      isAdmin = true;
    }

    // Prepare user data for response
    const userData = userLogIn.toObject();
    userData.role = userLogIn.role.role;
    userData.isAdmin = isAdmin;
    userData.password = null;

    // Return success response
    return res.status(200).json({
      message: "Login successful",
      success: true,
      data: userData,
      accessToken: accessToken,
    });
  };

  handleRefeshToken = async (req, res) => {
    // Get cookies from request
    const cookies = req.headers.cookies;

    // Check if cookies exist
    if (!cookies) {
      throw new expressError(
        "No refresh token found",
        400,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Extract refresh token
    const refreshToken = cookies.split("=")[1];

    // Find user with this refresh token
    const foundUser = await user.findOne({ refreshToken });

    // Check if user exists
    if (!foundUser) {
      throw new expressError(
        "Invalid refresh token",
        403,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Verify the refresh token
    try {
      const decoded = jwt.verify(refreshToken, REFRESH_TOKEN_SECRET);

      // Check if the token belongs to the user
      if (foundUser._id.toString() !== decoded.userId) {
        throw new expressError(
          "Token validation failed",
          403,
          expressError.CODES.AUTHENTICATION_REQUIRED
        );
      }

      // Generate new access token
      const accessToken = generateToken(decoded, ACCESS_TOKEN_SECRET, {
        expiresIn: "30d",
      });

      // Return new access token
      return res.json({
        success: true,
        accessToken,
      });
    } catch (jwtError) {
      throw new expressError(
        "Invalid or expired token",
        403,
        expressError.CODES.SESSION_EXPIRED
      );
    }
  };
  logout = async (req, res) => {
    // On client, also delete the accessToken
    const cookie = req.headers.cookies;

    // If no cookie, still return success (user is effectively logged out)
    if (!cookie) {
      return res.status(200).json({
        success: true,
        message: "Logged out successfully",
      });
    }

    // Extract refresh token
    const refreshToken = cookie.split("=")[1];

    // Find user with this refresh token
    const foundUser = await user.findOne({ refreshToken });

    // If user found, clear their refresh token
    if (foundUser) {
      // Delete refresh token in db
      foundUser.refreshToken = "";
      await foundUser.save();

      // Clear cookie
      res.clearCookie("jwt", {
        httpOnly: true,
        sameSite: "None",
        secure: true,
      });
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: "Logged out successfully",
    });
  };
  logoutPhone = async (req, res) => {
    const userId = req.user;

    // Find user by ID
    const foundUser = await user.findById(userId);

    // If user not found, return error
    if (!foundUser) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Clear refresh token
    foundUser.refreshToken = "";
    await foundUser.save();

    // Return success response
    return res.status(200).json({
      success: true,
      message: "Logged out successfully",
    });
  };
  changePassword = async (req, res) => {
    const userId = req.user;
    const { currentPassword, newPassword } = req.body;

    // Validate required fields
    if (!currentPassword) {
      throw new expressError(
        "Current password is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!newPassword) {
      throw new expressError(
        "New password is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find user by ID
    const userToBeFetched = await user.findById(userId);

    // Check if user exists
    if (!userToBeFetched) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(
      currentPassword,
      userToBeFetched.password
    );

    if (!isPasswordValid) {
      throw new expressError(
        "Current password is incorrect",
        401,
        expressError.CODES.INVALID_CREDENTIALS
      );
    }

    // Hash new password
    const hashedNewPassword = await this.hashPassword(newPassword);

    // Update password
    userToBeFetched.password = hashedNewPassword;
    await userToBeFetched.save();

    // Return success response
    return res.status(200).json({
      success: true,
      message: "Password changed successfully",
    });
  };
  forgotPassword = async (req, res) => {
    const { email } = req.body;

    // Validate required fields
    if (!email) {
      throw new expressError(
        "Email is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find user by email
    const userToBeFetched = await user.findOne({ email });

    // For security reasons, always return success even if user not found
    // This prevents email enumeration attacks
    if (!userToBeFetched) {
      // Still return success to prevent email enumeration
      return res.status(200).json({
        success: true,
        message:
          "If your email is registered, you will receive password reset instructions",
      });
    }

    // Generate reset token
    const token = generateResetToken(userToBeFetched);

    // Create reset link
    const resetLink = `${CLIENT_URL}/resetpassword/${token}`;

    // Save reset link to user
    await saveResetLinkToUser(userToBeFetched, token);

    // Send reset email
    sendResetEmail(userToBeFetched.email, token, resetLink);

    // Return success response
    return res.status(200).json({
      success: true,
      message:
        "If your email is registered, you will receive password reset instructions",
    });
  };
  resetPassword = async (req, res) => {
    const { resetLink, newPass } = req.body;

    // Validate required fields
    if (!resetLink) {
      throw new expressError(
        "Reset link is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!newPass) {
      throw new expressError(
        "New password is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Verify reset link
    try {
      verifyResetLink(resetLink);
    } catch (error) {
      throw new expressError(
        "Invalid or expired reset link",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find user by reset link
    const userToUpdate = await user.findOne({ resetLink });

    // Check if user exists
    if (!userToUpdate) {
      throw new expressError(
        "Invalid reset link",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Hash new password
    const hashedPassword = await this.hashPassword(newPass);

    // Update password and clear reset link
    userToUpdate.password = hashedPassword;
    userToUpdate.resetLink = "";
    await userToUpdate.save();

    // Return success response
    return res.status(200).json({
      success: true,
      message: "Password has been reset successfully",
    });
  };
}

const controller = new Controller();
module.exports = controller;
