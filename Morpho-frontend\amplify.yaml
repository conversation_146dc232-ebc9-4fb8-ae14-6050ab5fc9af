version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: dist
    files:
      - "**/*"
  customHeaders:
    - pattern: "**/*.js"
      headers:
        - key: "Content-Type"
          value: "application/javascript"
    - pattern: "**/*.mjs"
      headers:
        - key: "Content-Type"
          value: "application/javascript"
    - pattern: "**/*.js.map"
      headers:
        - key: "Content-Type"
          value: "application/json"
    - pattern: "**/*.css"
      headers:
        - key: "Content-Type"
          value: "text/css"
