const router = require("express").Router();
const controller = require("../../controllers/templateController");
const { validateTemplateSchema } = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");
const { admin, designer } = require("../../constants");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
router.get(
  "/get-all-templates",
  // verifyRoles([admin, designer]),
  async<PERSON>rror<PERSON>and<PERSON>(controller.getAllTemplate)
);
router.get(
  "/get-template-by-id",
  async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.getTemplateById)
);
router.get(
  "/get-template-with-project",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getTemplateWithProject)
);
router.delete(
  "/delete-template",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.deleteTemplateById)
);
router
  .route("/create-template")
  .post(
    validateTemplateSchema,
    verifyRoles([admin, designer]),
    async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.createTemplate)
  );

module.exports = router;
