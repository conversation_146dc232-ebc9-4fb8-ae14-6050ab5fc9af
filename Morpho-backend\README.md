# Morpho-backend

Backend Side of Morpho Platform

# CI/CD Pipeline of project

## What does the pipeline do?

The backend pipeline can build any backend application (Node.js, .Net, etc...) into a Docker Containerised image and deploys it to the internet via AWS ECS as an orchestration tool.

## How to use the pipeline?

### Triggering the pipeline

First things first, a developer will need to checkout their code into a new or different branch.
When a developer has finished their updates and would like to deploy the project online, they can run the pipeline the following way:

1. Push the latest code to your newly created or pre-existing branch
2. Go to [Github](https://github.com) and select the repository
3. Select "Pull Requests" tab
4. Create a new pull request, this pull request should merge your created or pre-existing branch with the main branch.
5. Complete the merging process
6. (Optionnal) Delete the branch, you can keep it if you would like to re-use it tho.

After the merge is closed, the pipeline should run ( check it out in the Actions page by clicking on the "Actions" tab in the repository ).
_This pipeline follows the "on: pull-request" strategy for being triggered._

## Miscellaneous changes to the pipeline

### Changing variables

If you desire to change some of the variables for the project, please follow these steps:

#### From Github

1. Open [Github](https://github.com)
2. Open the repository
3. Navigate to "Settings"
4. Open the "Secrets and variables" Dropdown underneath "Security" section, followed by Actions
5. Open "Variables"
6. Edit a variable key
7. Add a value to your liking

#### From VSCode

1. Download an extension called Github Actions
2. Open the tab for the extension
3. On the bottom of the Extension's Panel in Setting, Open the "Variables" dropdown box
4. Click on Edit for a variable key
5. Add a value to your liking

#### From gh cli on your terminal ( not available on al OSes )

1. In this repository, open a file called .ghvariables.env
2. Change the variable/s to your liking
3. Run the following command to update the variables: `gh variable set -f ./.ghvariables.env`

N.B: the variables and secrets are stored on the repository-level, so they are global to the project no matter what branch you're on.

## Important remarks concerning the pipeline

- The project link is https://api.modularcx.link/your-application-route/...
- Pushing directly to the main branch will **NOT** trigger the pipeline
- If the pipeline fails, you usually receive an email from github informing you so, or
  you can check it manually in the "Actions" Tab, the workflow would have an "X" in red next to it if a failure occured.
  In such scenario, please inform Jad Beydoun so they can fix the pipeline
- Please be patient for the pipeline's build step, sometimes building the docker images can take some time to complete.

## For the curious ones

### What is happening under the hood ?

The Github Actions pipeline builds the backend applications into Docker Images and deploys them to Dockerhub, following this step, the Docker Images are deployed to AWS, the services in use are CLI, IAM, ECS, EC2 and its features such as ALB, ASG, EC2 TGs, and Route 53.

- CLI for running commands in the pipeline
- IAM for giving the necessary policy for the github action to work ( we are following the STS Role assumption to follow industry best-practices )
- ECS, where our docker images run in an orchestration manner similar to Kubernetes, these images have each their own Task Definition, and each their own Service and Tasks, all inside one cluster called "Backend-Cluster".
- EC2 is used to store our ECS tasks inside of them, these servers are automatically deployed with the help of a launch template that deploys our needed server with predefined configurations ( Confihurations such as OS, VPC, variables, etc... ). We are also using features for EC2 such as ASG ( or Auto Scaling Goup) to scale horizontally our images, ALB ( Application Load Balancer ) to distribute the incoming traffic to the images with each their respective rule, target link, necessary health checks etc...
- Route 53 for creating and association of our projects to a domain that we own, this domain is linked with our ALB

This pipeline relies heavily on shell scripts and AWS CLI commands to work on github action servers.

## CI/CD Pipeline Members

[Jad Beydoun](https://github.com/MJayybee21)
