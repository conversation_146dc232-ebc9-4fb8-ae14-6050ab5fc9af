const productionSchema = require("../models/Production");
const resHandle = require("../utils/responseHandle");

class Controller {
  createProduction = async (req, res) => {
    try {
      const { name, description, project, imageUrl } = req.body;
      let newProduction = new productionSchema({
        name: name,
        description: description,
        project: project,
        imageUrl: imageUrl,
      });
      await newProduction.save();
      resHandle.handleData(
        res,
        200,
        "Production Created Successfully",
        true,
        newProduction,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getAllProductions = async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;
      const totalCount = await productionSchema.countDocuments();
      if (totalCount == 0)
        return resHandle.handleError(res, 404, `No Production Found`);
      let productionToBeRetrieved = await productionSchema
        .find()
        .skip(skip)
        .limit(limit);
      resHandle.handleData(
        res,
        200,
        "Production retrieved Successfully",
        true,
        productionToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getProductionById = async (req, res) => {
    try {
      const { id } = req.body;
      let productionToBeRetrieved = await productionSchema.findOne({ _id: id });
      if (!productionToBeRetrieved)
        return resHandle.handleError(res, 404, `Production Not Found`);

      resHandle.handleData(
        res,
        200,
        "Production retrieved Successfully",
        true,
        productionToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  deleteProductionById = async (req, res) => {
    try {
      const { id } = req.body;
      let productionToBeDeleted = await productionSchema.findByIdAndDelete(id);
      if (!productionToBeDeleted)
        return resHandle.handleError(res, 404, `Production Not Found`);
      resHandle.handleData(res, 200, "Production Deleted Successfully", true);
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getProductionWithProject = async (req, res) => {
    try {
      const { id } = req.body;
      let productionToBeRetrieved = await productionSchema
        .findOne({ _id: id })
        .populate("project");
      if (!productionToBeRetrieved)
        return resHandle.handleError(res, 404, `Production Not Found`);

      resHandle.handleData(
        res,
        200,
        "Production Retrieved Successfully",
        true,
        productionToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
}

const controller = new Controller();
module.exports = controller;
