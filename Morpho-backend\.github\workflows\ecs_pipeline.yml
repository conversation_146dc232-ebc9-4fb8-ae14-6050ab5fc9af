name: ECS Deployment Pipeline

on:
  pull_request:
    types: [closed]
    branches: [main]

jobs:
  # test:
  #   uses: Modular3D/UniversalCICDPipelines/.github/workflows/jest_test.yml
  #   with:
  #     NODE_VERSION: ${{ vars.NODE_VERSION }}
  #   secrets:
  #     inherit

  build:
    # needs: test
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/docker_deployment.yml@main
    with:
      NODE_VERSION: ${{ vars.NODE_VERSION }}
      DOCKER_IMAGE: ${{ vars.DOCKER_IMAGE }}
      DOCKER_TAG: ${{ vars.DOCKER_TAG }}
      CONTAINER_PORT: ${{ vars.CONTAINER_PORT }}
      AWS_BACKEND_SSM_ROLE_PATH: ${{ vars.AWS_BACKEND_SSM_ROLE_PATH }}
      AWS_TARGET_GROUP_PATH: ${{ vars.AWS_TARGET_GROUP_PATH }}
    secrets: inherit

  deploy:
    needs: build
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/ecs_deployment.yml@main
    with:
      AWS_BACKEND_SSM_ROLE_PATH: ${{ vars.AWS_BACKEND_SSM_ROLE_PATH }}
      TASK_DEFINITION_PATH: ${{ vars.TASK_DEFINITION_PATH }}
      AWS_CLUSTER_NAME: ${{ vars.AWS_CLUSTER_NAME }}
      AWS_TARGET_GROUP_PATH: ${{ vars.AWS_TARGET_GROUP_PATH }}
      AWS_LOAD_BALANCER_LISTENER_ARN: ${{ vars.AWS_LOAD_BALANCER_LISTENER_ARN }}
      AWS_LOAD_BALANCER_RULE_ARN: ${{ vars.AWS_LOAD_BALANCER_RULE_ARN }}
      AWS_PROJECT_NAME: ${{ vars.AWS_PROJECT_NAME }}
      AWS_TASKS_TO_RUN_NUMBERS: ${{ vars.AWS_TASKS_TO_RUN_NUMBERS }}
      AWS_VPC_ID: ${{ vars.AWS_VPC_ID }}
      CONTAINER_PORT: ${{ vars.CONTAINER_PORT }}
      DOCKER_IMAGE: ${{ vars.DOCKER_IMAGE }}
    secrets: inherit
