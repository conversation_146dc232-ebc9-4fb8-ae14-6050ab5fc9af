import React, { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Button } from "../ui/Button";
import { GlassContainer } from "../ui/GlassContainer";
import { Logo } from "../ui/Logo";
import { ScrollProgress } from "../ui/ScrollProgress";
import { useNavigate } from "react-router-dom";

export const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeLink, setActiveLink] = useState<string | null>(null);

  useEffect(() => {
    // Throttled scroll handler for better performance
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          setIsScrolled(window.scrollY > 10);

          // Simplified active section detection
          const sections = ["features", "testimonials", "pricing", "faq"];
          for (const section of sections) {
            const element = document.getElementById(section);
            if (element) {
              const rect = element.getBoundingClientRect();
              if (rect.top <= 100 && rect.bottom >= 100) {
                setActiveLink(section);
                break;
              }
            }
          }

          ticking = false;
        });

        ticking = true;
      }
    };

    // Use passive flag for better performance
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks = [
    { href: "#features", label: "Features" },
    { href: "#testimonials", label: "Testimonials" },
    { href: "#pricing", label: "Pricing" },
    { href: "#faq", label: "FAQ" },
  ];
  const navigate = useNavigate();

  return (
    <>
      <ScrollProgress />
      <GlassContainer
        as="header"
        className={`fixed w-full z-50 transition-all duration-300 ${
          isScrolled
            ? "py-2 backdrop-blur-md bg-dark-900/80 border-b border-blue-400/20"
            : "py-4 backdrop-blur-sm bg-dark-900/60"
        }`}
      >
        <div className="container mx-auto flex items-center justify-between px-4">
          <Logo />

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                className={`text-white hover:text-blue-300 transition-all duration-300 relative ${
                  activeLink === link.href.substring(1) ? "text-blue-300" : ""
                }`}
              >
                {link.label}
                {activeLink === link.href.substring(1) && (
                  <span className="absolute -bottom-1 left-0 h-0.5 w-full bg-blue-400"></span>
                )}
              </a>
            ))}
          </nav>

          <div className="hidden md:block">
            <Button intent="primary" onClick={() => navigate("/login")}>
              Start Your 3D Journey
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-white p-2 rounded-md hover:bg-white/5 transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        <div
          className={`md:hidden absolute top-full left-0 w-full bg-[#12121A] bg-opacity-95 backdrop-blur-md transition-all duration-300 transform origin-top ${
            isMobileMenuOpen ? "scale-y-100 opacity-100" : "scale-y-0 opacity-0"
          }`}
        >
          <nav className="container mx-auto py-4 px-4 flex flex-col space-y-4">
            {navLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                className="text-white py-2 hover:text-blue-400 transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </a>
            ))}

            <Button
              intent="primary"
              className="w-full mt-4"
              onClick={() => navigate("/login")}
            >
              Start Your 3D Journey
            </Button>
          </nav>
        </div>
      </GlassContainer>
    </>
  );
};
