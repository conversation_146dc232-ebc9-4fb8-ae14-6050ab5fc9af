import React, { useState, useEffect, useCallback } from 'react';
import type { ViewerSettings } from '../types/viewer';

interface LoadingState {
  loading: boolean;
  progress: number;
}

interface ModelViewerProps {
  url: string;
  title: string;
  modelId: string;
  className?: string;
  poster?: string;
  settings?: ViewerSettings;
  source?: 'library' | 'detail' | 'embed' | 'ar';
  onLoad?: () => void;
  onError?: (error: string) => void;
}

const defaultSettings: ViewerSettings = {
  autoRotate: false,
  ar: true,
  fullscreen: true,
  zoom: true,
  background: {
    type: 'color',
    value: '#1A1C24',
    opacity: 1
  },
  controls: {
    position: 'bottom',
    layout: 'default',
    visible: true
  },
  lighting: {
    environment: 'neutral',
    intensity: 1,
    shadows: true
  }
};

const ModelViewer: React.FC<ModelViewerProps> = ({ 
  url, 
  title, 
  modelId,
  className = '', 
  poster,
  settings = defaultSettings,
  source = 'detail',
  onLoad,
  onError 
}) => {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    loading: true,
    progress: 0
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoadingState({ loading: true, progress: 0 });
    setError(null);
  }, [url]);

  const handleError = useCallback((event: any) => {
    const errorMsg = event?.detail?.sourceError?.message || 
                    (url ? 'Failed to load 3D model' : 'No model URL provided');
    setError(errorMsg);
    setLoadingState({ loading: false, progress: 0 });
    onError?.(errorMsg);
  }, [onError, url]);

  const handleLoad = useCallback(() => {
    setLoadingState({ loading: false, progress: 100 });
    setError(null);
    onLoad?.();
  }, [onLoad]);

  const handleProgress = useCallback((event: any) => {
    if (!event.detail) return;
    
    const progress = Math.round(event.detail.totalProgress * 100);
    
    // Only update if progress has changed
    setLoadingState({
      loading: progress < 100,
      progress
    });
  }, []);

  useEffect(() => {
    const viewer = document.querySelector('model-viewer');
    if (!viewer) return;

    // Add event listeners
    viewer.addEventListener('progress', handleProgress);
    viewer.addEventListener('error', handleError);
    viewer.addEventListener('load', handleLoad);

    return () => {
      // Clean up event listeners
      viewer.removeEventListener('progress', handleProgress);
      viewer.removeEventListener('error', handleError);
      viewer.removeEventListener('load', handleLoad);
    };
  }, [handleProgress, handleError, handleLoad]);

  return (
    <div className={`relative bg-dark-200/50 backdrop-blur-sm rounded-lg overflow-hidden ${className}`}>
      <div className="absolute inset-0 bg-gradient-to-b from-dark-300/20 to-transparent pointer-events-none" />
      {loadingState.loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-dark-300/50 backdrop-blur-sm">
          <div className="flex flex-col items-center gap-2">
            <div className="w-32 h-1 bg-dark-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-brand-300 transition-all duration-300 ease-out"
                style={{ width: `${loadingState.progress}%` }}
              />
            </div>
            <span className="text-sm text-gray-400">
              Loading {Math.round(loadingState.progress)}%
            </span>
          </div>
        </div>
      )}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-dark-300/50 backdrop-blur-sm">
          <div className="text-red-400 text-sm">{error}</div>
        </div>
      )}
      <model-viewer
        data-model-id={modelId}
        src={url}
        poster={poster}
        loading="eager"
        reveal="auto"
        camera-controls
        auto-rotate={settings.autoRotate}
        disable-zoom={!settings.zoom}
        ar={settings.ar}
        ar-modes="webxr scene-viewer quick-look"
        ar-scale="auto"
        alt={title}
        shadow-intensity={settings.lighting.intensity}
        shadow-softness={settings.lighting.shadows ? "1" : "0"}
        environment-image={settings.lighting.environment}
        exposure={settings.lighting.intensity}
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: settings.background.type === 'color' ? settings.background.value : 'transparent',
          '--poster-color': settings.background.type === 'color' ? settings.background.value : 'transparent'
        } as React.CSSProperties}
        camera-orbit="0deg 75deg 100%"
        camera-target="0m 0m 0m"
        min-camera-orbit="-Infinity 0deg auto"
        max-camera-orbit="Infinity 180deg auto"
        interaction-prompt="none"
        field-of-view="45deg"
        interpolation-decay="50"
        orbit-sensitivity="0.8"
        bounds="tight"
        min-field-of-view="10deg"
        max-field-of-view="90deg"
        interaction-policy="allow-when-focused"
        className="w-full h-full rounded-lg"
      ></model-viewer>
    </div>
  );
};

export default ModelViewer;