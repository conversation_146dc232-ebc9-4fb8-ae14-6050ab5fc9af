const { string } = require("joi");
const mongoose = require("mongoose");

const glbModelSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    url: {
      type: String,
      // required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    multiUrls: [
      {
        type: String,
        match:
          /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
      },
    ],
    imageUrl: {
      type: String,
      // required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    price: {
      type: Number,
      required: true,
    },
    sku: {
      type: String,
      // required: true,
    },
    sizes: [String],
    width: [String],
    published: {
      type: Boolean,
      default: false,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: "Project",
    },
    category: {
      type: mongoose.Types.ObjectId,
      ref: "Category",
    },
    collectionId: {
      type: mongoose.Types.ObjectId,
      ref: "Collection",
    },
    asset_library: {
      type: mongoose.Types.ObjectId,
      ref: "AssetLibrary",
    },

    hdr: {
      url: {
        type: String,
        match:
          /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
      },
      level: {
        type: Number,
      },
    },
    customizations: [
      {
        name: {
          type: String,
          required: true,
        },
        // materialName: {
        //   type: String,
        // },
        values: [
          {
            name: {
              type: String,
              required: true,
            },
            icon_id: {
              type: mongoose.Types.ObjectId,
              ref: "MediaModel",
            }, //switch material details to array of materials id
            materialDetails: {
              type: [mongoose.Types.ObjectId],
              ref: "Materials",
            },
          },
        ],
      },
    ],
    variants: [
      {
        title: {
          type: String, // "BLACK/WHITE"
        }, //switch materials to array of materials id(table)
        materials: {
          type: [mongoose.Types.ObjectId],
          ref: "Materials",
        },
        //Add a reference to return the variants name with the material properties
        options: [String],
        price: {
          type: Number,
        },
        sku: {
          type: String,
        },
      },
    ],
    key: {
      type: String,
      unique: true,
      sparse: true,
    },
    usdzUrl: {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    integrationKey: {
      type: String,
      index: true,
    },
    productOptions: [
      {
        shopifyVariantId: String,
        url: String,
      },
    ],
  },
  { timestamps: true, collection: "GlbModel" }
);

const glbModelModel = mongoose.model("GlbModel", glbModelSchema, "GlbModel");
module.exports = glbModelModel;
