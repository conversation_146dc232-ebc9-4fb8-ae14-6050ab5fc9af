const mongoose = require("mongoose");

const modelCustomisationSchema = new mongoose.Schema(
  {
    model: {
      type: mongoose.Types.ObjectId,
      ref: "GlbModel",
    },
    meshes_name: [String],
    position: {
      type: String,
      required: true,
    },
    rotation: {
      type: String,
      required: true,
    },
    scaling: {
      type: String,
      required: true,
    },
  },
  { timestamps: true, collection: "modelCustomisations" },
);

const modelCustomisationModel = mongoose.model(
  "ModelCustomisation",
  modelCustomisationSchema,
);
module.exports = modelCustomisationModel;
