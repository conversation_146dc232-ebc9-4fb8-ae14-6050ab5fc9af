const router = require("express").Router();
const controller = require("../../controllers/collectionController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

// Create a new collection
router.post(
  "/create-collection",
  asyncError<PERSON><PERSON><PERSON>(controller.createCollection)
);

// Get a specific collection with populated products
router.get("/get-collection/:id", asyncError<PERSON>and<PERSON>(controller.getCollection));

// Get collection by ID (without populated products)
router.get("/get-by-id/:id", asyncError<PERSON>and<PERSON>(controller.getCollectionById));

// Get all collections for the current user
router.get("/get-collections", asyncError<PERSON>and<PERSON>(controller.getCollections)); // Changed from getAllCollectionsByUser to match controller method

// Add products to a collection (JSON payload)
router.post(
  "/add-products",
  asyncErrorHandler(controller.addProductsToCollection)
);

// Add models to a collection (FormData with files)
router.post("/add-models", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.addModelsToCollection)); // Added missing route for handling file uploads

//Remove Product From Collection
router.post(
  "/remove-product",
  asyncErrorHandler(controller.removeModelFromCollection)
);

//Delete Collection
router.delete(
  "/delete-collection/:id",
  asyncErrorHandler(controller.deleteCollection)
);
module.exports = router;
