const router = require("express").Router();
const userController = require("../../controllers/userController");
const assetController = require("../../controllers/assetController");
const adminController = require("../../controllers/adminController");
const verifyRoles = require("../../middleware/verifyRole");
const { admin, designer, client } = require("../../constants");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.get(
  "/get-all-users",
  verifyRoles([admin]),
  async<PERSON>rror<PERSON>and<PERSON>(userController.getAllUsers)
);
router.get("/get-all-assets", asyncErrorHandler(assetController.getAllAssets));
router.get(
  "/get-all-clients",
  verifyRoles([admin]),
  async<PERSON>rror<PERSON><PERSON><PERSON>(adminController.getAllClients)
);
router.get(
  "/get-all-designers",
  verifyRoles([admin]),
  async<PERSON>rror<PERSON><PERSON><PERSON>(adminController.getAllDesigners)
);
module.exports = router;
