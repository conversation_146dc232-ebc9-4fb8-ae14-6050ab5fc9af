const router = require("express").Router();
const verifyJWT = require("../../middleware/verifyJWT");

router.get("/main", (req, res) => {
  res.send("Connected to main route");
});

// Shopify webhook routes - no JWT verification needed as these are called by Shopify
router.use("/webhooks", require("./shopifyWebhookRoutes"));

router.use("/materialshowroom", require("./showroomMaterialRoutes"));
router.use("/auth", require("./authRoutes"));
router.use("/showroomcategory", require("./showroomCategoryRoutes"));
router.use("/otp", require("./verifyOtpRoutes"));
router.use("/user", require("./userRoutes"));
router.use("/role", require("./roleRoutes"));
router.use("/project", verifyJWT, require("./projectRoutes"));
router.use("/asset", require("./assetRoutes"));
router.use("/template", verifyJWT, require("./templateRoutes"));
router.use("/hdr", require("./hdrRoutes"));
router.use("/collection", verifyJWT, require("./collectionRoutes"));
router.use("/production", verifyJWT, require("./productionRoutes"));
router.use("/category", require("./categoryRoutes"));
router.use("/customizedshowroom", require("./customizedShowroomRoutes"));
router.use("/glbmodel", require("./glbModelRoutes"));
router.use(
  "/customizedMaterial",
  verifyJWT,
  require("./customizedMaterialRoutes")
);
router.use(
  "/modelCustomization",
  verifyJWT,
  require("./modelCustomizationRoutes")
);
router.use("/texture", verifyJWT, require("./textureRoutes"));
router.use("/comment", verifyJWT, require("./commentRoutes"));
router.use("/media", verifyJWT, require("./mediaRoutes"));
router.use("/materials", verifyJWT, require("./materialRoutes"));
router.use("/publish", require("./publishRoutes"));
router.use("/materiallibrary", verifyJWT, require("./materialLibraryRoutes"));
router.use("/admin", verifyJWT, require("./adminRoutes"));
router.use("/showroom", require("./showroomRoutes"));
router.use("/showroomtexture", require("./showroomTextureRoutes"));

router.use("/stripe", verifyJWT, require("./stripeRoutes"));
router.use("/subscription", require("./subscriptionRoutes"));
const bigCommerceRoutes = require("./bigCommerceRoutes");
router.use("/bigCommerce", verifyJWT, bigCommerceRoutes);
const magentoRoutes = require("./magentoRoutes");
router.use("/magento", verifyJWT, magentoRoutes);
const prestaShopRoutes = require("./prestaShopRoutes");
router.use("/prestaShop", verifyJWT, prestaShopRoutes);
const shopifyRoutes = require("./shopifyRoutes");
router.use("/shopify", shopifyRoutes);
const webflowRoutes = require("./webflowRoutes");
router.use("/webflow", verifyJWT, webflowRoutes);
const wooCommerceRoutes = require("./wooCommerceRoutes");
router.use("/woocommerce", verifyJWT, wooCommerceRoutes);

module.exports = router;
