const showroomCategoryModel = require("../models/showroomCategoriesModel.js");
const showroomMaterialModel = require("../models/showroomMaterialsModel.js");
const showroomMatedrialModel = require("../models/showroomMaterialsModel.js");
const {
  createShowroomRelated,
  getCategories,
} = require("../services/showroomService.js");

class Controller {
  createShowroomMaterial = async (req, res) => {
    const bodyData = req.body;
    const files = req.files;
    let inputs = {
      name: null,
      albedoColor: {
        r: null,
        g: null,
        b: null,
      },
      metallic: null,
      roughness: null,
      alpha: null,
      transparencyMode: null,
      albedoTextureLink: "albedoTextureLink",
      category: null,
    };
    const bucket = `showroom`;
    const folder = `Materials`;

    const newShowroomMaterial = await createShowroomRelated(
      bodyData,
      files,
      inputs,
      showroomMaterialModel,
      folder,
      bucket,
    );
    return res.status(200).json({ data: newShowroomMaterial });
  };
  getAllShowroomMaterials = async (req, res) => {
    const materialsShowroom = await showroomMatedrialModel.find();
    return res.status(200).json({
      data: materialsShowroom,
    });
  };
  getById = async (req, res) => {
    const { id } = req.params;
    const material = await showroomMatedrialModel.findById(id);
    if (!material) {
      return res.status(404).json("Material Not Found");
    }
    return res.status(200).json({ data: material });
  };
  updateById = async (req, res) => {
    const { id } = req.params;
    const material = await showroomMatedrialModel.findByIdAndUpdate(
      id,
      { ...req.body },
      { new: true },
    );
    if (!material) {
      return res.status(404).json("Material Not Found");
    }
    return res.status(200).json({ data: material });
  };
  getCategoriesOfMaterials = async (req, res) => {
    const categories = await getCategories(showroomMatedrialModel);
    return res.json({ data: categories });
  };
  deleteById = async (req, res) => {
    const { id } = req.params;
    const showroomMaterial = await showroomMatedrialModel.findByIdAndDelete(id);
    if (!showroomMaterial) {
      return res.status(404).json("Material Not Found");
    }
    return res.status(200).json("Material Deleted");
  };
}
const controller = new Controller();
module.exports = controller;
