import React, { useState } from "react";
import {
  Link2,
  <PERSON><PERSON>,
  Box,
  Smartphone,
  Maximize2,
  Rotate3d,
  AlertCircle,
  Image,
} from "lucide-react";
import { Model3D } from "../../types/models";
import type {
  ViewerSettings,
  GenerateLinkParams,
  BackgroundConfig,
  ControlPanelConfig,
} from "../../types/viewer";
import { validateAuthState } from "../../utils/debug";
import { api } from "../../services/api";

interface PublishTabProps {
  model: Model3D;
}

const PublishTab: React.FC<PublishTabProps> = ({ model }) => {
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedUrl, setGeneratedUrl] = useState<string | null>(null);
  const [viewerSettings, setViewerSettings] = useState<ViewerSettings>({
    autoRotate: true,
    ar: true,
    fullscreen: true,
    zoom: true,
  });

  const [backgroundConfig, setBackgroundConfig] = useState<BackgroundConfig>({
    type: "color",
    value: "#FFFFFF",
    opacity: 1.0,
  });

  const [controlPanelConfig, setControlPanelConfig] =
    useState<ControlPanelConfig>({
      position: "bottom-right",
      alignment: "right",
      visible: true,
      layout: "default",
    });

  const handleGenerateLink = async () => {
    try {
      setError(null);

      // Validate auth state
      const authState = validateAuthState();
      if (!authState.isValid) {
        throw new Error(
          `Authentication issues: ${authState.issues.join(", ")}`
        );
      }

      setGenerating(true);

      // Check required fields with more lenient validation

      if (!model._id) {
        throw new Error("Required field missing: _id");
      }

      if (!model.url && (!model.multiUrls || model.multiUrls.length === 0)) {
        throw new Error("Required field missing: URL or multiUrls");
      }

      // Initialize multiUrls as empty array if it's not defined
      const multiUrls = Array.isArray(model.multiUrls) ? model.multiUrls : [];

      // Prepare a fallback hashedId if key is missing
      const hashedId =
        model.key || model.id || model._id || "generated-id-" + Date.now();

      // Transform background for backend compatibility
      let background;
      if (backgroundConfig.type === "transparent") {
        background = {
          color: { red: 0, green: 0, blue: 0, alpha: 0 },
          imageLink: "",
          isTransparent: true,
        };
      } else if (backgroundConfig.type === "color") {
        // Convert hex to RGBA
        const hex = backgroundConfig.value.replace("#", "");
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        const a = backgroundConfig.opacity || 1.0;

        background = {
          color: { red: r, green: g, blue: b, alpha: a },
          imageLink: "",
          isTransparent: false,
        };
      } else {
        background = {
          color: { red: 0, green: 0, blue: 0, alpha: 1 },
          imageLink: backgroundConfig.value,
          isTransparent: false,
        };
      }

      // Transform control panel for backend compatibility
      // Map frontend position values to backend enum values
      const positionMap = {
        "top-left": "Top Left",
        "top-right": "Top Right",
        "bottom-left": "Bottom Left",
        "bottom-right": "Bottom Right",
      };

      const controlPanel = {
        orientation: "Horizontal",
        position: positionMap[controlPanelConfig.position],
      };

      // Prepare parameters for the viewer
      const params: GenerateLinkParams = {
        glb: model._id,
        glbUrls: multiUrls.length ? multiUrls : [model.url],
        autoRotate: viewerSettings.autoRotate,
        AR: viewerSettings.ar,
        fullScreen: viewerSettings.fullscreen,
        zoom: viewerSettings.zoom,
        background, // Using the transformed background
        hashedId, // Using fallback logic
        icon360: true,
        controlPanel,
      };

      const response = await api.generateLink(params);

      // Handle case where the response is a direct URL string
      if (typeof response === "string" && response.startsWith("http")) {
        const viewerUrl = response;
        setGeneratedUrl(viewerUrl);

        // Open the URL in a new tab
        window.open(viewerUrl, "_blank", "noopener,noreferrer");
        return;
      }

      // Handle JSON response format
      if (response.data?.url) {
        const viewerUrl = response.data.url;
        setGeneratedUrl(viewerUrl);

        // Open the URL in a new tab
        window.open(viewerUrl, "_blank", "noopener,noreferrer");
        return;
      }

      // If we reached here, there was a problem
      if (!response.success) {
        throw new Error(
          response.error || "Failed to generate link. Please try again."
        );
      }

      // If success is true but no URL in response
      throw new Error("No viewer URL received from server");
    } catch (err) {
      setGeneratedUrl(null);
      setError(err instanceof Error ? err.message : "Failed to generate link");
    } finally {
      setGenerating(false);
    }
  };

  const handleCopyEmbed = async () => {
    const embedCode = `<iframe src="${
      generatedUrl || ""
    }" width="100%" height="600" frameborder="0" allow="autoplay; fullscreen; xr-spatial-tracking" allowfullscreen title="${
      model.name || "Model Viewer"
    }"></iframe>`;
    await navigator.clipboard.writeText(embedCode);
  };

  return (
    <div className="card rounded-xl p-6">
      <div className="space-y-6">
        {error && (
          <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2">
            <AlertCircle size={18} strokeWidth={1.5} />
            <span>{error}</span>
          </div>
        )}

        <div className="flex items-center justify-between">
          <h3 className="text-lg font-light text-gray-200">
            Viewer Configuration
          </h3>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-sm font-light text-gray-400">Features</h4>
            <div className="space-y-3">
              <label className="flex items-center justify-between group">
                <div className="flex items-center gap-2 text-gray-300">
                  <Rotate3d size={18} strokeWidth={1.5} />
                  <span>Auto Rotate</span>
                </div>
                <div className="relative inline-block w-10 align-middle select-none">
                  <input
                    type="checkbox"
                    checked={viewerSettings.autoRotate}
                    onChange={(e) =>
                      setViewerSettings((prev) => ({
                        ...prev,
                        autoRotate: e.target.checked,
                      }))
                    }
                    className="toggle-checkbox"
                    id="autoRotate"
                  />
                  <label className="toggle-label" htmlFor="autoRotate" />
                </div>
              </label>

              <label className="flex items-center justify-between group">
                <div className="flex items-center gap-2 text-gray-300">
                  <Box size={18} strokeWidth={1.5} />
                  <span>AR Mode</span>
                </div>
                <div className="relative inline-block w-10 align-middle select-none">
                  <input
                    type="checkbox"
                    checked={viewerSettings.ar}
                    onChange={(e) =>
                      setViewerSettings((prev) => ({
                        ...prev,
                        ar: e.target.checked,
                      }))
                    }
                    className="toggle-checkbox"
                    id="arMode"
                  />
                  <label className="toggle-label" htmlFor="arMode" />
                </div>
              </label>

              <label className="flex items-center justify-between group">
                <div className="flex items-center gap-2 text-gray-300">
                  <Maximize2 size={18} strokeWidth={1.5} />
                  <span>Fullscreen</span>
                </div>
                <div className="relative inline-block w-10 align-middle select-none">
                  <input
                    type="checkbox"
                    checked={viewerSettings.fullscreen}
                    onChange={(e) =>
                      setViewerSettings((prev) => ({
                        ...prev,
                        fullscreen: e.target.checked,
                      }))
                    }
                    className="toggle-checkbox"
                    id="fullscreen"
                  />
                  <label className="toggle-label" htmlFor="fullscreen" />
                </div>
              </label>

              <label className="flex items-center justify-between group">
                <div className="flex items-center gap-2 text-gray-300">
                  <Smartphone size={18} strokeWidth={1.5} />
                  <span>Mobile Controls</span>
                </div>
                <div className="relative inline-block w-10 align-middle select-none">
                  <input
                    type="checkbox"
                    checked={viewerSettings.zoom}
                    onChange={(e) =>
                      setViewerSettings((prev) => ({
                        ...prev,
                        zoom: e.target.checked,
                      }))
                    }
                    className="toggle-checkbox"
                    id="mobileControls"
                  />
                  <label className="toggle-label" htmlFor="mobileControls" />
                </div>
              </label>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-sm font-light text-gray-400">Background</h4>
            <div className="space-y-3">
              <div className="flex flex-col gap-2">
                <label htmlFor="bgType" className="text-sm text-gray-300">
                  Type
                </label>
                <select
                  id="bgType"
                  value={backgroundConfig.type}
                  onChange={(e) =>
                    setBackgroundConfig((prev) => ({
                      ...prev,
                      type: e.target.value as
                        | "color"
                        | "environment"
                        | "transparent",
                    }))
                  }
                  className="bg-dark-500 border border-gray-700 rounded px-3 py-2 text-gray-200"
                >
                  <option value="color">Color</option>
                  <option value="environment">Environment Image</option>
                  <option value="transparent">Transparent</option>
                </select>
              </div>

              {backgroundConfig.type === "color" && (
                <div className="flex flex-col gap-2">
                  <label htmlFor="bgColor" className="text-sm text-gray-300">
                    Color
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="color"
                      id="bgColor"
                      value={backgroundConfig.value}
                      onChange={(e) =>
                        setBackgroundConfig((prev) => ({
                          ...prev,
                          value: e.target.value,
                        }))
                      }
                      className="bg-dark-500 border border-gray-700 rounded h-10 w-10"
                    />
                    <input
                      type="text"
                      value={backgroundConfig.value}
                      onChange={(e) =>
                        setBackgroundConfig((prev) => ({
                          ...prev,
                          value: e.target.value,
                        }))
                      }
                      className="bg-dark-500 border border-gray-700 rounded px-3 py-2 flex-1 text-gray-200"
                    />
                  </div>
                </div>
              )}

              {backgroundConfig.type === "environment" && (
                <div className="flex flex-col gap-2">
                  <label htmlFor="bgImage" className="text-sm text-gray-300">
                    Image URL
                  </label>
                  <div className="flex gap-2 items-center">
                    <Image
                      size={18}
                      strokeWidth={1.5}
                      className="text-gray-400"
                    />
                    <input
                      type="text"
                      id="bgImage"
                      value={backgroundConfig.value}
                      onChange={(e) =>
                        setBackgroundConfig((prev) => ({
                          ...prev,
                          value: e.target.value,
                        }))
                      }
                      className="bg-dark-500 border border-gray-700 rounded px-3 py-2 flex-1 text-gray-200"
                      placeholder="https://example.com/image.jpg"
                    />
                  </div>
                </div>
              )}

              {(backgroundConfig.type === "color" ||
                backgroundConfig.type === "environment") && (
                <div className="flex flex-col gap-2">
                  <label htmlFor="bgOpacity" className="text-sm text-gray-300">
                    Opacity: {Math.round((backgroundConfig.opacity || 1) * 100)}
                    %
                  </label>
                  <input
                    type="range"
                    id="bgOpacity"
                    min="0"
                    max="1"
                    step="0.01"
                    value={backgroundConfig.opacity || 1}
                    onChange={(e) =>
                      setBackgroundConfig((prev) => ({
                        ...prev,
                        opacity: parseFloat(e.target.value),
                      }))
                    }
                    className="w-full"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-6">
          <h4 className="text-sm font-light text-gray-400 mb-4">
            Control Panel
          </h4>
          <div className="grid grid-cols-2 gap-6">
            <div className="flex flex-col gap-3">
              <div className="flex flex-col gap-2">
                <label htmlFor="ctrlPosition" className="text-sm text-gray-300">
                  Position
                </label>
                <select
                  id="ctrlPosition"
                  value={controlPanelConfig.position}
                  onChange={(e) =>
                    setControlPanelConfig((prev) => ({
                      ...prev,
                      position: e.target.value as
                        | "top-left"
                        | "top-right"
                        | "bottom-left"
                        | "bottom-right",
                    }))
                  }
                  className="bg-dark-500 border border-gray-700 rounded px-3 py-2 text-gray-200"
                >
                  <option value="top-left">Top Left</option>
                  <option value="top-right">Top Right</option>
                  <option value="bottom-left">Bottom Left</option>
                  <option value="bottom-right">Bottom Right</option>
                </select>
              </div>

              <div className="flex flex-col gap-2">
                <label
                  htmlFor="ctrlAlignment"
                  className="text-sm text-gray-300"
                >
                  Alignment
                </label>
                <select
                  id="ctrlAlignment"
                  value={controlPanelConfig.alignment}
                  onChange={(e) =>
                    setControlPanelConfig((prev) => ({
                      ...prev,
                      alignment: e.target.value as "left" | "center" | "right",
                    }))
                  }
                  className="bg-dark-500 border border-gray-700 rounded px-3 py-2 text-gray-200"
                >
                  <option value="left">Left</option>
                  <option value="center">Center</option>
                  <option value="right">Right</option>
                </select>
              </div>
            </div>

            <div className="flex flex-col gap-3">
              <div className="flex flex-col gap-2">
                <label htmlFor="ctrlLayout" className="text-sm text-gray-300">
                  Layout
                </label>
                <select
                  id="ctrlLayout"
                  value={controlPanelConfig.layout}
                  onChange={(e) =>
                    setControlPanelConfig((prev) => ({
                      ...prev,
                      layout: e.target.value as
                        | "default"
                        | "minimal"
                        | "custom",
                    }))
                  }
                  className="bg-dark-500 border border-gray-700 rounded px-3 py-2 text-gray-200"
                >
                  <option value="default">Default</option>
                  <option value="minimal">Minimal</option>
                  <option value="custom">Custom</option>
                </select>
              </div>

              <label className="flex items-center justify-between group mt-3">
                <div className="flex items-center gap-2 text-gray-300">
                  <span>Show Controls</span>
                </div>
                <div className="relative inline-block w-10 align-middle select-none">
                  <input
                    type="checkbox"
                    checked={controlPanelConfig.visible}
                    onChange={(e) =>
                      setControlPanelConfig((prev) => ({
                        ...prev,
                        visible: e.target.checked,
                      }))
                    }
                    className="toggle-checkbox"
                    id="ctrlVisible"
                  />
                  <label className="toggle-label" htmlFor="ctrlVisible" />
                </div>
              </label>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            onClick={handleGenerateLink}
            disabled={generating}
            className="btn btn-primary flex items-center gap-2"
          >
            {generating ? (
              <span className="inline-block w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
            ) : (
              <>
                <Link2 size={18} strokeWidth={1.5} />
                <span>Generate Link</span>
              </>
            )}
          </button>
        </div>

        <div className="border-t border-gray-800 pt-6">
          <h4 className="text-sm font-light text-gray-400 mb-4">Embed Code</h4>
          <div className="bg-dark-400 rounded-lg p-4">
            <code className="text-sm text-gray-300 font-mono break-all select-all">
              {generatedUrl
                ? `<iframe src="${generatedUrl}" width="100%" height="600" frameborder="0" allow="autoplay; fullscreen; xr-spatial-tracking" allowfullscreen></iframe>`
                : "Generate a link first to see embed code here"}
            </code>
            <button
              onClick={handleCopyEmbed}
              disabled={!generatedUrl}
              className={`mt-4 btn ${
                !generatedUrl ? "btn-disabled" : "btn-secondary"
              } flex items-center gap-2`}
            >
              <Copy size={16} strokeWidth={1.5} />
              <span>Copy Code</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublishTab;
