const router = require("express").Router();
const controller = require("../../controllers/hdrController");
const { validateHdrSchema } = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");
const { admin, designer } = require("../../constants");
const { upload } = require("../../middleware/multer-middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.post(
  "/create-hdr",
  upload.fields([
    {
      name: "url",
      maxCount: 1,
    },
    { name: "icon", maxCount: 1 },
  ]),
  asyncErrorHandler(controller.createHdr)
);

router.route("/create-hdr").post(
  // validateHdrSchema,
  // verifyRoles([admin, designer]),
  controller.createHdr
);
router.get("/get-all-hdrs", controller.getAllHdr);
router.get("/get-by-id/:id", controller.getHdrById);
router.put("/update-by-id/:id", controller.updateById);
router.get("/get-hdr-with-asset/:id", controller.getHdrWithAsset);
router.delete(
  "/delete-hdr/:id",
  // verifyRoles([admin, designer]),
  controller.deleteHdrById
);

module.exports = router;
