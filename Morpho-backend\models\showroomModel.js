const mongoose = require("mongoose");

const showroomSchema = new mongoose.Schema({
  url: {
    type: String,
    required: [true, "Url Required"],
    match:
      /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
  },
  name: {
    type: String,
  },
  debugUrl: {
    type: String,
    required: [true, "Debug Url Required"],
    match:
      /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
  },
  sku: {
    type: Number,
    required: [true, "Sku Required"],
    unique: [true, "Sku Must Be Unique"],
    index: true,
  },
  position: {
    x: Number,
    y: Number,
    z: Number,
  },
  rotation: {
    x: Number,
    y: Number,
    z: Number,
  },
  scaling: {
    x: Number,
    y: Number,
    z: Number,
  },
  materialColorChanges: [
    {
      name: String,
      color: {
        r: Number,
        g: Number,
        b: Number,
      },
    },
  ],
  imagesUrl: {
    type: [String],
    match:
      /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
  },
  cameraValues: {
    alpha: Number,
    beta: Number,
    radius: Number,
    lowerRadius: { type: Number, default: 0.0 },
    upperRadius: { type: Number, default: 0.0 },
  }, //for the default
  imagePosition: {
    x: Number,
    y: Number,
    z: Number,
  },
  iconImageUrl: {
    type: String,
  },
  categories: [
    {
      type: mongoose.Types.ObjectId,
      ref: "ShowroomCategory",
    },
  ],
  tags: [String],
  glow: {
    has: { type: Boolean },
    kernel: Number,
    intensity: Number,
  },
});

// showroomSchema.pre("save", function (next) {
//   if (this.cameraValues && this.cameraValues.radius != null) {
//     this.cameraValues.lowerRadius = Number(
//       (this.cameraValues.radius / 2).toFixed(2),
//     ); // Convert to double
//     this.cameraValues.upperRadius = Number(this.cameraValues.radius.toFixed(2)); // Convert to double
//   }
//   next();
// });

// showroomSchema.pre("findOneAndUpdate", function (next) {
//   const update = this.getUpdate();

//   if (update && update.cameraValues && update.cameraValues.radius != null) {
//     const radius = Number(update.cameraValues.radius);
//     if (!isNaN(radius)) {
//       update.cameraValues.lowerRadius = Number((radius / 2).toFixed(2));
//       update.cameraValues.upperRadius = Number(radius.toFixed(2));
//     }
//   }

//   if (
//     update &&
//     update.$set &&
//     update.$set.cameraValues &&
//     update.$set.cameraValues.radius != null
//   ) {
//     const radius = Number(update.$set.cameraValues.radius);
//     if (!isNaN(radius)) {
//       update.$set.cameraValues.lowerRadius = Number((radius / 2).toFixed(2));
//       update.$set.cameraValues.upperRadius = Number(radius.toFixed(2));
//     }
//   }

//   this.setUpdate(update);
//   next();
// });

const showroomModel = mongoose.model("Showroom", showroomSchema);
module.exports = showroomModel;
