const router = require("express").Router();
const controller = require("../../controllers/productionController");
const { validateProductionSchema } = require("../../validation/middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
router
  .route("/create-production")
  .post(
    validateProductionSchema,
    asyncError<PERSON><PERSON><PERSON>(controller.createProduction)
  );
router.get(
  "/get-all-productions",
  asyncError<PERSON><PERSON><PERSON>(controller.getAllProductions)
);
router.get(
  "/get-production-by-id",
  async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.getProductionById)
);
router.get(
  "/get-production-with-project",
  async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.getProductionWithProject)
);
router.delete(
  "/delete-production",
  asyncError<PERSON><PERSON><PERSON>(controller.deleteProductionById)
);

module.exports = router;
