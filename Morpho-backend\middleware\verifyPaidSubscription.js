const User = require("../models/User");

/**
 * Middleware to verify if a user has an active paid subscription
 * This middleware should be used after verifyJWT middleware
 * as it relies on req.user being set
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 * @returns {void}
 */
const verifyPaidSubscription = async (req, res, next) => {
  try {
    // Get user ID from the request (set by verifyJWT middleware)
    const userId = req.user;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized: Authentication required"
      });
    }
    
    // Find the user and check their subscription status and plan
    const user = await User.findById(userId).select("subscriptionStatus subscriptionPlan");
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }
    
    // Check if the user has an active paid subscription
    const hasActivePaidSubscription = 
      user.subscriptionStatus === "active" && 
      ["basic", "pro", "enterprise"].includes(user.subscriptionPlan);
    
    if (!hasActivePaidSubscription) {
      return res.status(403).json({
        success: false,
        message: "Premium feature: This feature requires a paid subscription",
        subscriptionStatus: user.subscriptionStatus,
        subscriptionPlan: user.subscriptionPlan,
        requiredPlans: ["basic", "pro", "enterprise"]
      });
    }
    
    // Add subscription info to the request for use in downstream middleware/controllers
    req.subscription = {
      status: user.subscriptionStatus,
      plan: user.subscriptionPlan,
      isPaidUser: true
    };
    
    // If the user has an active paid subscription, proceed to the next middleware
    next();
  } catch (error) {
    console.error("Error verifying paid subscription:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while verifying subscription status",
      error: error.message
    });
  }
};

module.exports = verifyPaidSubscription;
