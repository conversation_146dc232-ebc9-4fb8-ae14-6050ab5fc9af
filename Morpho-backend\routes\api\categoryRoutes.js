const router = require("express").Router();
const controller = require("../../controllers/categoryController");
const verifyJWT = require("../../middleware/verifyJWT");
const verifyRoles = require("../../middleware/verifyRole");
const { validateCategorySchema } = require("../../validation/middleware");
const { admin, designer } = require("../../constants");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router
  .route("/create-category")
  .post(
    validateCategorySchema,
    verifyJWT,
    asyncError<PERSON>andler(controller.createCategory)
  );

router.get(
  "/get-all-categories",
  verifyJWT,
  verifyRoles([admin]),
  asyncError<PERSON>andler(controller.getAllCategories)
);

router.get(
  "/get-category-by-id",
  verifyJWT,
  asyncErrorHandler(controller.getCategoryById)
);

router.get(
  "/get-category-with-production",
  verifyJWT,
  asyncError<PERSON>andler(controller.getCategoryWithProduction)
);
router.post(
  "/get-category-of-project",
  asyncErrorHandler(controller.getCategoriesOfProject)
);

router.delete(
  "/delete-category",
  verifyJWT,
  asyncErrorHandler(controller.deleteCategoryById)
);

module.exports = router;
