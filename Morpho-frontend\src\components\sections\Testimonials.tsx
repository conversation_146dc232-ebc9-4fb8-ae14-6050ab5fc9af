import React, { useRef, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { GlassCard } from '../ui/GlassCard';
import { useInView } from '../../hooks/useInView';

const testimonials = [
  {
    id: 1,
    quote: "We tried a general 3D model viewer before Morpho and it was a nightmare to integrate with our online store. Morpho's Shopify 3D product viewer took minutes to set up and our conversion rate jumped 41% in the first month.",
    author: "<PERSON>",
    title: "Founder, Urban Threads Apparel"
  },
  {
    id: 2,
    quote: "Other 3D product visualization tools we tried required a developer. With Morpho's 3D viewer, I set it up myself in less than an hour. Now our customers can see every detail of our products in 3D before purchasing.",
    author: "<PERSON>",
    title: "E-commerce Director, Precision Tools"
  },
  {
    id: 3,
    quote: "Product returns were our biggest problem. Since implementing Morpho's 3D product visualization in our Shopify store, we've seen a 27% decrease in returns because customers can view our products in 3D and know exactly what they're getting.",
    author: "<PERSON>",
    title: "CEO, Modern Home Goods"
  }
];

export const Testimonials: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  const [activeIndex, setActiveIndex] = useState(0);

  const handlePrev = () => {
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
  };

  return (
    <section ref={sectionRef} id="testimonials" className="py-24 relative">
      {/* Background glow elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/2 left-1/4 w-1/3 h-1/3 bg-[#26D9C2]/10 rounded-full filter blur-[100px] opacity-60"></div>
        <div className="absolute bottom-0 right-1/3 w-1/4 h-1/4 bg-blue-500/10 rounded-full filter blur-[80px] opacity-70 animate-pulse-slow"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className={`text-3xl md:text-4xl font-bold text-white mb-6 transition-all duration-700 ${
            isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            Shopify Merchants Boosting Sales with Our 3D Product Viewer
          </h2>
        </div>

        <div className={`max-w-4xl mx-auto transition-all duration-700 delay-300 ${
          isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="relative">
            <div className="overflow-hidden">
              <div 
                className="flex transition-transform duration-500 ease-in-out" 
                style={{ transform: `translateX(-${activeIndex * 100}%)` }}
              >
                {testimonials.map((testimonial, index) => (
                  <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                    <div className="relative group">
                      {/* Enhanced glow effect on hover */}
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                      
                      {/* Main testimonial card */}
                      <GlassCard className="h-full border border-blue-500/20 group-hover:border-blue-500/40 transition-all duration-300">
                        <div className="flex flex-col h-full">
                          {/* Quote icon with gradient */}
                          <div className="mb-6 relative">
                            <div className="absolute -top-2 -left-2 w-12 h-12 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-md"></div>
                            <svg className="w-10 h-10 text-blue-400 relative z-10" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                            </svg>
                          </div>
                          
                          {/* Quote text with enhanced styling */}
                          <blockquote className="text-lg md:text-xl text-white font-light italic flex-grow mb-6 relative">
                            <span className="absolute -top-2 -left-2 text-blue-500/10 text-4xl">"</span>
                            {testimonial.quote}
                            <span className="absolute -bottom-2 -right-2 text-blue-500/10 text-4xl">"</span>
                          </blockquote>
                          
                          {/* Author info with enhanced styling */}
                          <div className="flex items-center mt-auto">
                            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500/30 to-purple-500/30 flex items-center justify-center flex-shrink-0 border border-blue-500/30">
                              <span className="text-blue-400 font-bold text-lg">
                                {testimonial.author.charAt(0)}
                              </span>
                            </div>
                            <div className="ml-4">
                              <p className="text-white font-semibold">{testimonial.author}</p>
                              <p className="text-gray-400 text-sm">{testimonial.title}</p>
                            </div>
                          </div>
                        </div>
                      </GlassCard>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex justify-center items-center mt-8 space-x-4">
              <button 
                onClick={handlePrev}
                className="w-10 h-10 rounded-full bg-[#12121A] border border-blue-500/30 flex items-center justify-center text-white hover:bg-blue-500/20 hover:border-blue-400 transition-colors"
                aria-label="Previous testimonial"
              >
                <ChevronLeft size={20} />
              </button>
              
              <div className="flex space-x-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveIndex(index)}
                    className={`w-2.5 h-2.5 rounded-full transition-colors relative ${
                      index === activeIndex ? 'bg-blue-400' : 'bg-gray-600 hover:bg-gray-500'
                    }`}
                    aria-label={`Go to testimonial ${index + 1}`}
                  >
                    {index === activeIndex && (
                      <span className="absolute inset-0 rounded-full animate-ping bg-blue-400 opacity-75"></span>
                    )}
                  </button>
                ))}
              </div>
              
              <button 
                onClick={handleNext}
                className="w-10 h-10 rounded-full bg-[#12121A] border border-blue-500/30 flex items-center justify-center text-white hover:bg-blue-500/20 hover:border-blue-400 transition-colors"
                aria-label="Next testimonial"
              >
                <ChevronRight size={20} />
              </button>
            </div>
          </div>
        </div>

        {/* Stats section with enhanced glow styling */}
        <div className={`mt-16 transition-all duration-700 delay-500 ${
          isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          {/* Enhanced glass container with multiple layers of glow effects */}
          <div className="relative group">
            {/* Outer glow layer */}
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/30 to-blue-500/20 rounded-xl blur-md opacity-70 animate-neon-pulse"></div>
            
            {/* Middle glow layer for intensity */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 via-blue-400/20 to-blue-500/20 rounded-xl blur-sm"></div>
            
            {/* Inner content with glass effect */}
            <div className="relative bg-[#12121A]/70 backdrop-blur-lg border border-blue-500/40 rounded-xl p-8 shadow-glow-md">
              {/* Corner decorative elements */}
              <div className="absolute top-0 left-0 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-transparent rounded-br-3xl"></div>
              <div className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-blue-500/10 to-transparent rounded-tl-3xl"></div>
              
              {/* Subtle particle effects */}
              <div className="absolute inset-0 overflow-hidden rounded-xl">
                <div className="absolute top-1/4 right-1/3 w-1.5 h-1.5 rounded-full bg-blue-400/70 animate-pulse"></div>
                <div className="absolute bottom-1/3 left-1/4 w-1 h-1 rounded-full bg-blue-400/70 animate-pulse-slow"></div>
                <div className="absolute top-1/2 right-1/4 w-2 h-2 rounded-full bg-blue-400/50 animate-pulse-slow"></div>
              </div>
              
              <div className="grid md:grid-cols-3 gap-8 relative z-10">
                {/* Stat item 1 */}
                <div className="text-center group-hover:transform group-hover:scale-105 transition-transform duration-500">
                  <div className="inline-block relative">
                    <div className="absolute inset-0 bg-blue-400/10 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="relative text-4xl md:text-5xl font-bold text-blue-400 mb-2 text-shadow-neon">41%</div>
                  </div>
                  <p className="text-white text-lg">Average conversion rate increase</p>
                </div>
                
                {/* Stat item 2 */}
                <div className="text-center group-hover:transform group-hover:scale-105 transition-transform duration-500">
                  <div className="inline-block relative">
                    <div className="absolute inset-0 bg-blue-400/10 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="relative text-4xl md:text-5xl font-bold text-blue-400 mb-2 text-shadow-neon">27%</div>
                  </div>
                  <p className="text-white text-lg">Reduction in product returns</p>
                </div>
                
                {/* Stat item 3 */}
                <div className="text-center group-hover:transform group-hover:scale-105 transition-transform duration-500">
                  <div className="inline-block relative">
                    <div className="absolute inset-0 bg-blue-400/10 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="relative text-4xl md:text-5xl font-bold text-blue-400 mb-2 text-shadow-neon">4x</div>
                  </div>
                  <p className="text-white text-lg">Increased time on product pages</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default { Testimonials };