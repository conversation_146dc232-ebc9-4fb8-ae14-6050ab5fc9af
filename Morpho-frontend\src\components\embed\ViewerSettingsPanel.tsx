import React from 'react';
import { ViewerSettings } from '../../types/viewer';
import { Palette, Maximize2, Rotate3d, Box, Smartphone, Sun } from 'lucide-react';

interface ViewerSettingsPanelProps {
  settings: ViewerSettings;
  onChange: (settings: ViewerSettings) => void;
}

const ViewerSettingsPanel: React.FC<ViewerSettingsPanelProps> = ({
  settings,
  onChange
}) => {
  const updateSettings = (key: keyof ViewerSettings, value: any) => {
    onChange({ ...settings, [key]: value });
  };

  return (
    <div className="space-y-6">
      {/* Features */}
      <div className="space-y-4">
        <h3 className="text-sm font-light text-gray-400">Features</h3>
        <div className="space-y-3">
          <label className="flex items-center justify-between group">
            <div className="flex items-center gap-2 text-gray-300">
              <Rotate3d size={18} strokeWidth={1.5} />
              <span>Auto Rotate</span>
            </div>
            <div className="relative inline-block w-10 align-middle select-none">
              <input
                type="checkbox"
                checked={settings.autoRotate}
                onChange={(e) => updateSettings('autoRotate', e.target.checked)}
                className="toggle-checkbox"
                id="autoRotate"
              />
              <label className="toggle-label" htmlFor="autoRotate" />
            </div>
          </label>

          <label className="flex items-center justify-between group">
            <div className="flex items-center gap-2 text-gray-300">
              <Box size={18} strokeWidth={1.5} />
              <span>AR Mode</span>
            </div>
            <div className="relative inline-block w-10 align-middle select-none">
              <input
                type="checkbox"
                checked={settings.ar}
                onChange={(e) => updateSettings('ar', e.target.checked)}
                className="toggle-checkbox"
                id="arMode"
              />
              <label className="toggle-label" htmlFor="arMode" />
            </div>
          </label>

          <label className="flex items-center justify-between group">
            <div className="flex items-center gap-2 text-gray-300">
              <Maximize2 size={18} strokeWidth={1.5} />
              <span>Fullscreen</span>
            </div>
            <div className="relative inline-block w-10 align-middle select-none">
              <input
                type="checkbox"
                checked={settings.fullscreen}
                onChange={(e) => updateSettings('fullscreen', e.target.checked)}
                className="toggle-checkbox"
                id="fullscreen"
              />
              <label className="toggle-label" htmlFor="fullscreen" />
            </div>
          </label>

          <label className="flex items-center justify-between group">
            <div className="flex items-center gap-2 text-gray-300">
              <Smartphone size={18} strokeWidth={1.5} />
              <span>Mobile Controls</span>
            </div>
            <div className="relative inline-block w-10 align-middle select-none">
              <input
                type="checkbox"
                checked={settings.zoom}
                onChange={(e) => updateSettings('zoom', e.target.checked)}
                className="toggle-checkbox"
                id="mobileControls"
              />
              <label className="toggle-label" htmlFor="mobileControls" />
            </div>
          </label>
        </div>
      </div>

      {/* Background */}
      <div className="space-y-4">
        <h3 className="text-sm font-light text-gray-400">Background</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-300 mb-2">Type</label>
            <select
              value={settings.background.type}
              onChange={(e) => updateSettings('background', {
                ...settings.background,
                type: e.target.value
              })}
              className="input"
            >
              <option value="color">Solid Color</option>
              <option value="environment">Environment</option>
              <option value="transparent">Transparent</option>
            </select>
          </div>

          {settings.background.type === 'color' && (
            <div>
              <label className="block text-sm text-gray-300 mb-2">Color</label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={settings.background.value}
                  onChange={(e) => updateSettings('background', {
                    ...settings.background,
                    value: e.target.value
                  })}
                  className="w-10 h-10 rounded-lg overflow-hidden"
                />
                <input
                  type="text"
                  value={settings.background.value}
                  onChange={(e) => updateSettings('background', {
                    ...settings.background,
                    value: e.target.value
                  })}
                  className="input flex-1"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Lighting */}
      <div className="space-y-4">
        <h3 className="text-sm font-light text-gray-400">Lighting</h3>
        <div className="space-y-3">
          <label className="flex items-center justify-between group">
            <div className="flex items-center gap-2 text-gray-300">
              <Sun size={18} strokeWidth={1.5} />
              <span>Shadows</span>
            </div>
            <div className="relative inline-block w-10 align-middle select-none">
              <input
                type="checkbox"
                checked={settings.lighting.shadows}
                onChange={(e) => updateSettings('lighting', {
                  ...settings.lighting,
                  shadows: e.target.checked
                })}
                className="toggle-checkbox"
                id="shadows"
              />
              <label className="toggle-label" htmlFor="shadows" />
            </div>
          </label>

          <div>
            <label className="block text-sm text-gray-300 mb-2">Environment</label>
            <select
              value={settings.lighting.environment}
              onChange={(e) => updateSettings('lighting', {
                ...settings.lighting,
                environment: e.target.value
              })}
              className="input"
            >
              <option value="legacy">Neutral</option>
              <option value="neutral">Studio</option>
              <option value="sunset">Outdoor</option>
            </select>
          </div>

          <div>
            <label className="block text-sm text-gray-300 mb-2">Intensity</label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={settings.lighting.intensity}
              onChange={(e) => updateSettings('lighting', {
                ...settings.lighting,
                intensity: parseFloat(e.target.value)
              })}
              className="w-full accent-brand-300 bg-dark-200 rounded-lg h-2 appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0</span>
              <span>1</span>
              <span>2</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewerSettingsPanel;