const router = require("express").Router();
const controller = require("../../controllers/showroomCategoryController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.post("/create", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.create));
router.delete(
  "/delete-by-name/:name",
  asyncError<PERSON><PERSON><PERSON>(controller.deleteByName)
);
router.put("/update-by-id/:id", asyncError<PERSON><PERSON><PERSON>(controller.updateById));

module.exports = router;
