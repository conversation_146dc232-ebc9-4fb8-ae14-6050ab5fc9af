const User = require("../models/User");
const Plan = require("../models/Plan");

/**
 * Middleware factory to verify if a user has access to a feature based on their plan
 * This middleware should be used after verifyJWT middleware as it relies on req.user being set
 * 
 * @param {Object} options - Options for the middleware
 * @param {Array<string>} options.requiredPlans - Array of plan names that have access (e.g., ['basic', 'pro', 'enterprise'])
 * @param {boolean} options.checkProductLimit - Whether to check if the user has reached their product limit
 * @returns {Function} Express middleware function
 */
const verifyPlanAccess = (options = {}) => {
  const { requiredPlans = [], checkProductLimit = false } = options;
  
  return async (req, res, next) => {
    try {
      // Get user ID from the request (set by verifyJWT middleware)
      const userId = req.user;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "Unauthorized: Authentication required"
        });
      }
      
      // Find the user and check their subscription status and plan
      const user = await User.findById(userId).select(
        "subscriptionStatus subscriptionPlan productCount productLimit"
      );
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "User not found"
        });
      }
      
      // Check if the user has an active subscription
      if (user.subscriptionStatus !== "active" && requiredPlans.length > 0 && !requiredPlans.includes("free")) {
        return res.status(403).json({
          success: false,
          message: "Subscription required: This feature requires an active subscription",
          subscriptionStatus: user.subscriptionStatus
        });
      }
      
      // Check if the user's plan is in the required plans list
      if (requiredPlans.length > 0 && !requiredPlans.includes(user.subscriptionPlan || "free")) {
        // Get the minimum required plan for better error message
        const minRequiredPlan = requiredPlans[0];
        
        return res.status(403).json({
          success: false,
          message: `Plan upgrade required: This feature requires at least the ${minRequiredPlan} plan`,
          currentPlan: user.subscriptionPlan || "free",
          requiredPlan: minRequiredPlan
        });
      }
      
      // Check if the user has reached their product limit
      if (checkProductLimit && user.productCount >= user.productLimit) {
        return res.status(403).json({
          success: false,
          message: "Product limit reached: Please upgrade your plan to add more products",
          productCount: user.productCount,
          productLimit: user.productLimit
        });
      }
      
      // If all checks pass, proceed to the next middleware
      next();
    } catch (error) {
      console.error("Error verifying plan access:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while verifying plan access",
        error: error.message
      });
    }
  };
};

module.exports = verifyPlanAccess;
