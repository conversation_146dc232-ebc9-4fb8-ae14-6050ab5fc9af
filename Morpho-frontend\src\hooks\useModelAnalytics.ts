import { useState, useEffect } from 'react';
import { analyticsService } from '../services/analytics';

export const useModelAnalytics = (modelId: string) => {
  const [data, setData] = useState<{
    views: number;
    downloads: number;
    conversions: number;
    avgTimeSpent: string;
    viewsByDevice: {
      desktop: number;
      mobile: number;
      tablet: number;
    };
    dailyViews: number[];
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        const analyticsData = await analyticsService.getModelAnalytics(modelId);
        setData(analyticsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch model analytics');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [modelId]);

  return { data, loading, error };
};