import React, { useRef } from 'react';
import { GlassCard } from '../ui/GlassCard';
import { useInView } from '../../hooks/useInView';
import { H2, H3, Body, SectionIntro } from '../ui/Typography';

const problems = [
  {
    id: 'complex',
    title: 'Complex 3D modeling websites',
    description: 'Most platforms expect you to be a 3D expert',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
    )
  },
  {
    id: 'expensive',
    title: 'Expensive 3D product rendering',
    description: 'Custom 3D product modeling costs $10,000+ and takes months',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
  {
    id: 'integration',
    title: 'Poor Shopify integration',
    description: 'Generic 3D model viewers don\'t work with your Shopify theme',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
      </svg>
    )
  },
  {
    id: 'slow',
    title: 'Slow loading 3D websites',
    description: 'Poorly optimized 3D models kill your conversion rates',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
  {
    id: 'overwhelming',
    title: 'Overwhelming model management',
    description: 'Existing 3D product visualization tools aren\'t built for e-commerce',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>
    )
  }
];

export const Problem: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });

  return (
    <section ref={sectionRef} id="problem" className="py-24 relative">
      {/* Background elements with enhanced effects */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -bottom-24 -left-24 w-64 h-64 bg-blue-500/10 rounded-full filter blur-[80px] animate-pulse-slow"></div>
        <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-purple-500/10 rounded-full filter blur-[100px] animate-pulse-slow" style={{animationDelay: "1s"}}></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className={`transition-all duration-700 mb-16 max-w-3xl mx-auto text-center ${
          isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 relative inline-block">
            Why 3D Product Visualization Remains <span className="relative">
              Out of Reach
              <svg className="absolute -bottom-2 left-0 w-full" height="6" viewBox="0 0 200 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 5C50 -1 150 -1 199 5" stroke="#0066CC" strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </span> for Most Shopify Merchants
          </h2>
          <p className="text-xl text-gray-300 mt-6">
            You know 3D product viewers and 3D models boost sales on your online store, but implementing 3D product visualization has been practically impossible because:
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {problems.map((problem, index) => (
            <div 
              key={problem.id}
              className={`transition-all duration-700 rounded-xl
                 transform-gpu   border border-blue-500/30 
               shadow-[0_20px_50px_rgba(8,_112,_184,_0.4)] hover:shadow-[0_20px_80px_rgba(8,_112,_184,_0.6)] ${
                isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{ transitionDelay: `${150 + index * 100}ms` }}
            >
              <div className="relative h-full group">
                {/* Enhanced backdrop with multiple layers */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-blue-800/5 to-purple-900/10 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-500 backdrop-blur-sm"></div>
                
                {/* External outline glow */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 via-blue-500/10 to-blue-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                
                {/* Main content with refined styling and multiple borders */}
                <div className="relative h-full bg-[#151520] backdrop-blur-xl rounded-xl overflow-hidden transition-all duration-500 p-6 border border-blue-500/30 group-hover:border-blue-500/50 group-hover:shadow-[0_0_15px_rgba(0,102,204,0.15)]">
                  {/* Inner border line */}
                  <div className="absolute inset-[3px] rounded-lg border border-blue-500/10 pointer-events-none"></div>
                  
                  {/* Corner accents */}
                  <div className="absolute top-0 left-0 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-transparent"></div>
                  <div className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-purple-500/10 to-transparent"></div>
                  
                  {/* Icon with enhanced presentation */}
                  <div className="relative mb-6">
                    <div className="absolute -top-4 -left-4 w-16 h-16 bg-blue-500/5 rounded-full blur-md"></div>
                    <div className="relative z-10 flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-blue-900/40 to-purple-900/60 border border-blue-500/30 shadow-lg transform group-hover:scale-105 transition-transform duration-300">
                      {/* Circular outline around icon */}
                      <div className="absolute inset-0 rounded-full border-2 border-dashed border-blue-500/20 group-hover:border-blue-500/40 transition-colors duration-300"></div>
                      
                      {/* Pulse effect */}
                      <div className="absolute inset-0 rounded-full bg-blue-500/10 animate-pulse opacity-0 group-hover:opacity-100"></div>
                      {problem.icon}
                    </div>
                  </div>
                  
                  {/* Content with improved typography and spacing */}
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-blue-300 transition-colors duration-300">{problem.title}</h3>
                    
                    {/* Divider with gradient - CHANGED FROM w-12 TO w-full */}
                    <div className="w-full h-0.5 bg-gradient-to-r from-blue-500/0 via-blue-500/30 to-blue-500/0 my-3"></div>
                    
                    <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300">{problem.description}</p>
                  </div>
                  
                  {/* Decorative elements */}
                  <div className="absolute bottom-4 right-4 w-2 h-2 rounded-full bg-blue-500/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                
                {/* Subtle outer border */}
                <div className="absolute inset-px rounded-xl border border-blue-500/5 pointer-events-none"></div>
              </div>
            </div>
          ))}
        </div>

        <div className={`mt-16 max-w-4xl mx-auto transition-all duration-700 delay-700 ${
          isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="bg-gradient-to-br from-[#1A1A25]/90 to-[#1A1A25]/70 rounded-lg p-8 border border-blue-500/30 relative overflow-hidden shadow-lg">
            {/* Enhanced outline with multiple layers */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 via-blue-500/10 to-purple-500/30 rounded-lg blur-sm"></div>
            <div className="absolute inset-0 border-2 border-blue-500/20 rounded-lg"></div>
            
            {/* Enhanced quote mark design element */}
            <div className="absolute -top-6 -left-6 text-blue-500/10 select-none pointer-events-none">
              <svg width="120" height="120" viewBox="0 0 120 120" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path d="M48 24C28.05 24 12 40.05 12 60V96H48V60H24C24 46.8 34.8 36 48 36V24ZM108 24C88.05 24 72 40.05 72 60V96H108V60H84C84 46.8 94.8 36 108 36V24Z"/>
              </svg>
            </div>
            
            {/* Glowing accent line */}
            <div className="absolute h-full w-1 top-0 left-0 bg-gradient-to-b from-blue-500/0 via-blue-500/50 to-blue-500/0"></div>
            
            <div className="flex flex-col md:flex-row items-center gap-8 relative z-10">
              <div className="flex-shrink-0">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500/20 to-purple-900/30 rounded-full flex items-center justify-center shadow-lg border border-blue-500/30 relative">
                  {/* Inner glow effect */}
                  <div className="absolute inset-2 rounded-full bg-blue-500/5 blur-md"></div>
                  
                  {/* Circular outline */}
                  <div className="absolute inset-0 rounded-full border-2 border-dashed border-blue-500/30"></div>
                  
                  <svg className="w-12 h-12 text-blue-400 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 14c1.5 1.5 4.5 1.5 6 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
              </div>
              <div>
                <blockquote className="text-xl italic text-white mb-4 relative">
                  "We tried three different 3D viewers before Morpho. None of them understood Shopify or our specific online store needs."
                  {/* Decorative quotes */}
                  <span className="absolute -top-3 -left-3 text-blue-500/20 text-4xl">"</span>
                  <span className="absolute -bottom-5 -right-3 text-blue-500/20 text-4xl">"</span>
                </blockquote>
                
                {/* Divider with gradient */}
                <div className="w-24 h-0.5 bg-gradient-to-r from-blue-500/0 via-blue-500/80 to-blue-500/0 mb-3"></div>
                
                <p className="text-blue-400 font-medium flex items-center">
                  — Frustrated Shopify merchant
                </p>
              </div>
            </div>
            
            {/* Extra decorative elements */}
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-purple-500/5 to-transparent rounded-tl-full"></div>
          </div>
        </div>
      </div>
    </section>
  );
};