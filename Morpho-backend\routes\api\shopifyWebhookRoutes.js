const router = require('express').Router();
const express = require('express');
const controller = require('../../controllers/shopifyWebhookController');
const verifyShopifyWebhook = require('../../middleware/verifyShopifyWebhook');
const asyncErrorHandler = require('../../errors/asyncErrorHandler');

/**
 * Shopify Webhook Routes
 * All webhook requests must be verified using HMAC
 * Raw body is required for HMAC verification
 */

// Use raw body parser for all webhook routes
router.use(express.raw({ type: 'application/json' }));

// Main webhook endpoint - handles all webhook types
router.post(
  '/',
  verifyShopifyWebhook,
  asyncErrorHandler(controller.handleWebhook)
);

module.exports = router; 