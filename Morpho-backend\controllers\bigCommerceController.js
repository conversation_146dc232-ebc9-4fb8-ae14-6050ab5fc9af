const assetLibraryModel = require("../models/AssetLibrary");
const GlbModel = require("../models/GlbModel");
const projectSchema = require("../models/Project");
exports.getAllProducts = async (req, res, next) => {
  const limit = parseInt(req.query.limit, 10) || 5;
  try {
    const products = await req.bigCommerceClient.get(
      `/catalog/products?limit=${limit}`,
    );
    res.status(200).json({ success: true, data: products });
  } catch (error) {
    console.error(
      "Error fetching products:",
      error.response?.data || error.message,
    );
    res
      .status(500)
      .json({ success: false, message: "Error fetching products" });
  }
};

exports.getStatistics = async (req, res, next) => {
  // Fetch all orders
  const orders = await req.bigCommerceClient.get("/orders");

  if (!orders || orders.length === 0) {
    return res.status(200).json({
      success: true,
      message: "No orders found",
      data: {
        totalOrders: 0,
        topProducts: [],
      },
    });
  }

  const orderProductData = [];
  // Fetch products for each order
  for (const order of orders) {
    try {
      const orderProducts = await req.bigCommerceClient.get(
        `/orders/${order.id}/products`,
      );
      orderProductData.push(...orderProducts);
    } catch (productError) {
      console.error(
        `Error fetching products for order ${order.id}:`,
        productError.response?.data || productError.message,
      );
    }
  }

  // Calculate product sales statistics
  const productSales = {};
  orderProductData.forEach((item) => {
    if (!productSales[item.product_id]) {
      productSales[item.product_id] = {
        productId: item.product_id,
        name: item.name,
        totalSold: 0,
        revenue: 0,
      };
    }
    productSales[item.product_id].totalSold += item.quantity;
    productSales[item.product_id].revenue += item.quantity * item.price_inc_tax;
  });

  // Sort by totalSold and return the top 10 products
  const sortedProducts = Object.values(productSales)
    .sort((a, b) => b.totalSold - a.totalSold)
    .slice(0, 10);

  res.status(200).json({
    success: true,
    data: {
      totalOrders: orders.length,
      topProducts: sortedProducts,
    },
  });
};

exports.saveAllProducts = async (req, res, next) => {
  const userId = req.user;

  // Get the user's project
  const project = await projectSchema.findOne({ client: userId }).lean();
  const assetLibrary = await assetLibraryModel.findOne({
    project: project._id,
  });

  const assetLibraryId = assetLibrary._id;
  if (!project) {
    return res
      .status(404)
      .json({ success: false, message: "Project not found" });
  }
  const projectId = project._id;

  // Fetch all products from BigCommerce with images included
  const response = await req.bigCommerceClient.get(
    "/catalog/products?include=images",
  );
  const products = response.data;

  if (!products || products.length === 0) {
    return res.status(404).json({
      success: false,
      message: "No products found on BigCommerce.",
    });
  }

  // Arrays to track new and skipped products
  const skippedProducts = [];
  const productPromises = products.map(async (product) => {
    // Check if the product already exists in the database using `integrationKey`
    const exists = await GlbModel.findOne({
      integrationKey: product.id.toString(),
    });
    if (exists) {
      skippedProducts.push;
      return null;
    }

    // Get the first image URL
    const imageUrl =
      product.images && product.images.length > 0
        ? product.images[0].url_standard
        : null;

    // Skip products without an image
    if (!imageUrl) {
      skippedProducts.push({
        name: product.name,
        id: product.id,
        reason: "Missing image URL.",
      });
      return null;
    }

    // Clean the description field
    const cleanDescription = product.description
      ? product.description.replace(/<\/?[^>]+(>|$)/g, "").trim() // Remove HTML tags and trim
      : "";

    // Format the product for database insertion, including `integrationKey` and `projectId`
    return {
      name: product.name,
      description: cleanDescription, // Cleaned description
      imageUrl, // Resolved image URL
      price: parseFloat(product.price || 0),
      sku: product.sku || "",
      published: product.is_visible === 1, // BigCommerce uses `is_visible` for visibility
      integrationKey: product.id.toString(), // New integration-specific key
      project: projectId, // Include the project ID
      asset_library: assetLibraryId,
    };
  });

  // Resolve promises and filter out null values
  const formattedProducts = await Promise.all(productPromises);
  const newProducts = formattedProducts.filter(Boolean); // Remove skipped products

  // Insert new products into MongoDB
  if (newProducts.length > 0) {
    await GlbModel.insertMany(newProducts);
  }

  // Respond with the result, including skipped products
  res.status(201).json({
    success: true,
    message: `${newProducts.length} new product(s) added.`,
    data: newProducts,
    skippedProducts, // Include skipped products and reasons
  });
};
