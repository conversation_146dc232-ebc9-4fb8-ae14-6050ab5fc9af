const mongoose = require("mongoose");

const textureSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    imageUrl: {
      type: String,
      required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    url: {
      type: String,
      required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    customized_material: {
      type: mongoose.Types.ObjectId,
      ref: "CustomizedMaterial",
    },
    asset_library: {
      type: mongoose.Types.ObjectId,
      ref: "AssetLibrary",
    },
  },
  { timestamps: true },
);

const textureModel = mongoose.model("Texture", textureSchema);
module.exports = textureModel;
