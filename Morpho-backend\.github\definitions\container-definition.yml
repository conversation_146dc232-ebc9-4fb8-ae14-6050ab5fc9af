containerDefinitions:
  - name: backendmorphoimage
    image: docker.io/modularcx/backendmorphoimage:latest
    repositoryCredentials:
      credentialsParameter: arn:aws:secretsmanager:eu-central-1:334641153102:secret:Dockerhub_Password-sFGupd
    cpu: 0
    portMappings:
      - containerPort: 5000
        hostPort: 0
        protocol: tcp
    environment: []

cpu: "512"
executionRoleArn: arn:aws:iam::334641153102:role/ecsTaskExecutionRole
family: backendMorphoTask
networkMode: bridge
requiresCompatibilities:
  - EC2
memory: "256"
taskRoleArn: arn:aws:iam::334641153102:role/ecsTaskIAMRole
volumes: []
