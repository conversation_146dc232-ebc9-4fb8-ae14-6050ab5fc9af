const mongoose = require("mongoose");

const materialLibrarySchema = new mongoose.Schema(
  {
    project: {
      type: mongoose.Types.ObjectId,
      ref: "Project",
      index: true,
    },
    materials: [
      {
        type: mongoose.Types.ObjectId,
        ref: "Materials",
      },
    ],
  },
  { timestamps: true },
);

const materialLibraryCollection = new mongoose.model(
  "Material Library",
  materialLibrarySchema,
);

module.exports = materialLibraryCollection;
