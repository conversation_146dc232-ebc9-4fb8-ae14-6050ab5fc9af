const showroomCategoryModel = require("../models/showroomCategoriesModel");

class Controller {
  create = async (req, res) => {
    const category = new showroomCategoryModel(req.body);
    await category.save();
    return res.status(200).json({ data: category });
  };
  deleteByName = async (req, res) => {
    const { name } = req.params;
    const categoryToBeDeleted = await showroomCategoryModel.findOneAndDelete({
      name,
    });
  };
  updateById = async (req, res) => {
    const { id } = req.params;
    const categoryToBeUpdated = await showroomCategoryModel.findOneAndUpdate(
      id,
      req.body,
      { new: true },
    );
    if (!categoryToBeUpdated) {
      return res.status(404).json("Category Not Found");
    }
    return res.status(200).json({ data: categoryToBeUpdated });
  };
}

const controller = new Controller();
module.exports = controller;
