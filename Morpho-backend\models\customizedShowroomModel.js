const mongoose = require("mongoose");

const customizedShowroomSchema = new mongoose.Schema(
  {
    position: {
      x: Number,
      y: Number,
      z: Number,
    },
    sku: {
      type: Number,
    },
    rotation: {
      x: Number,
      y: Number,
    },
    scale: Number,
    url: {
      type: String,
      required: true,
    },
    description: {
      bold: Boolean, //sa7
      font: String, //sa7
      italic: Boolean, //sa7
      text: String, //sa7
      textAlignment: String, //sa7
      toogle: Boolean, //sa7
    },

    hdr: {
      id: {
        type: mongoose.Types.ObjectId,
        ref: "HDR",
      },
      intensity: Number,
      rotationY: Number,
    },

    logo: {
      borderShape: String,
      frameColor: { r: Number, g: Number, b: Number },
      imageUrl: { type: String },
      scale: Number,
    },
    materials: [
      {
        name: { type: String },
        customizationId: {
          type: mongoose.Types.ObjectId,
          ref: "ShowroomMaterial",
        },
        customizationType: String,
        r: Number,
        g: Number,
        b: Number,
      },
    ],
    user: {
      type: mongoose.Types.ObjectId,
      ref: "User",
    },
    glow: {
      has: { type: Boolean },
      kernel: Number,
      intensity: Number,
    },
    category: {
      type: mongoose.Types.ObjectId,
      ref: "ShowroomCategory",
    },
    images: {
      toogle: Boolean,
      imageUrls: [{ type: String }],
      borderColor: { r: Number, g: Number, b: Number },
    },
    selectedTemplate: String,
    templateColor: String,
    templateTextColor: String,
    templateTextFont: String,
  },

  { timestamps: true }
);

const customizedShowroomModel = mongoose.model(
  "Customized Showroom",
  customizedShowroomSchema
);

module.exports = customizedShowroomModel;
