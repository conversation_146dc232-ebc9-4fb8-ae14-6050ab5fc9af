export interface Model3D {
  _id: string;
  name: string;
  description: string;
  url: string;
  multiUrls: string[];
  imageUrl: string;
  price: number;
  sku: string;
  sizes: any[];
  width: any[];
  published: boolean;
  project: {
    _id: string;
    name: string;
    description: string;
    industry: string;
    type: string;
    status: string;
  };
  customizations: any[];
  variants: any[];
  key: string;
  usdzUrl: string;
  createdAt: string;
  updatedAt: string;
}