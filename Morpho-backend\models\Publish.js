const mongoose = require("mongoose");
const controlPanelSchema = require("./ControlPanel");
const backgroundSchema = require("./Background");
const publishSchema = mongoose.Schema({
  project: {
    type: mongoose.Types.ObjectId,
    ref: "Project",
  },
  glb: {
    type: mongoose.Types.ObjectId,
    ref: "GlbModel",
  },
  glbUrls: [
    {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
      required: true,
    },
  ],
  hdr: [
    {
      url: {
        type: String,
        match:
          /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
      },
      level: {
        type: Number,
      },
    },
  ],
  autoRotate: {
    type: Boolean,
    required: true,
  },
  AR: {
    type: Boolean,
    required: true,
  },
  fullScreen: {
    type: Boolean,
    required: true,
  },
  zoom: {
    type: Boolean,
    required: true,
  },
  background: backgroundSchema,
  hashedId: {
    type: String,
    required: true,
  },
  icon360: {
    type: Boolean,
    required: true,
  },
  controlPanel: controlPanelSchema,
});

const publishModel = new mongoose.model("Publish", publishSchema);

module.exports = publishModel;
