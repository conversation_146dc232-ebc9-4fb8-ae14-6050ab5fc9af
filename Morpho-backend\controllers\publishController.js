const publishSchema = require("../models/Publish");
const glbSchema = require("../models/GlbModel");
const User = require("../models/User");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
const {
  getAllData,
  getModelById,
  createModel,
  deleteModelById,
  getDataByVariable,
  updateModelById,
} = require("../services/dataService");
const { hashId, verifyHash } = require("../utils/hashing");
const {
  incrementProductCount,
  decrementProductCount,
} = require("../utils/productLimitUtils");
class Controller {
  createPublish = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Create the publish record
    const result = await createModel(
      req,
      res,
      "Publish",
      publishSchema,
      hashId
    );

    // If the publish was created successfully, increment the user's product count
    if (result && result.success) {
      const userId = req.user;
      const incrementResult = await incrementProductCount(userId);
      if (!incrementResult.success) {
        console.error(
          "Failed to increment product count:",
          incrementResult.message
        );
      }
    }

    return result;
  };
  getAll = async (req, res) => {
    // Get all publish records
    return await getAllData(req, res, "Publish", publishSchema);
  };
  getById = async (req, res) => {
    const { _id } = req.body;

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Publish ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Get publish by ID
    return await getModelById(req, res, "Publish", publishSchema, _id);
  };
  getByProject = async (req, res) => {
    const { project } = req.body;

    // Validate required fields
    if (!project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Get publish by project
    return await getDataByVariable(req, res, "Publish", publishSchema, project);
  };
  getByGlb = async (req, res) => {
    const { glb } = req.body;

    // Validate required fields
    if (!glb) {
      throw new expressError(
        "GLB ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Get publish by GLB
    return await getDataByVariable(req, res, "Publish", publishSchema, glb);
  };
  updateById = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate required fields
    if (!req.body._id) {
      throw new expressError(
        "Publish ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Update publish by ID
    return await updateModelById(req, res, "Publish", publishSchema);
  };
  deleteById = async (req, res) => {
    const { _id } = req.body;
    const userId = req.user;

    // Validate user authentication
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Publish ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Delete the publish record
    const result = await deleteModelById(
      req,
      res,
      "Publish",
      publishSchema,
      _id
    );

    // If the publish was deleted successfully, decrement the user's product count
    if (result && result.success) {
      const decrementResult = await decrementProductCount(userId);
      if (!decrementResult.success) {
        console.error(
          "Failed to decrement product count:",
          decrementResult.message
        );
      }
    }

    return result;
  };
  saveSettings = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Save publish settings
    return await createModel(req, res, "Publish", publishSchema, "hashId");
  };
  generateLink = async (req, res) => {
    // Check if user has reached their product limit
    const userId = req.user;
    if (!userId) {
      throw new expressError(
        "Unauthorized: User ID not found",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Check if the user has reached their product limit
    const user = await User.findById(userId).select(
      "productCount productLimit subscriptionPlan"
    );

    if (!user) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Check if the user has reached their product limit
    if (user.productCount >= user.productLimit) {
      throw new expressError(
        `Product limit reached: Your ${user.subscriptionPlan} plan allows ${user.productLimit} products. Please upgrade your plan to publish more products.`,
        403,
        expressError.CODES.LIMIT_EXCEEDED
      );
    }

    // Create the publish record
    let publishToBeCreated = new publishSchema(req.body);
    publishToBeCreated.hashedId = hashId(publishToBeCreated._id);
    await publishToBeCreated.save();

    // Increment the user's product count
    const incrementResult = await incrementProductCount(userId);
    if (!incrementResult.success) {
      console.error(
        "Failed to increment product count:",
        incrementResult.message
      );
    }

    const generateLink = `${process.env.SCENE_VIEWER}/${publishToBeCreated._id}`;
    return res.json(generateLink);
  };
  generateLinkMobileVersion = async (req, res) => {
    // Check if user has reached their product limit
    const userId = req.user;
    if (!userId) {
      throw new expressError(
        "Unauthorized: User ID not found",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Check if the user has reached their product limit
    const user = await User.findById(userId).select(
      "productCount productLimit subscriptionPlan"
    );

    if (!user) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Check if the user has reached their product limit
    if (user.productCount >= user.productLimit) {
      throw new expressError(
        `Product limit reached: Your ${user.subscriptionPlan} plan allows ${user.productLimit} products. Please upgrade your plan to publish more products.`,
        403,
        expressError.CODES.LIMIT_EXCEEDED
      );
    }

    // Validate required fields
    const bodyData = req.body;
    if (!bodyData.project || !bodyData.glb || !bodyData.glbUrls) {
      throw new expressError(
        "Project, GLB, and GLB URLs are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Prepare input data
    const inputs = {
      project: bodyData.project,
      glb: bodyData.glb,
      glbUrls: [],
      hdr: {},
      autoRotate: bodyData.autoRotate,
      AR: bodyData.AR,
      fullScreen: bodyData.fullScreen,
      zoom: bodyData.zoom,
      background: {},
      icon360: bodyData.icon360,
      controlPanel: {},
    };

    inputs.glbUrls.push(req.body.glbUrls);
    inputs.background.color = req.body.color;
    inputs.background.imageLink = req.body.imageLink;
    inputs.background.isTransparent = req.body.isTransparent;
    inputs.controlPanel.orientation = req.body.orientation;
    inputs.controlPanel.position = req.body.position;

    // Create publish record
    let publishToBeCreated = new publishSchema(inputs);
    publishToBeCreated.hashedId = hashId(publishToBeCreated._id);
    await publishToBeCreated.save();

    // Increment the user's product count
    const incrementResult = await incrementProductCount(userId);
    if (!incrementResult.success) {
      console.error(
        "Failed to increment product count:",
        incrementResult.message
      );
    }

    // Generate and return link
    const generateLink = `${process.env.SCENE_VIEWER}/${publishToBeCreated._id}`;
    return res.json(generateLink);
  };
  getPublishDetails = async (req, res) => {
    const _id = req.params._id;
    // Find publish by ID
    let publishToBeRetrieved = await publishSchema
      .findById(_id)
      .populate("glb", "name")
      .lean();

    // Check if publish exists
    if (!publishToBeRetrieved) {
      throw new expressError(
        "Publish not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Format publish data
    if (publishToBeRetrieved?.glb) {
      publishToBeRetrieved.glb = publishToBeRetrieved.glb.name;
    }

    // Extract filename from URL
    if (
      publishToBeRetrieved.glbUrls &&
      publishToBeRetrieved.glbUrls.length > 0
    ) {
      const fullUrl = publishToBeRetrieved.glbUrls[0];
      const fileName = fullUrl.substring(fullUrl.lastIndexOf("/") + 1);
      publishToBeRetrieved.glb = fileName;
      publishToBeRetrieved.glbUrls = null;
    }

    // Return publish details
    return res.json(publishToBeRetrieved);
  };
  getPublishDetailsShopify = async (req, res) => {
    // This method is currently empty - implement as needed
    throw new expressError(
      "This feature is not implemented yet",
      501,
      expressError.CODES.NOT_IMPLEMENTED
    );
  };
}

const controller = new Controller();
module.exports = controller;
