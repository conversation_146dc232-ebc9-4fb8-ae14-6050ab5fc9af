name: PRODUCTION TRIGGER

on:
  workflow_dispatch:

jobs:
  staging-production-swap:
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/s3_production_job.yml@main
    with:
      AWS_S3_BUILD_BUCKET: ${{ vars.AWS_S3_BUILD_BUCKET }}
      AWS_FRONTEND_SSM_ROLE_PATH: ${{ vars.AWS_FRONTEND_SSM_ROLE_PATH }}
    secrets: inherit

  amplify-deployment:
    needs: staging-production-swap
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/amplify_production_job.yml@main
    with:
      AWS_FRONTEND_SSM_ROLE_PATH: ${{ vars.AWS_FRONTEND_SSM_ROLE_PATH }}
      AWS_S3_BUILD_BUCKET: ${{ vars.AWS_S3_BUILD_BUCKET }}
      AWS_AMPLIFY_PRODUCTION_APP_NAME: ${{ vars.AWS_AMPLIFY_PRODUCTION_APP_NAME }}
      AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME: ${{ vars.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}
      AWS_AMPLIFY_PRODUCTION_DOMAIN: ${{ vars.AWS_AMPLIFY_PRODUCTION_DOMAIN }}
      AWS_AMPLIFY_PRODUCTION_SUBDOMAIN: ${{ vars.AWS_AMPLIFY_PRODUCTION_SUBDOMAIN }}
    secrets: inherit
