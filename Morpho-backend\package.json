{"name": "morpho-backend", "version": "1.0.0", "description": "", "main": "index.js", "engines": {"node": "18.16.0"}, "scripts": {"start": "nodemon index.js", "install:prod": "npm ci --production"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.620.0", "@aws-sdk/lib-storage": "^3.620.1", "aws-sdk": "^2.1386.0", "axios": "^1.7.9", "bcrypt": "^5.1.0", "body-parser": "^1.20.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.9.0", "form-data": "^4.0.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "mongoose": "^6.7.0", "multer": "^1.4.5-lts.1", "node-bigcommerce": "^4.1.0", "nodemailer": "^6.9.4", "nodemon": "^3.1.9", "passport-google-oauth20": "^2.0.0", "sanitize-html": "^2.11.0", "shopify-api-node": "^3.14.1", "stripe": "^16.9.0", "uuid": "^9.0.0", "ws": "^8.18.1"}}