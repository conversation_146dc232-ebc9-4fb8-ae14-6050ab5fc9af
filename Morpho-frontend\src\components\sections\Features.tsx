import React, { useRef, useState } from 'react';
import { GlassContainer } from './../ui/GlassContainer';
import { useInView } from '../../hooks/useInView';
import { H3, H4, SectionIntro } from './../ui/Typography';

const features = [
  {
    id: 'integration',
    title: 'Complete Shopify 3D Integration',
    description: 'Native Shopify 3D app with one-click installation, automatic product matching with your existing catalog.',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    gradient: 'from-blue-500/20 to-blue-400/20'
  },
  {
    id: 'visualization',
    title: '3D Product Visualization App',
    description: 'Transform products into 3D models in minutes using your existing iPhone or iPad with automated optimization.',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    ),
    gradient: 'from-purple-500/20 to-blue-500/20'
  },
  {
    id: 'upload',
    title: 'Direct 3D Model Upload',
    description: 'Upload any industry-standard 3D file formats with automatic format conversion and optimization.',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
      </svg>
    ),
    gradient: 'from-teal-500/20 to-blue-500/20'
  },
  {
    id: 'management',
    title: '3D Product Management System',
    description: 'Manage 3D models alongside your Shopify product catalog with batch operations and automatic synchronization.',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
      </svg>
    ),
    gradient: 'from-blue-500/20 to-purple-500/20'
  },
  {
    id: 'analytics',
    title: '3D Product Conversion Analytics',
    description: 'Track how 3D product visualization impacts your conversion rates with direct ROI measurement.',
    icon: (
      <svg className="w-10 h-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    gradient: 'from-blue-400/20 to-teal-500/20'
  }
];

const comparisonData = [
  {
    challenge: 'Technical Complexity',
    other: 'Require expertise in 3D modeling',
    morpho: 'No 3D skills needed',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
      </svg>
    )
  },
  {
    challenge: 'Shopify Integration',
    other: 'Generic plugins with limited features',
    morpho: 'Native Shopify 3D app',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    )
  },
  {
    challenge: 'Implementation Time',
    other: 'Weeks to implement',
    morpho: 'Live within hours',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
  {
    challenge: 'Content Creation',
    other: 'Expensive outsourcing',
    morpho: 'Scan with your iPhone',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    )
  },
  {
    challenge: 'Loading Speed',
    other: 'Slow 3D websites',
    morpho: 'Under 2 seconds',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    )
  }
];

export const Features: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);
  const [hoveredRow, setHoveredRow] = useState<number | null>(null);

  return (
    <section ref={sectionRef} id="features" className="py-12 sm:py-16 md:py-24 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/4 right-0 w-64 h-64 bg-blue-500/10 rounded-full filter blur-[80px] animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 left-0 w-64 h-64 bg-purple-500/10 rounded-full filter blur-[80px] animate-pulse-slow" style={{animationDelay: "1.5s"}}></div>
        <div className="absolute top-3/4 right-1/4 w-32 h-32 bg-teal-400/10 rounded-full filter blur-[60px] animate-pulse-slow" style={{animationDelay: "0.7s"}}></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <SectionIntro
          className={`transition-all duration-700 ${
            isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          Key Features of Our Shopify 3D Product Viewer & Visualization Platform
        </SectionIntro>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={feature.id}
              className={`
                    transform-gpu transition-all duration-500 rounded-xl overflow-hidden
               shadow-[0_20px_50px_rgba(8,_112,_184,_0.4)] hover:shadow-[0_20px_80px_rgba(8,_112,_184,_0.6)]
                 ${
                isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{ transitionDelay: `${150 + index * 100}ms` }}
              onMouseEnter={() => setHoveredFeature(feature.id)}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              <div className="relative h-full group">
                {/* Enhanced backdrop with multiple layers */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-500 backdrop-blur-sm`}></div>
                
                {/* External outline glow */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 via-blue-500/10 to-blue-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                
                {/* Main content with refined styling and multiple borders */}
                <div className="relative h-full bg-[#151520] backdrop-blur-xl rounded-xl overflow-hidden transition-all duration-500 p-6 border border-blue-500/30 group-hover:border-blue-500/50 group-hover:shadow-[0_0_15px_rgba(0,102,204,0.15)]">
                  {/* Inner border line */}
                  <div className="absolute inset-[3px] rounded-lg border border-blue-500/10 pointer-events-none"></div>
                  
                  {/* Corner accents */}
                  <div className="absolute top-0 left-0 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-transparent rounded-br-full"></div>
                  <div className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-purple-500/10 to-transparent rounded-tl-full"></div>
                  
                  {/* Icon with enhanced presentation */}
                  <div className="relative mb-4">
                    <div className="absolute -top-4 -left-4 w-16 h-16 bg-blue-500/5 rounded-full blur-md"></div>
                    <div className="relative z-10 flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-blue-900/40 to-purple-900/60 border border-blue-500/30 shadow-lg transform group-hover:scale-105 transition-transform duration-300">
                      {/* Circular outline around icon */}
                      <div className="absolute inset-0 rounded-full border-2 border-dashed border-blue-500/20 group-hover:border-blue-500/40 transition-colors duration-300"></div>
                      
                      {/* Pulse effect */}
                      <div className="absolute inset-0 rounded-full bg-blue-500/10 animate-pulse opacity-0 group-hover:opacity-100"></div>
                      {feature.icon}
                    </div>
                  </div>
                  
                  {/* Content with improved typography and spacing - REDUCED SPACING */}
                  <div className="flex flex-col h-full">
                    <h4 className="text-xl font-semibold text-white mb-1 group-hover:text-blue-300 transition-colors duration-300">{feature.title}</h4>
                    
                    {/* Divider with gradient - REDUCED MARGINS */}
                    <div className="w-full h-0.5 bg-gradient-to-r from-blue-500/0 via-blue-500/30 to-blue-500/0 mb-2"></div>
                    
                    <p className="text-gray-400 group-hover:text-gray-300 transition-colors duration-300 mt-0">{feature.description}</p>
                  </div>
                  
                  {/* Decorative elements */}
                  <div className="absolute bottom-4 right-4 w-2 h-2 rounded-full bg-blue-500/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                
                {/* Subtle outer border */}
                <div className="absolute inset-px rounded-xl border border-blue-500/5 pointer-events-none"></div>
              </div>
            </div>
          ))}
        </div>

        <div className={`mt-12 md:mt-16 transition-all duration-700 delay-700 ${
          isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <GlassContainer className="p-5 sm:p-6 md:p-8 rounded-lg border-blue-400/20 shadow-glow-sm relative overflow-hidden">
            {/* Enhanced background with layered gradients */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5"></div>
              <div className="absolute -top-24 -right-24 w-48 h-48 bg-blue-500/10 rounded-full filter blur-[60px] opacity-70"></div>
              <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-purple-500/10 rounded-full filter blur-[60px] opacity-60"></div>
            </div>
            
            <div className="flex flex-col lg:grid lg:grid-cols-2 gap-8 items-start lg:items-center relative z-10 overflow-hidden ">
              <div>
                <H3 className="mb-6 relative">
                  <span className="relative z-10">How Morpho's 3D Product Viewer Solves Problems Other 3D Websites Can't</span>
                  <span className="block h-1 w-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2"></span>
                </H3>
                
                <div className="mt-6 md:mt-8 space-y-1 overflow-x-hidden pb-2">
                  {/* Mobile Comparison Cards */}
                  <div className="lg:hidden space-y-4">
                    {comparisonData.map((item, index) => (
                      <div key={index} className="bg-[#12121A]/60 rounded-lg p-4 border border-gray-800 hover:border-blue-400/30 transition-all duration-300">
                        <div className="flex items-center mb-2">
                          <span className="text-blue-400 mr-2">{item.icon}</span>
                          <H4 className="mb-0">{item.challenge}</H4>
                        </div>
                        <div className="grid grid-cols-2 gap-3 mt-3">
                          <div className="bg-[#1A1A25] p-3 rounded border border-blue-500/20">
                            <h5 className="text-blue-400 text-xs font-semibold mb-1">Other 3D Viewers</h5>
                            <p className="text-gray-400 text-sm line-through">{item.other}</p>
                          </div>
                          <div className="bg-[#1A1A25] p-3 rounded border border-blue-400/30 shadow-glow-sm">
                            <h5 className="text-blue-400 text-xs font-semibold mb-1">With Morpho</h5>
                            <p className="text-blue-300 text-sm">{item.morpho}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Desktop Comparison Table */}
                  <div className="hidden lg:block">
                    {/* Header Row */}
                    <div className="grid grid-cols-3 gap-2 mb-4">
                      <div className="text-white font-semibold px-3 py-2">Your Challenge</div>
                      <div className="text-blue-400 font-semibold px-3 py-2">Other 3D Viewers</div>
                      <div className="text-blue-400 font-semibold px-3 py-2">With Morpho</div>
                    </div>
                    
                    {/* Comparison Rows */}
                    {comparisonData.map((item, index) => (
                      <div 
                        key={index} 
                        className={`grid grid-cols-3 gap-2 overflow-hidden relative rounded-md transition-all duration-300 ${
                          hoveredRow === index ? 'bg-blue-400/10 shadow-glow-sm scale-[1.01]' : 'bg-transparent'
                        }`}
                        onMouseEnter={() => setHoveredRow(index)}
                        onMouseLeave={() => setHoveredRow(null)}
                      >
                        <div className="text-white px-3 py-4 flex items-center space-x-2">
                          <span className={`transition-colors duration-300 ${hoveredRow === index ? 'text-blue-400' : 'text-white'}`}>
                            {item.icon}
                          </span>
                          <span>{item.challenge}</span>
                        </div>
                        <div className={`text-gray-400 px-3 py-4 relative border-l border-blue-400/10 ${
                          hoveredRow === index ? 'line-through text-blue-200/40' : ''
                        }`}>
                          {item.other}
                          {hoveredRow === index && (
                            <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-30">
                              <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </div>
                          )}
                        </div>
                        <div className={`px-3 py-4 relative border-l border-blue-400/10 transition-all duration-300 ${
                          hoveredRow === index ? 'text-blue-400 font-semibold' : 'text-blue-400'
                        }`}>
                          {hoveredRow === index && (
                            <span className="absolute left-0 top-0 w-1 h-full bg-blue-400 animate-pulse"></span>
                          )}
                          <span className="relative">{item.morpho}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              {/* Data Visualization SVG */}
              <div className="relative mt-8 lg:mt-0 transition-all duration-700 rounded-xl
                 transform-gpu   border border-blue-500/30 
               shadow-[0_20px_50px_rgba(8,_112,_184,_0.4)] hover:shadow-[0_20px_80px_rgba(8,_112,_184,_0.6)] ">
                <div className=" aspect-square bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center overflow-hidden relative group">
                  {/* Static comparison visualization */}
                  <svg className="w-full h-full" viewBox="0 0 300 300" xmlns="http://www.w3.org/2000/svg">
                    <g transform="translate(150, 150)">
                      {/* Chart background */}
                      <rect x="-100" y="-100" width="200" height="200" fill="#1A1A25" opacity="0.3" rx="10" />
                      
                      {/* Chart grid lines */}
                      <line x1="-80" y1="0" x2="80" y2="0" stroke="#FFFFFF" strokeWidth="1" strokeOpacity="0.1" />
                      <line x1="-80" y1="-40" x2="80" y2="-40" stroke="#FFFFFF" strokeWidth="1" strokeOpacity="0.1" />
                      <line x1="-80" y1="40" x2="80" y2="40" stroke="#FFFFFF" strokeWidth="1" strokeOpacity="0.1" />
                      
                      <line x1="0" y1="-80" x2="0" y2="80" stroke="#FFFFFF" strokeWidth="1" strokeOpacity="0.1" />
                      <line x1="-40" y1="-80" x2="-40" y2="80" stroke="#FFFFFF" strokeWidth="1" strokeOpacity="0.1" />
                      <line x1="40" y1="-80" x2="40" y2="80" stroke="#FFFFFF" strokeWidth="1" strokeOpacity="0.1" />
                      
                      {/* Chart bars - Other 3D viewers */}
                      <rect x="-70" y="10" width="20" height="40" fill="#0066CC" opacity="0.3" rx="2" />
                      <rect x="-30" y="25" width="20" height="25" fill="#0066CC" opacity="0.3" rx="2" />
                      <rect x="10" y="15" width="20" height="35" fill="#0066CC" opacity="0.3" rx="2" />
                      <rect x="50" y="20" width="20" height="30" fill="#0066CC" opacity="0.3" rx="2" />
                      
                      {/* Chart bars - Morpho */}
                      <rect x="-70" y="-80" width="20" height="80" fill="#0066CC" opacity="0.9" rx="2" />
                      <rect x="-30" y="-60" width="20" height="60" fill="#0066CC" opacity="0.9" rx="2" />
                      <rect x="10" y="-75" width="20" height="75" fill="#0066CC" opacity="0.9" rx="2" />
                      <rect x="50" y="-65" width="20" height="65" fill="#0066CC" opacity="0.9" rx="2" />
                      
                      {/* Legend */}
                      <rect x="-90" y="60" width="10" height="10" fill="#0066CC" opacity="0.9" rx="2" />
                      <text x="-75" y="69" fontSize="12" fill="#FFFFFF">Morpho</text>
                      
                      <rect x="10" y="60" width="10" height="10" fill="#0066CC" opacity="0.3" rx="2" />
                      <text x="25" y="69" fontSize="12" fill="#FFFFFF">Others</text>
                      
                      {/* Glowing elements */}
                      <circle cx="-70" cy="-80" r="3" fill="#0066CC" />
                      <circle cx="-30" cy="-60" r="3" fill="#0066CC" />
                      <circle cx="10" cy="-75" r="3" fill="#0066CC" />
                      <circle cx="50" cy="-65" r="3" fill="#0066CC" />
                    </g>
                  </svg>
                   
                  <div className="absolute bottom-8 left-0 right-0 text-center">
                    <p className="text-white text-lg font-semibold">Comparative visualization</p>
                    <p className="text-blue-400 mt-2">Showing the Morpho advantage</p>
                  </div>
                </div>
              </div>
            </div>
          </GlassContainer>
        </div>
      </div>
    </section>
  );
};