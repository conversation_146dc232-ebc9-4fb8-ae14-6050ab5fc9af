const router = require("express").Router();
const controller = require("../../controllers/wooCommerceController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
const setWooCommerceClient = require("../../utils/wooCommerce");

router.get(
  "/products",
  setWooCommerceClient,
  asyncErrorH<PERSON><PERSON>(controller.getAllProducts)
);
router.get(
  "/statistics",
  setWooCommerceClient,
  asyncErrorHandler(controller.getStatistics)
);
router.post(
  "/save-products",
  setWooCommerceClient,
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.saveAllProducts)
);

module.exports = router;
