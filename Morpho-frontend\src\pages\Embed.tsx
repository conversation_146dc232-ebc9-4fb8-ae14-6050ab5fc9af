import React, { useState } from 'react';
import { Save, Code, Eye, Co<PERSON>, Pa<PERSON>, Settings as SettingsIcon, Layout, Box } from 'lucide-react';
import { ViewerSettings, ViewerTemplate } from '../types/viewer';
import ViewerPreview from '../components/embed/ViewerPreview';
import TemplateCard from '../components/embed/TemplateCard';
import ViewerSettingsPanel from '../components/embed/ViewerSettingsPanel';
import CollectionSelector from '../components/embed/CollectionSelector';

const Embed = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<ViewerTemplate | null>(null);
  const [settings, setSettings] = useState<ViewerSettings>({
    autoRotate: true,
    ar: true,
    fullscreen: true,
    zoom: true,
    background: {
      type: 'color',
      value: '#1A1C24',
      opacity: 1
    },
    controls: {
      position: 'bottom',
      layout: 'default',
      visible: true
    },
    lighting: {
      environment: 'neutral',
      intensity: 1,
      shadows: true
    }
  });
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [previewProductId, setPreviewProductId] = useState<string | null>(null);

  const handleSaveTemplate = () => {
    // Save template logic
  };

  const handleCopyEmbed = () => {
    // Copy embed code logic
  };

  const handleApplyToProducts = () => {
    // Apply settings to selected products/collection
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light tracking-wide text-gray-100">Embed Settings</h1>
          <p className="text-gray-400 mt-2">Customize and manage your 3D viewer embeds</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleSaveTemplate}
            className="btn btn-secondary flex items-center gap-2"
          >
            <Save size={18} strokeWidth={1.5} />
            <span>Save Template</span>
          </button>
          <button
            onClick={handleCopyEmbed}
            className="btn btn-secondary flex items-center gap-2"
          >
            <Code size={18} strokeWidth={1.5} />
            <span>Copy Embed Code</span>
          </button>
          <button
            onClick={handleApplyToProducts}
            className="btn btn-primary flex items-center gap-2"
          >
            <Eye size={18} strokeWidth={1.5} />
            <span>Apply to Products</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Main Content */}
        <div className="col-span-8 space-y-6">
          {/* Templates */}
          <div className="card rounded-xl p-6">
            <h2 className="text-lg font-light text-gray-200 mb-4 flex items-center gap-2">
              <Layout size={18} strokeWidth={1.5} className="text-brand-300" />
              Viewer Templates
            </h2>
            <div className="grid grid-cols-3 gap-4">
              <TemplateCard
                name="Default"
                description="Standard 3D viewer layout"
                icon={Box}
                isSelected={!selectedTemplate}
                onClick={() => setSelectedTemplate(null)}
              />
              {/* Add more template cards */}
            </div>
          </div>

          {/* Preview */}
          <div className="card rounded-xl p-6">
            <h2 className="text-lg font-light text-gray-200 mb-4 flex items-center gap-2">
              <Eye size={18} strokeWidth={1.5} className="text-brand-300" />
              Preview
            </h2>
            <ViewerPreview 
              settings={settings} 
              selectedProductId={previewProductId || selectedProducts[0]} 
            />
          </div>
        </div>

        {/* Settings Sidebar */}
        <div className="col-span-4 space-y-6">
          {/* Settings Panel */}
          <div className="card rounded-xl p-6">
            <h2 className="text-lg font-light text-gray-200 mb-4 flex items-center gap-2">
              <SettingsIcon size={18} strokeWidth={1.5} className="text-brand-300" />
              Viewer Settings
            </h2>
            <ViewerSettingsPanel
              settings={settings}
              onChange={setSettings}
            />
          </div>

          {/* Collection/Product Selection */}
          <div className="card rounded-xl p-6">
            <h2 className="text-lg font-light text-gray-200 mb-4 flex items-center gap-2">
              <Box size={18} strokeWidth={1.5} className="text-brand-300" />
              Apply To
            </h2>
            <CollectionSelector
              selectedCollection={selectedCollection}
              selectedProducts={selectedProducts}
              previewProductId={previewProductId}
              onCollectionChange={setSelectedCollection}
              onProductsChange={setSelectedProducts}
              onPreviewChange={setPreviewProductId}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Embed;