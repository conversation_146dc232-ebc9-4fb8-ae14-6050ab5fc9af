const CustomizedMaterialSchema = require("../models/CustomizedMaterial");
const resHandle = require("../utils/responseHandle");
class Controller {
  createCustomizedMaterial = async (req, res) => {
    try {
      const { material_name, material_description, material_iconUrl, model } =
        req.body;
      let newCustomizedMaterial = new CustomizedMaterialSchema({
        material_name: material_name,
        material_description: material_description,
        material_iconUrl: material_iconUrl,
        model: model,
      });
      newCustomizedMaterial.init();
      await newCustomizedMaterial.save();
      resHandle.handleData(
        res,
        200,
        "Customized Material Created Successfully",
        true,
        newCustomizedMaterial,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getAllCustomizedMaterial = async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;
      const totalCount = await CustomizedMaterialSchema.countDocuments();
      if (totalCount == 0)
        return resHandle.handleError(res, 404, `No Customized Material Found`);

      let getAllCustomizedMaterial = await CustomizedMaterialSchema.find({})
        .skip(skip)
        .limit(limit);
      //Check with the front end before changing it
      return res.status(200).json({
        message: "Customized Materials Retrieved Successfully",
        success: true,
        data: getAllCustomizedMaterial,
        total: totalCount,
        page: page,
        perPage: limit,
      });
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getCustomizedMaterialById = async (req, res) => {
    try {
      const { id } = req.body;
      let customizedMaterialToBeRetrieved =
        await CustomizedMaterialSchema.findOne({ _id: id });
      if (!customizedMaterialToBeRetrieved)
        return resHandle.handleError(res, 404, `Customized Material Not Found`);
      resHandle.handleData(
        res,
        200,
        "Customized Material Retrieved Successfully",
        true,
        customizedMaterialToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getCustomizedMaterialWithModel = async (req, res) => {
    try {
      const { id } = req.body;
      let customizedMaterialToBeRetrieved =
        await CustomizedMaterialSchema.findOne({ _id: id }).populate("model");
      if (!customizedMaterialToBeRetrieved)
        return resHandle.handleError(res, 404, `Customized Material Not Found`);
      resHandle.handleData(
        res,
        200,
        "Customized Material Retrieved Successfully",
        true,
        customizedMaterialToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  deleteCustomizedMaterial = async (req, res) => {
    try {
      const { id } = req.body;
      let customizedMaterialToBeDeleted =
        await CustomizedMaterialSchema.findByIdAndDelete(id);
      if (!customizedMaterialToBeDeleted)
        return resHandle.handleError(
          res,
          500,
          `Customized Material Not Found}`,
        );
      resHandle.handleData(
        res,
        200,
        "Customized Material Deleted Successfully",
        true,
      );
      return res.status(200).json({
        message: "Customized Material Deleted Successfully",
        success: true,
      });
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
}
const controller = new Controller();
module.exports = controller;
