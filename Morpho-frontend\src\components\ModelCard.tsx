import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  Calendar,
  DollarSign,
  Edit,
  Trash,
  Eye,
  FolderPlus,
} from "lucide-react";
import { Model3D } from "../types/models";
import { api } from "../services/api";
import DeleteConfirmationModal from "./modals/DeleteConfirmationModal";

interface ModelCardProps {
  model: Model3D;
  viewMode: "grid" | "list";
  onPublish: (id: string) => void;
  onDuplicate: (id: string) => void;
  onDelete?: (id: string) => void;
  onAddToCollection?: (model: Model3D) => void;
  deleteButtonLabel?: string; // New prop for custom delete button label
  onClick?: (model: Model3D) => void; // New prop for handling clicks
}

const ModelCard: React.FC<ModelCardProps> = ({
  model,
  viewMode,
  onPublish,
  onDuplicate,
  onDelete,
  onAddToCollection,
  deleteButtonLabel = "Delete", // Default label
  onClick,
}) => {
  const navigate = useNavigate();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);

  // Handle image error without showing console errors
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.preventDefault();
    e.stopPropagation();
    setImageError(true);
    // Clear the src to prevent further attempts to load
    if (e.currentTarget) {
      e.currentTarget.src = '';
    }
    return false;
  };

  // Navigate to the detail page
  const goToDetailPage = () => {
    if (onClick) {
      // Use the provided onClick handler if available
      onClick(model);
    } else {
      // Fall back to default navigation
      navigate(`/dashboard/library/${model._id}`);
    }
  };

  // IMPORTANT: This function MUST completely isolate the click event
  // and not allow it to trigger the card's click
  const handleDeleteClick = (e: React.MouseEvent) => {
    // These are crucial to prevent navigation
    e.preventDefault();
    e.stopPropagation();

    // Show the delete confirmation modal instead of navigating
    setShowDeleteConfirm(true);

    // Return false to prevent default behavior in any browser
    return false;
  };

  // Handler for Add to Collection button
  const handleAddToCollection = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onAddToCollection) {
      onAddToCollection(model);
    }

    return false;
  };

  // Function to handle actual deletion
  const handleDelete = async () => {
    if (!model._id) {
      setDeleteError("Model ID is missing");
      return;
    }

    setIsDeleting(true);
    setDeleteError(null);

    try {
      // If we're in a collection context, we don't use the deleteModel API
      // Instead we use the parent component's onDelete callback which should handle the collection removal
      if (deleteButtonLabel !== "Delete") {
        if (onDelete) {
          onDelete(model._id);
        }
      } else {
        // This is a true delete operation
        await api.deleteModel(model._id);
        if (onDelete) {
          onDelete(model._id);
        }
      }
      setShowDeleteConfirm(false);
    } catch (err) {
      console.error("Delete error:", err);
      setDeleteError(
        err instanceof Error ? err.message : "Failed to delete model"
      );
    } finally {
      setIsDeleting(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // For the grid view
  if (viewMode === "grid") {
    return (
      <>
        {/* Main card - clickable */}
        <div
          className="card rounded-xl overflow-hidden group cursor-pointer hover:shadow-md transition-shadow"
          onClick={goToDetailPage}
        >
          {/* Model preview */}
          <div className="relative aspect-square bg-dark-200 flex items-center justify-center overflow-hidden">
            {model.imageUrl && !imageError ? (
              <img
                src={model.imageUrl}
                alt={model.name}
                className="w-full h-full object-cover"
                onError={handleImageError}
                loading="lazy"
              />
            ) : (
              <div className="text-gray-600 font-light w-full h-full flex items-center justify-center">
                No preview
              </div>
            )}

            {/* Quick actions overlay - using pointer-events-none on the parent */}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 flex items-center justify-center gap-3 transition-opacity duration-200 pointer-events-none">
              {/* View button */}
              <button
                className="p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors pointer-events-auto"
                onClick={(e) => {
                  // Let this event propagate to trigger navigation
                  // No need to do anything as the card click will handle it
                }}
              >
                <Eye size={18} strokeWidth={1.5} className="text-white" />
              </button>

              {/* Edit button */}
              <button
                className="p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors pointer-events-auto"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent default navigation
                  navigate(`/dashboard/library/${model._id}?edit=true`); // Navigate with edit parameter
                }}
              >
                <Edit size={18} strokeWidth={1.5} className="text-white" />
              </button>

              {/* Add to Collection button */}
              {onAddToCollection && (
                <div
                  className="pointer-events-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  <button
                    className="p-2 rounded-full bg-brand-500/30 backdrop-blur-sm hover:bg-brand-500/50 transition-colors"
                    onClick={handleAddToCollection}
                    onMouseDown={(e) => e.stopPropagation()}
                    onMouseUp={(e) => e.stopPropagation()}
                    onTouchStart={(e) => e.stopPropagation()}
                    onTouchEnd={(e) => e.stopPropagation()}
                  >
                    <FolderPlus
                      size={18}
                      strokeWidth={1.5}
                      className="text-white"
                    />
                  </button>
                </div>
              )}

              {/* Special handling for delete button */}
              <div
                className="pointer-events-auto"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <button
                  className="p-2 rounded-full bg-red-500/30 backdrop-blur-sm hover:bg-red-500/50 transition-colors"
                  onClick={handleDeleteClick}
                  onMouseDown={(e) => e.stopPropagation()}
                  onMouseUp={(e) => e.stopPropagation()}
                  onTouchStart={(e) => e.stopPropagation()}
                  onTouchEnd={(e) => e.stopPropagation()}
                  title={deleteButtonLabel}
                >
                  <Trash size={18} strokeWidth={1.5} className="text-white" />
                </button>
              </div>
            </div>
          </div>

          {/* Card content */}
          <div className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="text-gray-100 font-light text-lg">
                  {model.name}
                </h3>
                <div className="mt-1 flex items-center gap-2 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar size={12} strokeWidth={1.5} />
                    <span>{formatDate(model.updatedAt)}</span>
                  </div>
                  <div>•</div>
                  <div className="flex items-center gap-1">
                    <DollarSign size={12} strokeWidth={1.5} />
                    <span>${model.price}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Delete confirmation modal */}
        {showDeleteConfirm && (
          <DeleteConfirmationModal
            modelName={model.name}
            onClose={() => setShowDeleteConfirm(false)}
            onConfirm={handleDelete}
            isDeleting={isDeleting}
            confirmationText={
              deleteButtonLabel === "Delete"
                ? "Are you sure you want to delete this model? This action cannot be undone."
                : "Are you sure you want to remove this model from the collection? The model will still be available in your library."
            }
            confirmButtonText={deleteButtonLabel}
          />
        )}

        {/* No Product Update Modal - now integrated in product detail page */}

        {/* Delete error toast */}
        {deleteError && (
          <div className="fixed bottom-4 right-4 bg-red-500/90 text-white px-4 py-3 rounded-lg shadow-lg z-50">
            {deleteError}
          </div>
        )}
      </>
    );
  }

  // List view
  return (
    <>
      <div
        className="card rounded-lg p-4 hover:bg-dark-200/50 transition-colors cursor-pointer"
        onClick={goToDetailPage}
      >
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-dark-200 rounded-lg flex items-center justify-center overflow-hidden">
            {model.imageUrl && !imageError ? (
              <img
                src={model.imageUrl}
                alt={model.name}
                className="w-full h-full object-cover"
                onError={handleImageError}
                loading="lazy"
              />
            ) : (
              <div className="text-gray-600 text-xs font-light">No preview</div>
            )}
          </div>

          <div className="flex-1">
            <h3 className="text-gray-100 font-light">{model.name}</h3>
            <div className="mt-1 flex items-center gap-4 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <Calendar size={12} strokeWidth={1.5} />
                <span>Updated {formatDate(model.updatedAt)}</span>
              </div>
              <div className="flex items-center gap-1">
                <DollarSign size={12} strokeWidth={1.5} />
                <span>${model.price}</span>
              </div>
            </div>
          </div>

          {/* Special handling for the action buttons in list view */}
          <div
            className="flex items-center gap-2"
            onClick={(e) => e.stopPropagation()} // Prevent card click when clicking the actions area
          >
            <button
              className="btn btn-icon btn-secondary"
              onClick={(e) => {
                // Allow click to propagate to card for navigation
              }}
            >
              <Eye size={16} strokeWidth={1.5} />
            </button>

            {/* Add to Collection button for list view */}
            {onAddToCollection && (
              <button
                onClick={handleAddToCollection}
                className="btn btn-icon btn-secondary"
              >
                <FolderPlus size={16} strokeWidth={1.5} />
              </button>
            )}

            <button
              className="btn btn-icon btn-secondary"
              onClick={(e) => {
                e.stopPropagation(); // Prevent card click
                navigate(`/dashboard/library/${model._id}?edit=true`); // Navigate with edit parameter
              }}
            >
              <Edit size={16} strokeWidth={1.5} />
            </button>
            <div onClick={(e) => e.stopPropagation()}>
              <button
                onClick={handleDeleteClick}
                className="btn btn-icon bg-red-500/10 hover:bg-red-500/20 text-red-400"
                title={deleteButtonLabel}
              >
                <Trash size={16} strokeWidth={1.5} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Delete confirmation modal */}
      {showDeleteConfirm && (
        <DeleteConfirmationModal
          modelName={model.name}
          onClose={() => setShowDeleteConfirm(false)}
          onConfirm={handleDelete}
          isDeleting={isDeleting}
          confirmationText={
            deleteButtonLabel === "Delete"
              ? "Are you sure you want to delete this model? This action cannot be undone."
              : "Are you sure you want to remove this model from the collection? The model will still be available in your library."
          }
          confirmButtonText={deleteButtonLabel}
        />
      )}

      {/* Delete error toast */}
      {deleteError && (
        <div className="fixed bottom-4 right-4 bg-red-500/90 text-white px-4 py-3 rounded-lg shadow-lg z-50">
          {deleteError}
        </div>
      )}

      {/* No Product Update Modal - now integrated in product detail page */}
    </>
  );
};

export default ModelCard;
