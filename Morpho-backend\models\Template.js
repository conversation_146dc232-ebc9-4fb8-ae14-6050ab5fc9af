const mongoose = require("mongoose");

const templateSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    id: {
      type: mongoose.Types.ObjectId,
      ref: "Project",
    },
    description: {
      type: String,
      required: true,
    },
    previewUrl: {
      type: String,
      required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    imageUrl: {
      type: String,
      required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
  },
  { timestamps: true, collection: "templates" }
);

const templateModel = mongoose.model("Template", templateSchema);
module.exports = templateModel;
