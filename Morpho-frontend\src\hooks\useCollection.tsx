import { useState, useEffect, useCallback } from "react";
import { useAuth } from "./useAuth";
import { storage } from "../utils/storage";
import { Model3D } from "../types/models";

export interface Collection {
  _id: string;
  name: string;
  description: string;
  products: Model3D[] | string[];
  user: string;
  createdAt: string;
  updatedAt: string;
}

export const useCollections = () => {
  const API_URL = "https://api.modularcx.link/morpho";
  const { isAuthenticated } = useAuth();
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedCollection, setSelectedCollection] =
    useState<Collection | null>(null);
  const [loading, setLoading] = useState(false);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCollections = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      setError(null);

      const token = storage.getToken();

      const response = await fetch(`${API_URL}/collection/get-collections`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
      });

      if (!response.ok) {
        const errorText = await response.text();

        let errorMessage = "Failed to fetch collections";

        try {
          if (errorText.trim().startsWith("{")) {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.message || errorMessage;
          }
        } catch (e) {
          // Not JSON
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      setCollections(data.collections || data.data || []);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while fetching collections"
      );
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Get collection details
  // In useCollections.tsx
  const getCollectionDetails = useCallback(async (collectionId: string) => {
    try {
      setDetailsLoading(true);
      setError(null);

      const token = storage.getToken();

      const response = await fetch(
        `${API_URL}/collection/get-collection/${collectionId}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = "Failed to fetch collection details";

        try {
          if (errorText.trim().startsWith("{")) {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.message || errorMessage;
          }
        } catch (e) {
          // Not JSON
          console.log(e);
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setSelectedCollection(data.collection || data.data);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "An error occurred while fetching collection details"
      );
      // Important: Propagate the error so we can catch it in the library component
      throw err;
    } finally {
      setDetailsLoading(false);
    }
  }, []);

  // Clear selected collection
  const clearSelectedCollection = useCallback(() => {
    setSelectedCollection(null);
  }, []);

  // Create a new collection
  const createCollection = useCallback(
    async (collectionData: { name: string; description: string }) => {
      if (!isAuthenticated) {
        throw new Error("Authentication required");
      }

      try {
        const token = storage.getToken();

        const response = await fetch(
          `${API_URL}/collection/create-collection`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(collectionData),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();

          let errorMessage = "Failed to create collection";

          try {
            if (errorText.trim().startsWith("{")) {
              const errorData = JSON.parse(errorText);
              errorMessage = errorData.message || errorMessage;
            }
          } catch (e) {
            // Not JSON
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();

        // Refresh collections after creating a new one
        fetchCollections();

        return data;
      } catch (error) {
        throw error;
      }
    },
    [isAuthenticated, fetchCollections]
  );

  // Add products to a collection
  const addProductsToCollection = useCallback(
    async (collectionId: string, productIds: string[]) => {
      if (!isAuthenticated) {
        throw new Error("Authentication required");
      }

      try {
        const token = storage.getToken();

        const response = await fetch(`${API_URL}/collection/add-products`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
          body: JSON.stringify({
            collectionId,
            productIds,
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();

          let errorMessage = "Failed to add products to collection";

          try {
            if (errorText.trim().startsWith("{")) {
              const errorData = JSON.parse(errorText);
              errorMessage = errorData.message || errorMessage;
            }
          } catch (e) {
            // Not JSON
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        throw error;
      }
    },
    [isAuthenticated]
  );

  // Fetch collections on mount and when authentication status changes
  useEffect(() => {
    if (isAuthenticated) {
      fetchCollections();
    }
  }, [isAuthenticated, fetchCollections]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    collections,
    selectedCollection,
    loading,
    detailsLoading,
    error,
    fetchCollections,
    getCollectionDetails,
    clearSelectedCollection,
    createCollection,
    addProductsToCollection,
    clearError,
  };
};
