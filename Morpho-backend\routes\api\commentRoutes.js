const router = require("express").Router();
const controller = require("../../controllers/commentController");
const { validateCommentSchema } = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router
  .route("/create-comment")
  .post(validateCommentSchema, asyncError<PERSON><PERSON><PERSON>(controller.createComment));
router.get("/get-all-comments", async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.getAllComments));
router.get("/get-comment-by-id", async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.getCommentById));
router.delete(
  "/delete-comment-by-id",
  async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.deleteCommentById)
);
module.exports = router;
