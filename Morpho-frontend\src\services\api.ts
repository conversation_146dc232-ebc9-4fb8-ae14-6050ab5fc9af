import { storage } from "../utils/storage";
import type { Model3D } from "../types/models";
import type { GenerateLinkParams } from "../types/viewer";
import { API_URL } from "../config/apiConfig";
import type { SignupCredentials } from "../types/auth";

export const api = {
  login: async (email: string, password: string) => {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();

    if (!response.ok || !data.success) {
      throw new Error(data.message || "Invalid credentials");
    }

    if (!data.accessToken) {
      throw new Error("No access token received");
    }

    storage.setToken(data.accessToken);

    // Store the user ID for webhook simulation
    if (data.data && data.data._id) {
      storage.setUserId(data.data._id);
    }

    return data;
  },

  signup: async (credentials: SignupCredentials) => {
    try {
      const response = await fetch(`${API_URL}/auth/signup`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      // The API returns a status field instead of success for signup
      if (!response.ok) {
        throw new Error(data.message || "Signup failed");
      }

      // Check if we have the user data in the response
      if (data.data && data.data.userId && data.data.email) {
        return {
          success: true,
          message: data.message || "Verification OTP email sent",
          data: {
            userId: data.data.userId,
            email: data.data.email,
          },
        };
      } else {
        // If we don't have the user data, throw an error
        throw new Error("User data not found in response. Please try again.");
      }
    } catch (error) {
      console.error("Signup error:", error);
      throw error;
    }
  },

  verifyOTP: async (userId: string, otp: string) => {
    try {
      const response = await fetch(`${API_URL}/otp/verifyOTP`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId, otp }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "OTP verification failed");
      }

      // If verification is successful and we get a token, store it
      if (data.accessToken) {
        storage.setToken(data.accessToken);

        // Store the user ID if available
        if (data.data && data.data._id) {
          storage.setUserId(data.data._id);
        }
      }

      return {
        success: true,
        message: data.message || "Email verified successfully!",
        data: data.data || null,
        accessToken: data.accessToken || null,
      };
    } catch (error) {
      console.error("OTP verification error:", error);
      throw error;
    }
  },

  resendOTP: async (userId: string, email: string) => {
    try {
      const response = await fetch(`${API_URL}/otp/resendOTP`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId, email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to resend OTP");
      }

      return {
        success: true,
        message: data.message || "OTP resent successfully!",
        data: data.data || null,
      };
    } catch (error) {
      console.error("Resend OTP error:", error);
      throw error;
    }
  },

  // New method for uploading 3D models
  uploadModel: async (formData: FormData) => {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    try {
      // Note: We're using localhost here specifically for this endpoint
      // as requested in your instructions
      const response = await fetch(`${API_URL}/glbmodel/create-glb`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          // Note: Do NOT set 'Content-Type': 'multipart/form-data' when using FormData
          // The browser will set the correct boundary value automatically
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Upload failed with status: ${response.status}`
        );
      }

      const data = await response.json();
      // Check if we have the expected response structure
      if (data && data.data) {
        return {
          success: true,
          data: data.data,
          message: data.message || "Upload successful",
        };
      }
      return data;
    } catch (error) {
      console.error("Model upload error:", error);
      throw error;
    }
  },

  getModels: async (): Promise<{
    success: boolean;
    data: Model3D[];
    error?: string;
  }> => {
    const token = storage.getToken();
    if (!token) {
      return {
        success: false,
        data: [],
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(
        `${API_URL}/glbmodel/get-models-by-clientId`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // First try to parse the response as JSON
      let data;
      try {
        const jsonResponse = await response.json();
        data = jsonResponse;
      } catch (parseError) {
        return {
          success: false,
          data: [],
          error: "Invalid server response",
        };
      }

      if (!response.ok) {
        return {
          success: false,
          data: [],
          error: data.message || `Server error: ${response.status}`,
        };
      }

      // Validate the response data structure
      if (!Array.isArray(data.data)) {
        return {
          success: false,
          data: [],
          error: "Invalid data format received from server",
        };
      }
      return {
        success: true,
        data: data.data || [],
      };
    } catch (err) {
      console.error("API Error:", err);
      return {
        success: false,
        data: [],
        error:
          err instanceof Error
            ? err.message
            : "Failed to communicate with the server",
      };
    }
  },

  generateLink: async (params: GenerateLinkParams) => {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    try {
      const response = await fetch(`${API_URL}/publish/generate-link`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error("Failed to generate link");
      }

      return response.json();
    } catch (error) {
      console.error("Generate link error:", error);
      throw error;
    }
  },

  getModel: async (id: string) => {
    const token = storage.getToken();
    if (!token) {
      return {
        success: false,
        data: null,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(`${API_URL}/glbmodel/get-glb-model-by-id`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          data: null,
          error:
            errorData.message || `Failed to fetch model: ${response.status}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data: data.data,
      };
    } catch (err) {
      console.error("API Error:", err);
      return {
        success: false,
        data: null,
        error: err instanceof Error ? err.message : "Failed to fetch model",
      };
    }
  },

  deleteModel: async (id: string) => {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    try {
      // Using the localhost endpoint as specified
      const response = await fetch(`${API_URL}/glbmodel/delete-by-id/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Deletion failed with status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Model deletion error:", error);
      throw error;
    }
  },

  // Add this function to your api.ts file
  addModelToCollection: async (collectionId: string, modelId: string) => {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    try {
      const response = await fetch(`${API_URL}/collection/add-products`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
        body: JSON.stringify({
          collectionId,
          productIds: [modelId], // We're adding a single model, so wrap it in an array
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = "Failed to add model to collection";

        try {
          if (errorText.trim().startsWith("{")) {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.message || errorMessage;
          }
        } catch (e) {
          // Not JSON
        }

        throw new Error(errorMessage);
      }

      return await response.json();
    } catch (error) {
      console.error("Error adding model to collection:", error);
      throw error;
    }
  },

  removeModelFromCollection: async (
    collectionId: string,
    productId: string
  ) => {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    try {
      const response = await fetch(`${API_URL}/collection/remove-product`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
        body: JSON.stringify({
          collectionId,
          productId,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = "Failed to remove model from collection";

        try {
          if (errorText.trim().startsWith("{")) {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.message || errorMessage;
          }
        } catch (e) {
          // Not JSON
        }

        throw new Error(errorMessage);
      }

      return await response.json();
    } catch (error) {
      console.error("Error removing model from collection:", error);
      throw error;
    }
  },

  deleteCollection: async (collectionId: string) => {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    try {
      const response = await fetch(
        `${API_URL}/collection/delete-collection/${collectionId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = "Failed to delete collection";

        try {
          if (errorText.trim().startsWith("{")) {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.message || errorMessage;
          }
        } catch (e) {
          // Not JSON
          console.log(e);
        }

        throw new Error(errorMessage);
      }

      return await response.json();
    } catch (error) {
      console.error("Error deleting collection:", error);
      throw error;
    }
  },

  // Compress a 3D model using the compression API
  compressModel: async (modelUrl: string): Promise<Blob> => {
    try {
      // First, fetch the model file from the URL
      const modelResponse = await fetch(modelUrl);
      if (!modelResponse.ok) {
        throw new Error(`Failed to fetch model file: ${modelResponse.status}`);
      }

      // Get the model file as a blob
      const modelBlob = await modelResponse.blob();

      // Create a File object from the blob
      const fileName = modelUrl.split("/").pop() || "model.glb";
      const modelFile = new File([modelBlob], fileName, {
        type: "model/gltf-binary",
      });

      // Create FormData for the compression API
      const formData = new FormData();
      formData.append("uploadedFile", modelFile);
      formData.append("compressionLevel", "7");
      formData.append("extPostCompression", "glb");

      // Call the compression API
      const compressionResponse = await fetch(
        "https://api.modularcx.link/converter-api/upload",
        {
          method: "POST",
          body: formData,
        }
      );

      if (!compressionResponse.ok) {
        throw new Error(`Compression failed: ${compressionResponse.status}`);
      }

      // Return the compressed model as a blob
      return await compressionResponse.blob();
    } catch (error) {
      console.error("Model compression error:", error);
      throw error;
    }
  },

  // Update a model with a new compressed file
  updateModelWithCompressedFile: async (
    productId: string,
    compressedFile: Blob
  ): Promise<Model3D> => {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    try {
      // Create a File object from the blob
      const fileName = `compressed-model-${Date.now()}.glb`;
      const modelFile = new File([compressedFile], fileName, {
        type: "model/gltf-binary",
      });

      // Create FormData for the update API
      const formData = new FormData();
      formData.append("url", modelFile);

      // Call the update API
      const response = await fetch(
        `${API_URL}/glbmodel/update-product/${productId}`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            // Don't set Content-Type for FormData
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Update failed with status: ${response.status}`
        );
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error("Model update error:", error);
      throw error;
    }
  },
};
