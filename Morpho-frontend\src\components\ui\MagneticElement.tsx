import React from 'react';

interface MagneticElementProps {
  children: React.ReactNode;
  className?: string;
  strength?: number;
}

export const MagneticElement: React.FC<MagneticElementProps> = ({
  children,
  className = '',
}) => {
  // Removed all magnetic functionality for better performance
  // This now just renders children directly without any effects
  
  return (
    <div className={`${className}`}>
      {children}
    </div>
  );
};