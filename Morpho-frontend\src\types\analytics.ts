export interface AnalyticsData {
  views: {
    total: number;
    daily: number[];
    byDevice: {
      desktop: number;
      mobile: number;
      tablet: number;
    };
  };
  models: {
    total: number;
    published: number;
    drafts: number;
    byCategory: Record<string, number>;
  };
  conversions: {
    total: number;
    rate: number;
    byModel: Array<{
      modelId: string;
      name: string;
      views: number;
      conversions: number;
      rate: number;
    }>;
  };
  requests: {
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
  };
  activity: Array<{
    id: string;
    type: 'model_upload' | 'model_update' | 'request_status' | 'integration';
    title: string;
    description: string;
    timestamp: string;
    metadata?: Record<string, any>;
  }>;
}

export interface ModelAnalytics {
  views: number;
  downloads: number;
  conversions: number;
  avgTimeSpent: string;
  viewsByDevice: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  dailyViews: number[];
}