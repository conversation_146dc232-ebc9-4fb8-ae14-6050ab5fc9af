import React, { useEffect, useRef, useCallback } from 'react';

interface Particle {
  x: number;
  y: number;
  size: number;
  vx: number;
  vy: number;
  color: string;
  alpha: number;
  fadeIn: number;
}

interface ParticleFieldProps {
  className?: string;
  particleCount?: number;
  particleSize?: number;
  color?: string;
  speed?: number;
  blur?: number;
  interactive?: boolean;
  depth?: boolean;
}

export const ParticleField: React.FC<ParticleFieldProps> = ({
  className = '',
  particleCount = 30, // Moderate number of particles - not too few, not too many
  particleSize = 2.5,
  color = '#0066CC',
  speed = 0.2, // Moderate speed
  blur = 0, // No blur for better performance
  interactive = true, // Re-enable interactivity with optimization
  depth = true, // Re-enable depth with optimization
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particles = useRef<Particle[]>([]);
  const mouse = useRef({ x: 0, y: 0, radius: 100 }); // Reduced radius for better performance
  const rafId = useRef<number>(0);
  const animationActive = useRef(true);
  const frameCountRef = useRef(0);
  const isVisibleRef = useRef(false);
  
  // Create particles with optimized settings
  const initParticles = useCallback((canvas: HTMLCanvasElement) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear existing particles
    particles.current = [];
    
    // Create moderate number of particles
    const count = Math.min(particleCount, 40); // Cap at 40 regardless of input
    
    for (let i = 0; i < count; i++) {
      const size = depth 
        ? Math.random() * particleSize + 0.5 
        : particleSize;
      
      particles.current.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size,
        vx: (Math.random() - 0.5) * speed,
        vy: (Math.random() - 0.5) * speed,
        color,
        alpha: depth ? 0.3 + Math.random() * 0.5 : 0.7, // Vary alpha for depth effect
        fadeIn: Math.random() * 100 // For staggered appearance
      });
    }
  }, [particleCount, particleSize, color, speed, depth]);

  // Performance-optimized animation function
  const animate = useCallback(() => {
    if (!animationActive.current || !isVisibleRef.current) {
      rafId.current = requestAnimationFrame(animate);
      return;
    }
    
    const canvas = canvasRef.current;
    if (!canvas) {
      rafId.current = requestAnimationFrame(animate);
      return;
    }

    const ctx = canvas.getContext('2d', { alpha: true });
    if (!ctx) {
      rafId.current = requestAnimationFrame(animate);
      return;
    }

    // Throttle renders for better performance (every 2 frames)
    frameCountRef.current++;
    if (frameCountRef.current % 2 === 0) {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.current.forEach((particle, index) => {
        // Fade in effect for staggered appearance
        if (particle.fadeIn > 0) {
          particle.fadeIn -= 1;
          particles.current[index] = particle;
          return;
        }
        
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Boundary checks - wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // Interactive mouse effects (simplified)
        if (interactive) {
          const dx = particle.x - mouse.current.x;
          const dy = particle.y - mouse.current.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < mouse.current.radius) {
            const angle = Math.atan2(dy, dx);
            const pushFactor = (mouse.current.radius - distance) / mouse.current.radius * 0.5;
            
            particle.x += Math.cos(angle) * pushFactor;
            particle.y += Math.sin(angle) * pushFactor;
          }
        }

        // Draw particle without shadows for performance
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        
        // Apply color with alpha for depth effect
        const alphaHex = Math.floor(particle.alpha * 255).toString(16).padStart(2, '0');
        ctx.fillStyle = `${particle.color}${alphaHex}`;
        ctx.fill();
        
        particles.current[index] = particle;
      });
    }
    
    // Continue animation loop
    rafId.current = requestAnimationFrame(animate);
  }, [interactive]);

  // Throttled mouse move handler
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!interactive || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    mouse.current.x = e.clientX - rect.left;
    mouse.current.y = e.clientY - rect.top;
  }, [interactive]);

  // Intersection observer to only animate when visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        isVisibleRef.current = entry.isIntersecting;
      },
      { threshold: 0.1 }
    );
    
    if (canvasRef.current) {
      observer.observe(canvasRef.current);
    }
    
    return () => {
      if (canvasRef.current) {
        observer.unobserve(canvasRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Setup
    const resizeCanvas = () => {
      if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        initParticles(canvas);
      }
    };
    
    // Initialize
    resizeCanvas();
    animationActive.current = true;
    isVisibleRef.current = true;
    
    // Start animation loop
    rafId.current = requestAnimationFrame(animate);

    // Throttled event listeners
    let resizeTimeout: number;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = window.setTimeout(() => {
        resizeCanvas();
      }, 200);
    };
    
    // Only add mouse move listener when interactive
    if (interactive) {
      let mouseMoveTimeout: number;
      const throttledMouseMove = (e: MouseEvent) => {
        clearTimeout(mouseMoveTimeout);
        mouseMoveTimeout = window.setTimeout(() => {
          handleMouseMove(e);
        }, 16); // ~ 60 fps
      };
      
      window.addEventListener('mousemove', throttledMouseMove, { passive: true });
    }
    
    window.addEventListener('resize', handleResize, { passive: true });

    // Cleanup
    return () => {
      animationActive.current = false;
      window.removeEventListener('resize', handleResize);
      
      if (interactive) {
        window.removeEventListener('mousemove', handleMouseMove);
      }
      
      cancelAnimationFrame(rafId.current);
    };
  }, [initParticles, animate, handleMouseMove, interactive]);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 z-0 ${className}`}
    />
  );
};

export default ParticleField;