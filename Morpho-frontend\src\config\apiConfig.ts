/**
 * API Configuration
 *
 * This file contains centralized configuration for API endpoints.
 * When deploying to different environments, only update the values here.
 */

// Determine the environment
const isDevelopment =
  process.env.NODE_ENV === "development" ||
  window.location.hostname === "localhost" ||
  window.location.hostname === "127.0.0.1";

// Set the base API URL to always use production
export const API_URL = "https://api.modularcx.link/morpho";
export const API_BASE_URL = "https://api.modularcx.link";

// Export other API-related constants if needed
export const API_TIMEOUT = 30000; // 30 seconds
