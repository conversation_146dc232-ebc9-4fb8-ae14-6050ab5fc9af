const projectSchema = require("../models/Project");
const mongoose = require("mongoose");
const assetSchema = require("../models/AssetLibrary");
const User = require("../models/User");
const Role = require("../models/Role");
const { v4: uuidv4 } = require("uuid");
const jwt = require("jsonwebtoken");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
class Controller {
  createProject = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate required fields
    if (!req.body.name) {
      throw new expressError(
        "Project name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Create new project
    const newProject = await projectSchema({
      ...req.body,
      client: req.user,
    });

    // Save project to database
    await newProject.save();

    // Create asset library for the project
    const newAsset = await assetSchema({
      serial_number: uuidv4(),
      project: newProject._id,
    });

    // Save asset library to database
    await newAsset.save();

    // Return success response
    return res.status(201).json({
      message: "Project created successfully",
      success: true,
      project: newProject,
      asset: newAsset,
    });
  };
  getProjectById = async (req, res) => {
    // Get project ID from request body
    const { _id } = req.body;

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!_id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Fetch project with all related data using aggregation pipeline
      const projectFound = await projectSchema.aggregate([
        { $match: { _id: mongoose.Types.ObjectId(_id) } },
        {
          $lookup: {
            from: "assetlibraries",
            localField: "_id",
            foreignField: "project",
            as: "assetLibrary",
          },
        },
        {
          $unwind: {
            path: "$assetLibrary",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "client",
            foreignField: "_id",
            as: "client",
          },
        },
        {
          $unwind: {
            path: "$client",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "artists",
            foreignField: "_id",
            as: "artists",
          },
        },
        {
          $lookup: {
            from: "templates",
            localField: "template",
            foreignField: "_id",
            as: "template",
          },
        },
      ]);

      // Check if project exists
      if (!projectFound || projectFound.length === 0) {
        throw new expressError(
          "Project not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Project retrieved successfully",
        true,
        projectFound
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid project ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  deleteProject = async (req, res) => {
    // Get project ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete project
    const deletedProject = await projectSchema.findByIdAndDelete({
      _id: id,
    });

    // Check if project exists
    if (!deletedProject) {
      throw new expressError(
        "Project not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(res, 200, "Project deleted successfully", true);
  };
  getAllProjects = async (req, res) => {
    // Get authorization token
    const cookie = req.headers["authorization"];

    // Validate authorization
    if (!cookie) {
      throw new expressError(
        "Authorization token is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Extract token
    const token = cookie.split(" ")[1];

    // Decode token
    const result = jwt.decode(token);

    // Validate token
    if (!result) {
      throw new expressError(
        "Invalid authorization token",
        401,
        expressError.CODES.INVALID_CREDENTIALS
      );
    }

    // Get role from token
    const role = result.role;

    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;

    // Build query based on role
    let query = {};
    if (role == "Designer") {
      const userId = result.userId;
      query = { artists: userId };
    }

    // Get total count
    const totalProjectsCount = await projectSchema.countDocuments(query);

    // Calculate total pages
    const totalPages = Math.ceil(totalProjectsCount / pageSize);

    // Fetch projects with pagination and populate references
    const projectsToBeRetrieved = await projectSchema
      .find(query)
      .sort({ createdAt: -1 }) // Sort by latest createdAt in descending order
      .populate("artists")
      .populate("template")
      .skip((page - 1) * pageSize)
      .limit(pageSize);

    // Check if projects exist
    if (!projectsToBeRetrieved || projectsToBeRetrieved.length === 0) {
      throw new expressError(
        "No projects found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return paginated response
    return res.status(200).json({
      message: "Projects retrieved successfully",
      success: true,
      data: projectsToBeRetrieved,
      page: page,
      pageSize: pageSize,
      totalPages: totalPages,
      totalItems: totalProjectsCount,
    });
  };
  getProjectsByClientId = async (req, res) => {
    try {
      const { client } = req.body; // Client ID from the request body
      let userToBeFound = await User.findById(req.user);
      let roleToBeFound = await Role.findById(userToBeFound.role);

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      let pipeline = [
        {
          $lookup: {
            from: "assetlibraries",
            localField: "_id",
            foreignField: "project",
            as: "assetLibrary",
          },
        },
        {
          $unwind: {
            path: "$assetLibrary",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "client",
            foreignField: "_id",
            as: "client",
            pipeline: [
              { $project: { username: 1, firstname: 1, lastname: 1 } }, // Select specific fields
            ],
          },
        },
        {
          $unwind: {
            path: "$client",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "artists",
            foreignField: "_id",
            as: "artists",
            pipeline: [
              { $project: { username: 1, firstname: 1, lastname: 1 } }, // Select specific fields
            ],
          },
        },
        {
          $lookup: {
            from: "templates",
            localField: "template",
            foreignField: "_id",
            as: "template",
          },
        },
        {
          $unwind: {
            path: "$template",
            preserveNullAndEmptyArrays: true,
          },
        },
        { $sort: { createdAt: -1 } }, // Sort by creation date in descending order
      ];

      // Role-based filtering
      if (roleToBeFound.lead || roleToBeFound.role == "Admin") {
        // Admins and Leads can see all projects
      }

      if (roleToBeFound.role == "Client") {
        const client = req.user; // Client ID
        pipeline.unshift({
          $match: { client: mongoose.Types.ObjectId(client) },
        });
      }

      if (roleToBeFound.role == "Designer") {
        pipeline.unshift({
          $match: {
            $or: [
              { client: mongoose.Types.ObjectId(client) },
              { artists: mongoose.Types.ObjectId(req.user) },
            ],
          },
        });
      }

      // Count total projects
      let totalCount = await projectSchema.aggregate([
        ...pipeline,
        { $count: "totalCount" },
      ]);
      totalCount = totalCount.length > 0 ? totalCount[0].totalCount : 0;

      if (totalCount === 0)
        return res.status(404).json({ message: "No Projects Found" });

      // Pagination
      pipeline.push({ $skip: skip }, { $limit: limit });

      const projectsToBeRetrieved = await projectSchema
        .aggregate(pipeline)
        .exec();

      // Send response
      return res.status(200).json({
        message: "Projects Retrieved Successfully",
        success: true,
        data: projectsToBeRetrieved,
        total: totalCount,
        page: page,
        perPage: limit,
      });
    } catch (error) {
      return res.status(500).json({
        message: `Error occurred: ${error.message}`,
        success: false,
      });
    }
  };

  getProjectsByArtistId = async (req, res) => {
    try {
      const { artist } = req.body;
      let projectsToBeRetrieved = await projectSchema
        .find({ artists: artist })
        .populate("client")
        .populate("artists")
        .populate("project_template");

      if (!projectsToBeRetrieved)
        return resHandle.handleError(res, 404, "No Projects to this client");
      resHandle.handleData(
        res,
        404,
        "Projects Retrieved Successfully",
        true,
        projectsToBeRetrieved
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
  getProjectWithArtists = async (req, res) => {
    try {
      const { id } = req.body;
      let projectToBeRetrieved = await projectSchema
        .findOne({ _id: id })
        .populate("artists");
      if (!projectToBeRetrieved)
        return resHandle.handleError(res, 404, "Project Not Found");
      resHandle.handleData(
        res,
        200,
        "Project Retrieved Successfully",
        true,
        projectToBeRetrieved
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getProjectWithTemplate = async (req, res) => {
    try {
      const { id } = req.body;
      let projectToBeRetrieved = await projectSchema
        .findOne({ _id: id })
        .populate("project_template");
      if (!projectToBeRetrieved)
        return resHandle.handleError(res, 404, "Project Not Found");
      resHandle.handleData(
        res,
        200,
        "Project Retrieved Successfully",
        true,
        projectToBeRetrieved
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getProjectWithAsset = async (req, res) => {
    try {
      const { id } = req.body;
      let projectWithAsset = await projectSchema.findOne({ _id: id });
      if (!projectWithAsset)
        return resHandle.handleError(res, 404, "Project Not Found");
      resHandle.handleData(
        res,
        200,
        "Project Retrieved Successfully",
        true,
        projectWithAsset
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getProjectWithAllInformations = async (req, res) => {
    try {
      const { id } = req.body;
      let projectWithAllInformations = await projectSchema
        .findOne({ _id: id })
        .populate("client")
        .populate("artists")
        .populate("project_template");
      resHandle.handleData(
        res,
        200,
        "Project Retrieved Successfully",
        true,
        projectWithAllInformations
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };

  //Check who can update the project
  updateProject = async (req, res) => {
    try {
      const { _id } = req.body;
      let projectToBeUpdated = await projectSchema.findByIdAndUpdate(_id, {
        $set: req.body,
      });
      if (!projectToBeUpdated)
        return resHandle.handleError(res, 404, "Project Not Found");

      let projectToBeRetrieved = await projectSchema.findById(_id);

      resHandle.handleData(
        res,
        200,
        "Project Updated Successfully",
        true,
        projectToBeRetrieved
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
}

let controller = new Controller();
module.exports = controller;
