const router = require("express").Router();
const controller = require("../../controllers/materialsController");
const { validateMaterialsSchema } = require("../../validation/middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
// router
//   .route("/create-material")
//   .post(validateMaterialsSchema, controller.createMaterials);
router.get("/get-all-materials", asyncError<PERSON><PERSON><PERSON>(controller.getAllMaterials));
router.get(
  "/get-materials-by-userid",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getMaterialByUserId)
);
router.post(
  "/get-material-by-active",
  asyncError<PERSON><PERSON><PERSON>(controller.getMaterialByActive)
);
router.post(
  "/get-material-by-id",
  asyncError<PERSON><PERSON><PERSON>(controller.getMaterialById)
);
router.put(
  "/add-materials-by-id",
  asyncError<PERSON><PERSON><PERSON>(controller.addMaterialsById)
);
router.put("/update-materialname-by-id", controller.updateMaterialNameById);

module.exports = router;
