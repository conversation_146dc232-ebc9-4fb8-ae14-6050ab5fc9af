import React, { useRef, useEffect,useState } from 'react';
import { GlassContainer } from '../ui/GlassContainer';
import { Button } from '../ui/Button';
import { useInView } from '../../hooks/useInView';
import { H1, Lead, Gradient, HighlightedText, Subtle } from '../ui/Typography';
import { useNavigate } from 'react-router-dom';
import Scene3d from "../3d/Scene3d.tsx";

// Interface for the lightweight particles
interface MiniParticle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  opacity: number;
  color: string;
}

export const Hero: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<MiniParticle[]>([]);
  const rafIdRef = useRef<number | null>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  
  // Initialize the mini particles
  useEffect(() => {
    const initParticles = () => {
      if (!gridRef.current || !canvasRef.current) return;
      
      const canvas = canvasRef.current;
      const grid = gridRef.current;
      canvas.width = grid.offsetWidth;
      canvas.height = grid.offsetHeight;
      
      const particleCount = 12; // Keep count low for performance
      particlesRef.current = [];
      
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 1.5 + 0.5, // Very small particles
          speedX: (Math.random() - 0.5) * 0.3, // Slow movement
          speedY: (Math.random() - 0.5) * 0.3,
          opacity: Math.random() * 0.5 + 0.2,
          color: i % 3 === 0 ? '#0066CC' : (i % 3 === 1 ? '#9747FF' : '#26D9C2')
        });
      }
    };
    
    const handleResize = () => {
      if (!gridRef.current || !canvasRef.current) return;
      canvasRef.current.width = gridRef.current.offsetWidth;
      canvasRef.current.height = gridRef.current.offsetHeight;
      initParticles();
    };
    
    initParticles();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Animate the mini particles
  useEffect(() => {
    const animate = () => {
      if (!canvasRef.current) return;
      
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particlesRef.current.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        
        // Set transparency
        ctx.fillStyle = `${particle.color}${Math.floor(particle.opacity * 255).toString(16).padStart(2, '0')}`;
        ctx.fill();
      });
      
      rafIdRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
    };
  }, []);
    const navigate = useNavigate();
  return (
    <section 
      ref={sectionRef}
      className="relative min-h-screen flex items-center justify-center pt-24 pb-16 overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 bg-[#0A0A10] overflow-hidden">
        <div className="absolute top-1/4 -left-1/4 w-1/2 h-1/2 bg-[#0066CC]/10 rounded-full filter blur-[100px]"></div>
        <div className="absolute bottom-1/4 -right-1/4 w-1/2 h-1/2 bg-[#9747FF]/10 rounded-full filter blur-[100px]"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div ref={gridRef} className="grid md:grid-cols-2 gap-12 items-center relative">
          {/* Canvas for lightweight particles specific to the grid */}
          <canvas ref={canvasRef} className="absolute inset-0 pointer-events-none" />
          
          <div 
            className={`transition-all duration-700 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <H1>
              Transform Your Shopify Store with{' '}
              <Gradient>3D Products</Gradient>
            </H1>
            
            <Lead>
              The complete 3D product viewer and management platform built specifically for Shopify e-commerce stores.
            </Lead>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button intent="primary" size="large" className="group" onClick={() => navigate("/login")}>
                <span>Start Your 3D Journey Today</span>
              </Button>
              <Button intent="secondary" size="large">
                Watch Demo
              </Button>
            </div>
            
            {/* Stats section with improved alignment */}
            <div className="mt-12 flex flex-wrap gap-8">
              <div className="flex items-baseline">
                <HighlightedText className="text-xl font-semibold whitespace-nowrap">250+</HighlightedText>
                <Subtle className="ml-2">Shopify merchants</Subtle>
              </div>
              <div className="flex items-baseline">
                <HighlightedText className="text-xl font-semibold whitespace-nowrap">10,000+</HighlightedText>
                <Subtle className="ml-2">3D models published</Subtle>
              </div>
              <div className="flex items-baseline">
                <HighlightedText className="text-xl font-semibold whitespace-nowrap">37%</HighlightedText>
                <Subtle className="ml-2">average increase in conversion</Subtle>
              </div>
            </div>
          </div>
          
          <div 
            className={`transition-all duration-700 delay-300 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div //border-blue-400/30 shadow-glow-sm
              className="relative overflow-hidden rounded-lg aspect-[4/3]"
             
            >
              <div className="absolute inset-0 flex items-center justify-center">
                {/* Interactive SVG cube that rotates slowly */}
                <div className="absolute bottom-0 top-0 left-0 right-0 text-center z-20 ">
                    <Scene3d />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};