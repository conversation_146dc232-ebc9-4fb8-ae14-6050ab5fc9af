import React, { useState, useEffect } from 'react';

export const ScrollProgress: React.FC = () => {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    // Throttled scroll handler for better performance
    let ticking = false;
    
    const calculateScrollProgress = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          const scrollTop = window.scrollY;
          const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
          const progress = (scrollTop / documentHeight) * 100;
          setScrollProgress(progress);
          ticking = false;
        });
        
        ticking = true;
      }
    };
    
    // Use passive flag for better performance
    window.addEventListener('scroll', calculateScrollProgress, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', calculateScrollProgress);
    };
  }, []);

  return (
    <div className="fixed top-0 left-0 w-full h-0.5 bg-gray-800 z-50">
      <div 
        className="h-full bg-blue-400"
        style={{ width: `${scrollProgress}%` }}
      ></div>
    </div>
  );
};