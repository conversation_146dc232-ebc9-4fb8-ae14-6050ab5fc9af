# Error Handling in Morpho Backend

This document explains the error handling system in the Morpho backend application.

## Overview

The error handling system is designed to:

1. Provide consistent error responses across the API
2. Give meaningful error messages to users
3. Include error codes for frontend handling
4. Log detailed error information for debugging
5. Handle different types of errors appropriately

## Error Response Format

All error responses follow this format:

```json
{
  "success": false,
  "status": 400, // HTTP status code
  "message": "User-friendly error message",
  "errorCode": "ERROR_CODE" // Optional, for frontend handling
}
```

## Error Codes

Error codes are used to help the frontend identify specific error types and handle them appropriately. Common error codes include:

### Authentication Errors
- `AUTHENTICATION_REQUIRED`: User needs to log in
- `INVALID_CREDENTIALS`: Wrong email or password
- `SESSION_EXPIRED`: User's session has expired

### Authorization Errors
- `PERMISSION_DENIED`: User doesn't have permission
- `SUBSCRIPTION_REQUIRED`: Feature requires a paid subscription

### Resource Errors
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `RESOURCE_ALREADY_EXISTS`: Resource already exists (e.g., duplicate email)

### Validation Errors
- `VALIDATION_ERROR`: Input validation failed
- `INVALID_DATA_FORMAT`: Data format is incorrect
- `MISSING_REQUIRED_FIELD`: Required field is missing

### File Errors
- `FILE_UPLOAD_ERROR`: General file upload error
- `FILE_TOO_LARGE`: File exceeds size limit
- `INVALID_FILE_TYPE`: File type not supported

### Server Errors
- `SERVER_ERROR`: General server error
- `DATABASE_ERROR`: Database operation failed
- `EXTERNAL_API_ERROR`: External API call failed

### Rate Limiting
- `RATE_LIMIT_EXCEEDED`: Too many requests

### Product Limits
- `PRODUCT_LIMIT_EXCEEDED`: User has reached their product limit

## Using Error Handling in Controllers

### Example 1: Using expressError

```javascript
const expressError = require('../errors/expressError');

// In a controller function
if (!user) {
  throw new expressError(
    'User not found. Please check the user ID and try again.',
    404,
    expressError.CODES.RESOURCE_NOT_FOUND
  );
}
```

### Example 2: Using responseHandle

```javascript
const resHandle = require('../utils/responseHandle');

// In a controller function
try {
  // Some operation
} catch (err) {
  return resHandle.handleError(
    res,
    400,
    'Failed to create product. Please try again.',
    false,
    'VALIDATION_ERROR'
  );
}
```

## Error Handling Middleware

The application uses a global error handling middleware (`errorController.js`) that catches all errors thrown in the application and formats them appropriately.

### How It Works

1. Controllers can throw errors or pass them to `next()`
2. The error middleware catches these errors
3. It identifies the error type and formats it
4. It sends a consistent response to the client

## Best Practices

1. Always use the error handling utilities (`expressError` or `resHandle`)
2. Provide user-friendly error messages
3. Include appropriate error codes
4. Log detailed error information for debugging
5. Handle expected errors gracefully

## Frontend Integration

The frontend should check for:

1. The `success` flag to determine if the request succeeded
2. The `message` to display to the user
3. The `errorCode` to handle specific error cases

Example frontend handling:

```javascript
try {
  const response = await api.createProduct(productData);
  // Handle success
} catch (error) {
  if (error.errorCode === 'PRODUCT_LIMIT_EXCEEDED') {
    // Show upgrade subscription dialog
  } else if (error.errorCode === 'VALIDATION_ERROR') {
    // Highlight form fields with errors
  } else {
    // Show general error message
    toast.error(error.message || 'An error occurred');
  }
}
```
