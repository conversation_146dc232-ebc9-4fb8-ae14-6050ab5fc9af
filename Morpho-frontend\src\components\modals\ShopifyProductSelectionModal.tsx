import React, { useState, useEffect } from "react";
import {
  X,
  Store,
  Search,
  AlertCircle,
  CheckCircle,
  Loader2,
  RefreshCw,
  ExternalLink,
} from "lucide-react";
import { ecommerceService } from "../../services/ecommerce";
import { storage } from "../../utils/storage";

interface ShopifyProduct {
  id: string;
  title: string;
  description: string;
  price: string;
  sku: string;
  imageUrl: string | null;
  status: string;
  alreadyImported: boolean;
}

interface ShopifyProductSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  credentials: {
    shopName: string;
    apiKey: string;
    password: string;
  };
  onImport: (selectedProductIds: string[]) => Promise<void>;
}

const ShopifyProductSelectionModal: React.FC<
  ShopifyProductSelectionModalProps
> = ({ isOpen, onClose, credentials, onImport }) => {
  const [products, setProducts] = useState<ShopifyProduct[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter products based on search term
  const filteredProducts = Array.isArray(products)
    ? products.filter(
        (product) =>
          product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (product.sku &&
            product.sku.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : [];

  // Load products from Shopify
  useEffect(() => {
    if (
      isOpen &&
      credentials.shopName &&
      credentials.apiKey &&
      credentials.password
    ) {
      fetchProducts();
    }
  }, [isOpen, credentials]);

  const fetchProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await ecommerceService.getShopifyProductsForSelection(
        credentials
      );

      // Add debugging to see the exact structure

      if (result.success && result.data) {
        // Check if data.products exists and is an array
        if (result.data.products && Array.isArray(result.data.products)) {
          const productsArray = result.data.products;

          setProducts(productsArray);

          // Pre-select products that are not already imported
          const productsToSelect = productsArray
            .filter((product: ShopifyProduct) => !product.alreadyImported)
            .map((product: ShopifyProduct) => product.id);

          setSelectedProducts(productsToSelect);
        } else {
          // Handle case where products is not an array

          setError("Invalid data format received from server");
          setProducts([]);
        }
      } else {
        setError(result.error || "Failed to fetch products from Shopify");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = () => {
    // Make sure filteredProducts is an array
    if (!Array.isArray(filteredProducts)) {
      return;
    }

    // Get non-imported products
    const nonImportedProducts = filteredProducts.filter(
      (p) => !p.alreadyImported
    );

    if (
      selectedProducts.length === nonImportedProducts.length &&
      nonImportedProducts.length > 0
    ) {
      // Deselect all non-imported products
      setSelectedProducts([]);
    } else {
      // Select all non-imported products
      setSelectedProducts(nonImportedProducts.map((product) => product.id));
    }
  };

  const handleProductSelect = (productId: string) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter((id) => id !== productId));
    } else {
      setSelectedProducts([...selectedProducts, productId]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedProducts.length === 0) {
      setError("Please select at least one product to import");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await onImport(selectedProducts);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="w-full max-w-4xl max-h-[90vh] overflow-hidden bg-dark-300 rounded-xl shadow-xl">
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <div className="flex items-center gap-3">
            <Store size={20} className="text-brand-300" />
            <h2 className="text-xl font-light text-gray-100">
              Select Products to Import from Shopify
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-dark-200 text-gray-400 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {error && (
            <div className="mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/20 flex items-start gap-3">
              <AlertCircle size={20} className="text-red-400 shrink-0 mt-0.5" />
              <p className="text-red-400">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-light text-gray-200">
                    Shopify Products
                  </h3>
                  <p className="text-sm text-gray-400">
                    Select the products you want to import from your Shopify
                    store
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={fetchProducts}
                    className="text-sm text-brand-300 hover:text-brand-200 flex items-center gap-1"
                  >
                    <RefreshCw size={14} />
                    <span>Refresh</span>
                  </button>
                  <button
                    type="button"
                    onClick={handleSelectAll}
                    className="text-sm text-brand-300 hover:text-brand-200"
                  >
                    {Array.isArray(filteredProducts) &&
                    selectedProducts.length ===
                      filteredProducts.filter((p) => !p.alreadyImported)
                        .length &&
                    filteredProducts.filter((p) => !p.alreadyImported).length >
                      0
                      ? "Deselect All"
                      : "Select All"}
                  </button>
                </div>
              </div>

              <div className="relative">
                <Search
                  size={18}
                  strokeWidth={1.5}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search products by name or SKU..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input pl-10 w-full"
                />
              </div>

              <div className="mt-2 space-y-2 max-h-[400px] overflow-y-auto border border-gray-800 rounded-lg p-2">
                {loading ? (
                  <div className="text-center py-4">
                    <Loader2
                      size={24}
                      className="animate-spin mx-auto text-brand-300"
                    />
                    <p className="text-gray-400 mt-2">
                      Loading products from Shopify...
                    </p>
                  </div>
                ) : filteredProducts.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-gray-400">No products found</p>
                  </div>
                ) : (
                  filteredProducts.map((product) => (
                    <label
                      key={product.id}
                      className={`flex items-center gap-3 p-3 rounded-lg hover:bg-dark-200/50 cursor-pointer ${
                        product.alreadyImported ? "opacity-50" : ""
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedProducts.includes(product.id)}
                        onChange={() => handleProductSelect(product.id)}
                        className="form-checkbox"
                        disabled={product.alreadyImported}
                      />
                      {product.imageUrl && (
                        <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-800 flex-shrink-0">
                          <img
                            src={product.imageUrl}
                            alt={product.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                      <div className="flex-1">
                        <p className="text-gray-300">{product.title}</p>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <span>SKU: {product.sku || "N/A"}</span>
                          <span>•</span>
                          <span>${parseFloat(product.price).toFixed(2)}</span>
                          <span>•</span>
                          <span
                            className={`capitalize ${
                              product.status === "active"
                                ? "text-green-400"
                                : "text-yellow-400"
                            }`}
                          >
                            {product.status}
                          </span>
                        </div>
                      </div>
                      {product.alreadyImported && (
                        <span className="px-2 py-1 text-xs rounded-full bg-brand-500/10 text-brand-300">
                          Already Imported
                        </span>
                      )}
                    </label>
                  ))
                )}
              </div>
            </div>

            <div className="mt-8 flex justify-end gap-3">
              <button
                type="button"
                onClick={onClose}
                className="btn btn-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting || selectedProducts.length === 0}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 size={16} className="animate-spin mr-2" />
                    Importing...
                  </>
                ) : (
                  `Import ${selectedProducts.length} Products`
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ShopifyProductSelectionModal;
