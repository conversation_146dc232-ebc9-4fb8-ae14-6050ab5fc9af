const router = require("express").Router();
const controller = require("../../controllers/assetController");
const verifyRole = require("../../middleware/verifyRole");
const { designer, admin, client } = require("../../constants");
const verifyJWT = require("../../middleware/verifyJWT");
const { validateAssetLibrarySchema } = require("../../validation/middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

// router.get("/get-all-asset", verifyRole[admin], asyncErrorHandler(controller.getAllAssets));
router.get(
  "/get-asset-by-id",
  verifyJWT,
  asyncErrorHandler(controller.getAssetById)
);
router.get(
  "/get-asset-with-project",
  verifyJWT,
  asyncErrorHandler(controller.getAssetByIdWithProject)
);
router.post(
  "/get-asset-by-project",
  verifyJWT,
  asyncErrorHandler(controller.getAssetByProjectId)
);
router.post(
  "/get-buff-asset-by-project",
  asyncErrorHandler(controller.getAssetByProjectId)
);
router
  .route("/create-asset")
  .post(
    validateAssetLibrarySchema,
    verifyJWT,
    asyncErrorHandler(controller.createAsset)
  );
router.delete(
  "/delete-asset",
  verifyJWT,
  asyncErrorHandler(controller.deleteAsset)
);
router
  .route("/update-asset")
  .put(
    validateAssetLibrarySchema,
    verifyJWT,
    asyncErrorHandler(controller.updateAsset)
  );

module.exports = router;
