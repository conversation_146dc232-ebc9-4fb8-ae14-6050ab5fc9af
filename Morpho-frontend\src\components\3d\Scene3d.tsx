import React, { Suspense, useRef, useEffect, useState } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
import { OrbitControls, useGLTF } from "@react-three/drei";
import * as THREE from "three";

const Model = () => {
  const { scene } = useGLTF("/models/Armchair_v1.1-compressed.glb");  // Load the model
  const modelRef = useRef<THREE.Group | null>(null);

  // Slowly rotate the model
  useFrame(() => {
    if (modelRef.current) {
      modelRef.current.rotation.y += 0.001; // Adjust the rotation speed
    }
  });

  return <primitive ref={modelRef} object={scene} position={[0, 0.3, 0]} />;  // Adjust Y position to lift the model
};

const Scene3D = () => {
  return (
    <div style={{ height: "90vh" }}>
      <Canvas
        camera={{ position: [0, 1, 4], fov: 30 }}  // Adjust camera position
        style={{ width: "100%", height: "100%" }}
      >
        <Suspense fallback={null}>
          <ambientLight intensity={0.5} />
          <spotLight position={[10, 10, 10]} angle={0.15} intensity={1} castShadow />
          <Model />
        </Suspense>

        {/* OrbitControls to interact with the model with zoom limits */}
        <OrbitControls minDistance={3.5} maxDistance={6} />
      </Canvas>
    </div>
  );
};

export default Scene3D;
