import React, { useRef } from 'react';
import { GlassContainer } from '../ui/GlassContainer';
import { useInView } from '../../hooks/useInView';

export const About: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  
  return (
    <section ref={sectionRef} id="about" className="py-24 relative">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className={`text-3xl md:text-4xl font-bold text-white mb-12 text-center transition-all duration-700 ${
            isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            About Morpho: The Leading Shopify 3D Product Viewer Platform
          </h2>
          
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className={`transition-all duration-700 delay-150 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <p className="text-gray-300 mb-6">
                Founded by a team of Shopify Plus developers and 3D product visualization experts, Morpho was built to solve the real challenges we saw Shopify merchants face when trying to implement 3D products in their online stores.
              </p>
              <p className="text-gray-300 mb-6">
                After helping dozens of brands struggle with complex, generic 3D model viewers that weren't built for e-commerce, we created the platform we wished existed: a true Shopify-first 3D product visualization solution.
              </p>
              <p className="text-gray-300">
                Our team combines over 30 years of Shopify development expertise with 3D product rendering technology innovation, backed by partnerships with leading Shopify Plus merchants.
              </p>
            </div>
            
            <div className={`transition-all duration-700 delay-300 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <GlassContainer className="overflow-hidden rounded-lg aspect-square">
                <div className="absolute inset-0 flex items-center justify-center">
                  {/* Placeholder for team photo */}
                  <div className="relative w-full h-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                    <div className="text-center p-6">
                      <p className="text-white text-lg">Our diverse team of experts</p>
                      <p className="text-gray-400 mt-2">Shopify developers and 3D specialists</p>
                    </div>
                  </div>
                </div>
              </GlassContainer>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};