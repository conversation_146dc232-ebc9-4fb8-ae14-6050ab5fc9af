const router = require("express").Router();
const controller = require("../../controllers/webflowController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
const setWebflowClient = require("../../utils/webflow");

router.get(
  "/products",
  setWebflowClient,
  asyncError<PERSON><PERSON>ler(controller.getAllProducts)
);
router.get(
  "/statistics",
  setWebflowClient,
  asyncErrorHandler(controller.getStatistics)
);
router.post(
  "/save-products",
  setWebflowClient,
  asyncErrorHandler(controller.saveAllProducts)
);

module.exports = router;
