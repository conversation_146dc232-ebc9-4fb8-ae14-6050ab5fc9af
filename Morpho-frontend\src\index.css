@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --brand: 0, 102, 204;
    --dark-bg: 15, 17, 21;
  }
}

@layer base {
  body {
    @apply bg-dark-400 text-gray-100;
  }
  
  model-viewer {
    --poster-color: transparent;
    --progress-bar-color: rgb(var(--brand));
    --progress-bar-height: 2px;
    --progress-mask: rgb(var(--dark-bg));
    --camera-orbit-sensitivity: 1;
    --interaction-prompt-threshold: 0;
    --interaction-prompt: none;
    --min-camera-orbit: auto 0deg auto;
    --max-camera-orbit: auto 180deg auto;
    --camera-target: 0m 0m 0m;
    --environment-image: legacy;
    --exposure: 1;
    --shadow-intensity: 1;
    --shadow-softness: 1;
    --auto-rotate: false;
  }

  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-dark-300;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-dark-100 hover:bg-brand-300/30 transition-colors rounded-full;
  }
}

@layer components {
  .card {
    @apply bg-dark-300/90 backdrop-blur-lg border border-gray-800/50 transition-all duration-300;
  }

  .card-hover {
    @apply hover:border-brand-300/30 hover:shadow-glow transition-all duration-300;
  }

  .toggle-checkbox {
    @apply absolute block w-6 h-6 rounded-full bg-white border-4 border-gray-400 appearance-none cursor-pointer duration-200 ease-in;
  }

  .toggle-checkbox:checked {
    @apply bg-brand-300 border-brand-300 transform translate-x-full;
  }

  .toggle-label {
    @apply block h-6 overflow-hidden bg-gray-700 rounded-full cursor-pointer;
  }

  .nav-link {
    @apply hover:bg-brand-300/5 transition-colors;
  }

  .nav-link-active {
    @apply bg-brand-300/10 text-gray-100;
  }

  .btn {
    @apply px-4 py-2.5 rounded-lg font-light transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98];
  }

  .btn-primary {
    @apply bg-gradient-to-r from-brand-500 to-brand-600 text-white hover:from-brand-600 hover:to-brand-700 shadow-lg shadow-brand-500/20 hover:shadow-brand-500/30;
  }

  .btn-secondary {
    @apply bg-dark-300/50 backdrop-blur-sm border border-gray-700/50 text-gray-300 hover:border-brand-500/50 hover:bg-dark-200/50;
  }
  
  .dropdown-menu {
    @apply absolute right-0 mt-2 w-64 rounded-lg bg-dark-300/95 backdrop-blur-sm border border-gray-800/50 shadow-xl 
           overflow-hidden transform origin-top-right transition-all duration-200 z-50;
  }
  
  .dropdown-item {
    @apply w-full px-4 py-3 text-left text-gray-300 hover:bg-brand-500/10 hover:text-brand-300 
           transition-colors duration-200 flex items-center gap-3 border-t first:border-t-0 border-gray-800/30;
  }
  
  .search-input {
    @apply w-full pl-10 pr-4 py-2.5 rounded-lg bg-dark-300/80 backdrop-blur-sm border border-gray-700/50 
           text-gray-100 placeholder-gray-500 focus:outline-none focus:border-brand-500/50 focus:ring-2 
           focus:ring-brand-500/20 transition-all duration-200;
  }
  
  .filter-select {
    @apply w-full px-4 py-2.5 rounded-lg bg-dark-300/80 backdrop-blur-sm border border-gray-700/50 
           text-gray-300 focus:outline-none focus:border-brand-500/50 focus:ring-2 focus:ring-brand-500/20 
           transition-all duration-200;
  }
  
  .tag-button {
    @apply px-3 py-1.5 rounded-full text-sm font-light transition-all duration-200 transform 
           hover:scale-105 active:scale-95;
  }
  
  .tag-button-active {
    @apply bg-brand-500/20 text-brand-300 border border-brand-500/30 shadow-lg shadow-brand-500/10;
  }
  
  .tag-button-inactive {
    @apply bg-dark-200/50 text-gray-400 border border-gray-700/50 hover:border-brand-500/30 
           hover:bg-dark-200/80;
  }
}

@layer components {
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }
  
  .animate-slideIn {
    animation: slideIn 0.2s ease-out forwards;
  }

  .input {
    @apply w-full px-4 py-2 rounded-lg bg-dark-300 border border-gray-700/50 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-brand-500/50 transition-colors;
  }
}