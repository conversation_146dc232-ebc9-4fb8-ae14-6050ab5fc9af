const { default: mongoose } = require("mongoose");
const categorySchema = require("../models/Category");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
class Controller {
  createCategory = async (req, res) => {
    // Get category details from request body
    const { name, description, production, hasCollection, imageUrl, price } =
      req.body;

    // Validate required fields
    if (!name) {
      throw new expressError(
        "Category name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Create new category
    const newCategory = new categorySchema({
      name,
      description,
      production,
      hasCollection,
      imageUrl,
      price,
    });

    // Save category to database
    await newCategory.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Category created successfully",
      true,
      newCategory
    );
  };
  getAllCategories = async (req, res) => {
    // Fetch all categories
    const categories = await categorySchema.find({});

    // Check if categories exist
    if (!categories || categories.length === 0) {
      throw new expressError(
        "No categories found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Categories retrieved successfully",
      true,
      categories
    );
  };
  getCategoryById = async (req, res) => {
    // Get category ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Category ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid category ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Find category by ID
      const category = await categorySchema.findOne({ _id: id });

      // Check if category exists
      if (!category) {
        throw new expressError(
          "Category not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Category retrieved successfully",
        true,
        category
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid category ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  getCategoryWithProduction = async (req, res) => {
    // Get category ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Category ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid category ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Find category by ID and populate production
      const category = await categorySchema
        .findOne({ _id: id })
        .populate("production");

      // Check if category exists
      if (!category) {
        throw new expressError(
          "Category not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Category retrieved successfully",
        true,
        category
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid category ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  getCategoriesOfProject = async (req, res) => {
    // Get project ID from request body
    const { project } = req.body;

    // Validate required fields
    if (!project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!project.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Build aggregation pipeline
      const categories = await categorySchema.aggregate([
        {
          $lookup: {
            from: "productions",
            localField: "production",
            foreignField: "_id",
            as: "production",
          },
        },
        {
          $unwind: {
            path: "$production",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $match: { "production.project": mongoose.Types.ObjectId(project) },
        },
      ]);

      // Check if categories exist
      if (!categories || categories.length === 0) {
        throw new expressError(
          "No categories found for this project",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Categories retrieved successfully",
        true,
        categories
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid project ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  deleteCategoryById = async (req, res) => {
    // Get category ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Category ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid category ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete category
    const deletedCategory = await categorySchema.findByIdAndDelete(id);

    // Check if category exists
    if (!deletedCategory) {
      throw new expressError(
        "Category not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Category deleted successfully",
      true
    );
  };
}

const controller = new Controller();

module.exports = controller;
