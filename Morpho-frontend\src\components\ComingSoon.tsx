import React from 'react';
import { Construction } from 'lucide-react';

interface ComingSoonProps {
  title?: string;
  description?: string;
}

const ComingSoon: React.FC<ComingSoonProps> = ({
  title = "Coming Soon",
  description = "We're working hard to bring you this feature. Stay tuned!"
}) => {
  return (
    <div className="fixed inset-0 bg-dark-400/95 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center space-y-4 max-w-lg mx-auto p-8">
        <div className="w-16 h-16 rounded-full bg-brand-500/10 flex items-center justify-center mx-auto">
          <Construction className="w-8 h-8 text-brand-300" strokeWidth={1.5} />
        </div>
        <h2 className="text-2xl font-light text-gray-100">{title}</h2>
        <p className="text-gray-400">{description}</p>
      </div>
    </div>
  );
};

export default ComingSoon; 