const { uploadToS3NewVersion, s3Upload } = require("../config/s3Service");
const showroomTextureModel = require("../models/showroomTextureModel");
const showroomCategory = require("../models/showroomCategoriesModel");
const { getCategories } = require("../services/showroomService");
const showroomCategoryModel = require("../models/showroomCategoriesModel");
class Controller {
  create = async (req, res) => {
    const bodyData = req.body;
    let inputs = {
      customizationName: bodyData.customizationName,
      url: bodyData.url,
      metallic: bodyData.metallic,
      roughness: bodyData.roughness,
      transparencyMode: bodyData.transparencyMode,
      alpha: bodyData.alpha,
      uScale: bodyData.uScale,
      vScale: bodyData.vScale,
      iconUrl: bodyData.iconUrl,
    };

    let categoryy = await showroomCategory.findOne({ name: req.body.category });
    if (!categoryy) {
      categoryy = new showroomCategory({ name: req.body.category });
      await categoryy.save();
    }
    inputs.category = categoryy._id;

    if (req.files && req.files.url && req.files.url[0]) {
      const data = await uploadToS3NewVersion(
        req.files.url[0],
        categoryy.name,
        `showroom/Textures/Customization Textures`,
      );
      inputs.url = data.Location;
    } else if (typeof bodyData.url === "string") {
      inputs.url = bodyData.url;
    }

    if (req.files && req.files.iconUrl && req.files.iconUrl[0]) {
      const data = await s3Upload(req.files.iconUrl[0], "iconurl");
      inputs.iconUrl = data?.Location;
    } else if (typeof bodyData.iconUrl === "string") {
      inputs.iconUrl = bodyData.iconUrl;
    }

    const newTexture = new showroomTextureModel(inputs);
    await newTexture.save();
    return res.status(200).json({ data: newTexture });
  };

  getById = async (req, res) => {
    const { id } = req.params;
    const showroomTexture = await showroomTextureModel
      .findById(id)
      .populate("category");
    if (!showroomTexture) return res.status(404).json("Texture Not Found");
    return res.status(200).json({ data: showroomTexture });
  };
  getAll = async (req, res) => {
    const showroomTexture = await showroomTextureModel
      .find()
      .populate("category");
    return res.status(200).json({ data: showroomTexture });
  };
  getAllCategoriesOfTextures = async (req, res) => {
    const categoryIds = await showroomTextureModel.distinct("category");
    const categories = await showroomCategory
      .find({
        _id: { $in: categoryIds },
      })
      .select("name _id");

    return res.status(200).json({ data: categories });
  };
  updateById = async (req, res) => {
    const { id } = req.params;
    let { category } = req.body;

    let categoryToBeFound = await showroomCategoryModel.findOne({
      name: category,
    });

    if (!categoryToBeFound) {
      categoryToBeFound = new showroomCategoryModel({ name: category });
      await categoryToBeFound.save();
    }

    const showroomTexture = await showroomTextureModel.findByIdAndUpdate(
      id,
      { ...req.body, category: categoryToBeFound._id },
      { new: true },
    );

    if (!showroomTexture)
      return res.status(404).json("Texture Failed To Be Updated");

    return res.status(200).json({ data: showroomTexture });
  };

  deleteById = async (req, res) => {
    const { id } = req.params;
    const showroomTexture = await showroomTextureModel.findByIdAndDelete(id);
    if (!showroomTexture)
      return res.status(404).json("Texture Failed To Be Deleted");
    return res.status(200).json("Texture Deleted");
  };
}

const controller = new Controller();
module.exports = controller;
