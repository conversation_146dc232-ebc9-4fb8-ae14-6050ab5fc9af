import React, { useState, useEffect, useMemo } from "react";
import {
  Check,
  AlertCircle,
  Loader2,
  CreditCard,
  Shield,
  Star,
  Zap,
  Calendar,
  RefreshCw,
} from "lucide-react";
import {
  subscriptionService,
  SubscriptionPlan,
  SubscriptionStatus,
} from "../services/subscription";
import { toast } from "react-hot-toast";

const Subscription: React.FC = () => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [subscription, setSubscription] = useState<
    (SubscriptionStatus & { periodEnd?: string }) | null
  >(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancelingSubscription, setCancelingSubscription] = useState(false);
  const [billingInterval, setBillingInterval] = useState<"month" | "year">(
    "month"
  );

  // Function to fetch subscription data
  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch subscription plans
      const plansResult = await subscriptionService.getSubscriptionPlans();

      if (!plansResult.success) {
        console.error("Failed to fetch plans:", plansResult.error);
        // Don't throw error, just log it and continue
        // We'll still try to fetch the subscription status
      } else {
        setPlans(plansResult.data || []);
      }

      // Fetch user subscription status
      const subscriptionResult =
        await subscriptionService.getUserSubscription();

      if (subscriptionResult.success) {
        setSubscription(subscriptionResult.data || null);
      } else if (subscriptionResult.error) {
        console.error(
          "Failed to fetch subscription:",
          subscriptionResult.error
        );
        // Create a default subscription status
        const defaultStatus: SubscriptionStatus = {
          status: "inactive",
          plan: "free",
          subscriptionId: null,
          productLimit: 1,
        };
        setSubscription(defaultStatus);
      }

      // Clear any previous errors
      setError(null);
    } catch (err) {
      console.error("Error fetching subscription data:", err);
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );

      // Set default values even if there's an error
      if (plans.length === 0) {
        // Create some default plans if we couldn't fetch them
        setPlans([
          {
            id: "free",
            name: "Starter",
            description: "Get started with 3D product visualization",
            price: 0,
            interval: "month",
            productLimit: 1,
            features: [
              "1 product in 3D",
              "Core 3D/AR viewer",
              "iOS scanning app",
              "Basic stats",
              "Community support",
            ],
            paymentLink: null,
          },
          {
            id: "basic",
            name: "Basic",
            description:
              "Perfect for small Shopify stores with a moderate catalog",
            price: 49,
            interval: "month",
            productLimit: 25,
            features: [
              "Up to 25 products in 3D",
              "Core features",
              "Free integration with e-commerce platforms",
              "Basic analytics",
              "Standard support",
              "10% off model services",
              "Encrypted data",
              "Model compression",
            ],
            paymentLink: null,
          },
          {
            id: "basic-annual",
            name: "Basic (Annual)",
            description:
              "Perfect for small Shopify stores with a moderate catalog",
            price: 499,
            interval: "year",
            productLimit: 25,
            features: [
              "Up to 25 products in 3D",
              "Core features",
              "Free integration with e-commerce platforms",
              "Basic analytics",
              "Standard support",
              "10% off model services",
              "Encrypted data",
              "Model compression",
              "Save $89 with annual billing",
            ],
            paymentLink: null,
          },
          {
            id: "pro",
            name: "Pro",
            description:
              "Ideal for growing Shopify brands with larger catalogs",
            price: 179,
            interval: "month",
            productLimit: 150,
            features: [
              "Up to 150 products in 3D",
              "Full analytics",
              "Priority support",
              "Custom branding",
              "API access",
              "Unlimited model conversions",
              "20% off modeling services",
              "Encrypted data",
              "Model compression",
            ],
            paymentLink: null,
          },
          {
            id: "pro-annual",
            name: "Pro (Annual)",
            description:
              "Ideal for growing Shopify brands with larger catalogs",
            price: 1789,
            interval: "year",
            productLimit: 150,
            features: [
              "Up to 150 products in 3D",
              "Full analytics",
              "Priority support",
              "Custom branding",
              "API access",
              "Unlimited model conversions",
              "20% off modeling services",
              "Encrypted data",
              "Model compression",
              "Save $359 with annual billing",
            ],
            paymentLink: null,
          },
          {
            id: "enterprise",
            name: "Enterprise",
            description: "Custom solution for large Shopify Plus merchants",
            price: null,
            customPricing: true,
            interval: "month",
            productLimit: Infinity,
            features: [
              "Unlimited products (1000+)",
              "Dedicated success manager",
              "SLA",
              "API/SDK",
              "White-label",
              "Full analytics",
              "Custom encryption protocols",
              "Advanced compression",
              "Large model bundles or custom services",
            ],
            paymentLink: null,
          },
        ]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Set up polling for subscription status updates
  useEffect(() => {
    // Set up polling interval to check for subscription updates
    const intervalId = setInterval(() => {
      // Fetch subscription status
      subscriptionService.getUserSubscription().then((result) => {
        if (result.success && result.data) {
          // Only update if the status has changed
          if (
            !subscription ||
            subscription.status !== result.data.status ||
            subscription.plan !== result.data.plan
          ) {
            setSubscription(result.data);

            // If status changed to active, show a success message
            if (
              subscription?.status !== "active" &&
              result.data.status === "active"
            ) {
              toast.success("Your subscription is now active!");
            }
          }
        }
      });
    }, 10000); // Check every 10 seconds

    // Clean up interval
    return () => {
      clearInterval(intervalId);
    };
  }, [subscription]);

  // Development-only functions removed

  const handleSubscribe = async (plan: SubscriptionPlan) => {
    // Handle subscription through Stripe checkout

    // Check if payment link exists
    if (!plan.paymentLink) {
      toast.success("This plan doesn't require payment.");
      return;
    }

    // Debug: Check if the payment link is valid
    if (
      !plan.paymentLink ||
      typeof plan.paymentLink !== "string" ||
      !plan.paymentLink.startsWith("http")
    ) {
      console.error("Invalid payment link:", plan.paymentLink);
      toast.error("Invalid payment link. Please try again later.");
      return;
    }

    // Show a message to the user
    toast.success(
      "Redirecting to Stripe checkout. Your subscription will be updated shortly after payment."
    );

    // Start checking for subscription updates immediately
    startSubscriptionPolling(plan);

    try {
      // Create an anchor element to open the link in a new tab
      const a = document.createElement("a");
      a.href = plan.paymentLink;
      a.target = "_blank";
      a.rel = "noopener noreferrer";

      // Append to the document, click it, and remove it
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (error) {
      console.error("Error opening payment link:", error);
      toast.error("Failed to open payment page. Please try again.");
    }

    // Set up a listener to check for subscription status when the user returns to this page
    const checkSubscriptionOnFocus = () => {
      // Fetch subscription status when user returns to the page
      fetchData();

      // Remove the event listener after it's triggered once
      window.removeEventListener("focus", checkSubscriptionOnFocus);
    };

    // Add the event listener
    window.addEventListener("focus", checkSubscriptionOnFocus);
  };

  // Helper function to start polling for subscription updates
  const startSubscriptionPolling = (plan: SubscriptionPlan) => {
    // Show a message to the user
    toast.success(
      "Your subscription will be updated automatically when payment is complete. No need to refresh the page."
    );

    // Set up aggressive polling for the next 2 minutes
    let checkCount = 0;
    const maxChecks = 24; // Check for up to 2 minutes (24 * 5 seconds)

    const checkInterval = setInterval(async () => {
      checkCount++;
      

      try {
        const result = await subscriptionService.getUserSubscription();
        if (result.success && result.data) {
          // Check if subscription is active and matches the plan the user subscribed to
          if (result.data.status === "active" && result.data.plan === plan.id) {
            // Subscription is active and matches the plan, clear the interval
            clearInterval(checkInterval);
            toast.success(`Your ${plan.name} subscription is now active!`);
            setSubscription(result.data);
          } else {
            // Update the subscription state anyway
            setSubscription(result.data);
          }
        }
      } catch (error) {
        console.error("Error checking subscription status:", error);
      }

      if (checkCount >= maxChecks) {
        clearInterval(checkInterval);
      }
    }, 5000); // Check every 5 seconds

    // Return the interval ID so we can clear it if needed
    return checkInterval;
  };

  // Helper function to get progress bar color based on usage percentage
  const getProgressBarColor = (count: number, limit: number) => {
    const percentage = (count / limit) * 100;
    if (percentage >= 90) return "bg-red-500";
    if (percentage >= 75) return "bg-yellow-500";
    return "bg-green-500";
  };

  // Filter plans based on billing interval and remove annual plans from display
  const filteredPlans = useMemo(() => {
    // First get all base plans (free, basic, pro, enterprise)
    const basePlans = plans.filter(
      (plan) => !plan.id.includes("-annual") && plan.interval === "month"
    );

    // For the free plan, always show it regardless of billing interval
    const freePlan = plans.find((plan) => plan.id === "free");

    if (billingInterval === "month") {
      return basePlans;
    } else {
      // For yearly billing, replace monthly plans with their annual counterparts
      return basePlans.map((plan) => {
        if (plan.id === "free") return plan;

        const annualPlan = plans.find((p) => p.id === `${plan.id}-annual`);
        if (annualPlan) {
          return annualPlan;
        }
        return plan;
      });
    }
  }, [plans, billingInterval]);

  // Helper function to get the annual plan for a monthly plan
  const getAnnualPlan = (plan: SubscriptionPlan) => {
    return plans.find((p) => p.id === `${plan.id}-annual`);
  };

  // Helper function to calculate savings percentage
  const calculateSavings = (monthlyPrice: number, yearlyPrice: number) => {
    const monthlyCostPerYear = monthlyPrice * 12;
    const savings = monthlyCostPerYear - yearlyPrice;
    const savingsPercentage = Math.round((savings / monthlyCostPerYear) * 100);
    return savingsPercentage;
  };

  const handleCancelSubscription = async () => {
    if (!subscription || !subscription.subscriptionId) {
      toast.error("No active subscription found");
      return;
    }

    // Show confirmation dialog
    if (
      !window.confirm(
        "Are you sure you want to cancel your subscription? \n\n" +
          "Your subscription will remain active until the end of your current billing period. \n" +
          "After that, your plan will be downgraded to the free tier."
      )
    ) {
      return; // User canceled
    }

    setCancelingSubscription(true);
    try {
      const result = await subscriptionService.cancelSubscription();
      if (result.success) {
        toast.success(
          result.message ||
            "Subscription canceled successfully. Your subscription will remain active until the end of your current billing period."
        );
        // Update local subscription state with cancellation date if available
        setSubscription((prev) =>
          prev
            ? {
                ...prev,
                status: "canceling",
                periodEnd: result.data?.periodEnd || prev.periodEnd,
              }
            : null
        );
      } else {
        toast.error(result.error || "Failed to cancel subscription");
      }
    } catch (err) {
      toast.error(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setCancelingSubscription(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-6rem)]">
        <Loader2 className="w-8 h-8 text-brand-300 animate-spin" />
        <p className="mt-4 text-gray-400">
          Loading subscription information...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-6rem)]">
        <div className="p-6 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2 max-w-md">
          <AlertCircle size={20} strokeWidth={1.5} />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 max-w-6xl mx-auto">
      <div>
        <h1 className="text-3xl font-light tracking-wide text-gray-100">
          Subscription Plans
        </h1>
        <p className="text-gray-400 mt-2">
          Choose the plan that works best for you
        </p>
      </div>

      {/* Current Subscription Status */}
      {subscription && (
        <div className="card rounded-xl p-6 bg-dark-300/90 backdrop-blur-lg">
          <h2 className="text-xl font-light text-gray-200 mb-4">
            Your Subscription
          </h2>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CreditCard size={18} className="text-brand-300" />
                <span className="text-gray-300">Current Plan</span>
              </div>
              <span className="text-gray-200 font-medium capitalize">
                {subscription.plan
                  ? `${
                      subscription.plan.charAt(0).toUpperCase() +
                      subscription.plan.slice(1)
                    } Plan`
                  : "No Plan"}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap size={18} className="text-brand-300" />
                <span className="text-gray-300">Product Limit</span>
              </div>
              <div className="flex flex-col items-end gap-1">
                <span className="text-gray-200 font-medium">
                  {subscription.productCount || 0} /{" "}
                  {subscription.productLimit === Infinity
                    ? "Unlimited"
                    : subscription.productLimit}{" "}
                  Products
                </span>
                {subscription.productLimit !== Infinity && (
                  <div className="w-32 h-2 bg-dark-400 rounded-full overflow-hidden">
                    <div
                      className={`h-full rounded-full ${getProgressBarColor(
                        subscription.productCount || 0,
                        subscription.productLimit
                      )}`}
                      style={{
                        width: `${Math.min(
                          ((subscription.productCount || 0) /
                            subscription.productLimit) *
                            100,
                          100
                        )}%`,
                      }}
                    ></div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield size={18} className="text-brand-300" />
                <span className="text-gray-300">Status</span>
              </div>
              <span
                className={`font-medium capitalize ${
                  subscription.status === "active"
                    ? "text-green-400"
                    : subscription.status === "past_due"
                    ? "text-yellow-400"
                    : subscription.status === "canceling"
                    ? "text-orange-400"
                    : subscription.status === "none"
                    ? "text-gray-400"
                    : "text-gray-400"
                }`}
              >
                {subscription.status === "none"
                  ? "inactive"
                  : subscription.status}
              </span>
            </div>

            {subscription.status === "active" && (
              <div className="pt-4 border-t border-gray-800">
                <button
                  onClick={handleCancelSubscription}
                  disabled={cancelingSubscription}
                  className="btn btn-secondary w-full"
                >
                  {cancelingSubscription ? (
                    <>
                      <Loader2 size={16} className="animate-spin mr-2" />
                      Canceling...
                    </>
                  ) : (
                    "Cancel Subscription"
                  )}
                </button>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Your subscription will remain active until the end of your
                  current billing period.
                </p>
              </div>
            )}

            {subscription.status === "canceling" && subscription.periodEnd && (
              <div className="pt-4 border-t border-gray-800">
                <div className="bg-yellow-500/10 border border-yellow-500/20 text-yellow-400 p-3 rounded-md text-sm">
                  <p>
                    Your subscription will be canceled on{" "}
                    <strong>{subscription.periodEnd}</strong>. You can continue
                    using all features until then.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Billing Interval Toggle */}
      <div className="flex justify-center mb-8">
        <div className="inline-flex items-center bg-dark-400/50 p-1 rounded-lg">
          <button
            onClick={() => setBillingInterval("month")}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingInterval === "month"
                ? "bg-brand-300 text-dark-400"
                : "text-gray-300 hover:text-white"
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setBillingInterval("year")}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingInterval === "year"
                ? "bg-brand-300 text-dark-400"
                : "text-gray-300 hover:text-white"
            }`}
          >
            Yearly
          </button>
        </div>
      </div>

      {/* Subscription Plans */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredPlans.map((plan) => {
          // Get the corresponding monthly plan if we're showing annual plans
          const monthlyPlan =
            billingInterval === "year" &&
            plan.id !== "free" &&
            !plan.id.includes("-annual")
              ? plan
              : plans.find((p) => p.id === plan.id.replace("-annual", ""));

          // Get the corresponding annual plan if we're showing monthly plans
          const annualPlan =
            billingInterval === "month" && plan.id !== "free"
              ? getAnnualPlan(plan)
              : null;

          // Calculate savings if there's an annual plan
          const savingsPercentage =
            annualPlan && monthlyPlan
              ? calculateSavings(monthlyPlan.price, annualPlan.price)
              : 0;

          return (
            <div
              key={plan.id}
              className={`card rounded-xl p-6 bg-dark-300/90 backdrop-blur-lg border-2 ${
                subscription?.plan === plan.id &&
                subscription?.status === "active"
                  ? "border-brand-300"
                  : "border-transparent hover:border-gray-700"
              } transition-colors`}
            >
              {subscription?.plan === plan.id &&
                subscription?.status === "active" && (
                  <div className="absolute -top-3 -right-3 bg-brand-300 text-dark-400 text-xs font-medium px-2 py-1 rounded-full">
                    Current Plan
                  </div>
                )}

              <div className="space-y-4">
                <div>
                  <h3 className="text-xl font-medium text-gray-100">
                    {plan.name.replace(" (Annual)", "")}
                  </h3>
                  <p className="text-gray-400 mt-1">{plan.description}</p>
                </div>

                <div className="flex flex-col space-y-2">
                  {plan.customPricing ? (
                    <div className="flex items-baseline">
                      <span className="text-3xl font-light text-gray-100">
                        Custom Pricing
                      </span>
                    </div>
                  ) : (
                    <>
                      <div className="flex items-baseline">
                        <span className="text-3xl font-light text-gray-100">
                          ${plan.price}
                        </span>
                        <span className="text-gray-400 ml-1">
                          /{plan.interval}
                        </span>
                      </div>

                      {/* Show savings for yearly plans */}
                      {plan.interval === "year" && savingsPercentage > 0 && (
                        <div className="flex items-center">
                          <span className="bg-green-500/20 text-green-400 text-xs font-medium px-2 py-1 rounded-full mr-2">
                            Save {savingsPercentage}%
                          </span>
                          <span className="text-gray-400 text-sm">
                            ${monthlyPlan?.price} × 12 = $
                            {monthlyPlan?.price * 12}
                          </span>
                        </div>
                      )}
                    </>
                  )}

                  {/* Show toggle to annual option for monthly plans */}
                  {billingInterval === "month" &&
                    plan.id !== "free" &&
                    annualPlan && (
                      <div
                        onClick={() => setBillingInterval("year")}
                        className="flex items-center text-sm text-brand-300 hover:text-brand-200 cursor-pointer"
                      >
                        <Calendar size={14} className="mr-1" />
                        <span>
                          Switch to yearly: ${annualPlan.price}/year (Save $
                          {monthlyPlan.price * 12 - annualPlan.price})
                        </span>
                      </div>
                    )}

                  {/* Show toggle to monthly option for yearly plans */}
                  {billingInterval === "year" &&
                    plan.id !== "free" &&
                    monthlyPlan &&
                    plan.id.includes("-annual") && (
                      <div
                        onClick={() => setBillingInterval("month")}
                        className="flex items-center text-sm text-brand-300 hover:text-brand-200 cursor-pointer"
                      >
                        <RefreshCw size={14} className="mr-1" />
                        <span>
                          Switch to monthly: ${monthlyPlan.price}/month
                        </span>
                      </div>
                    )}
                </div>

                <div className="bg-dark-400/50 px-3 py-2 rounded-md inline-block">
                  <span className="text-brand-300 font-medium">
                    {plan.productLimit === Infinity
                      ? "Unlimited"
                      : plan.productLimit}{" "}
                    Products
                  </span>
                </div>

                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check
                        size={18}
                        className="text-brand-300 shrink-0 mt-0.5"
                      />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="space-y-2">
                  <button
                    onClick={() => {
                      if (plan.id === "enterprise") {
                        // Open email client with subject line
                        window.location.href =
                          "mailto:<EMAIL>?subject=Enterprise Plan Inquiry";
                      } else if (plan.paymentLink) {
                        handleSubscribe(plan);
                      }
                    }}
                    disabled={
                      (!plan.paymentLink && plan.id !== "enterprise") || // Free plan has no payment link
                      (subscription?.plan === plan.id &&
                        ["active", "canceling"].includes(
                          subscription?.status || ""
                        ))
                    }
                    className={`btn w-full ${
                      !plan.paymentLink && plan.id !== "enterprise"
                        ? "btn-secondary cursor-not-allowed opacity-50"
                        : subscription?.plan === plan.id &&
                          ["active", "canceling"].includes(
                            subscription?.status || ""
                          )
                        ? "btn-secondary cursor-not-allowed opacity-50"
                        : "btn-primary"
                    }`}
                  >
                    {plan.id === "enterprise"
                      ? "Contact Us"
                      : !plan.paymentLink
                      ? "Free Plan"
                      : subscription?.plan === plan.id &&
                        ["active", "canceling"].includes(
                          subscription?.status || ""
                        )
                      ? "Current Plan"
                      : subscription?.status === "canceling" &&
                        subscription?.plan === plan.id
                      ? "Renew Subscription"
                      : "Subscribe"}
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Benefits Section */}
      <div className="card rounded-xl p-6 bg-dark-300/90 backdrop-blur-lg mt-8">
        <h2 className="text-xl font-light text-gray-200 mb-6">
          Why Subscribe?
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-3">
            <div className="w-10 h-10 rounded-full bg-brand-500/20 flex items-center justify-center">
              <Zap size={20} className="text-brand-300" />
            </div>
            <h3 className="text-lg font-medium text-gray-200">More Products</h3>
            <p className="text-gray-400">
              Upgrade to access more product slots - from 25 products with Basic
              to unlimited with Enterprise.
            </p>
          </div>

          <div className="space-y-3">
            <div className="w-10 h-10 rounded-full bg-brand-500/20 flex items-center justify-center">
              <Star size={20} className="text-brand-300" />
            </div>
            <h3 className="text-lg font-medium text-gray-200">
              Premium Features
            </h3>
            <p className="text-gray-400">
              Access advanced customization options and integrations with
              e-commerce platforms.
            </p>
          </div>

          <div className="space-y-3">
            <div className="w-10 h-10 rounded-full bg-brand-500/20 flex items-center justify-center">
              <Shield size={20} className="text-brand-300" />
            </div>
            <h3 className="text-lg font-medium text-gray-200">
              Priority Support
            </h3>
            <p className="text-gray-400">
              Get faster responses and dedicated assistance for all your 3D
              modeling needs.
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="card rounded-xl p-6 bg-dark-300/90 backdrop-blur-lg mt-8">
        <h2 className="text-xl font-light text-gray-200 mb-6">
          Frequently Asked Questions
        </h2>

        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-200">
              Can I cancel my subscription at any time?
            </h3>
            <p className="text-gray-400">
              Yes, you can cancel your subscription at any time. Your
              subscription will remain active until the end of your current
              billing period.
            </p>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-200">
              How do I change my subscription plan?
            </h3>
            <p className="text-gray-400">
              To change your plan, first cancel your current subscription, then
              subscribe to the new plan once your current subscription period
              ends.
            </p>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-200">
              What payment methods are accepted?
            </h3>
            <p className="text-gray-400">
              We accept all major credit cards including Visa, Mastercard,
              American Express, and Discover through our secure payment
              processor, Stripe.
            </p>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-200">
              What happens if I reach my product limit?
            </h3>
            <p className="text-gray-400">
              When you reach your product limit, you'll need to upgrade to a
              higher tier plan to add more products. You can continue to manage
              your existing products, but won't be able to add new ones until
              you upgrade or remove some existing products.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Subscription;
