const hdrSchema = require(".././models/HDR");
const { s3Upload, uploadToS3NewVersion } = require("../config/s3Service");
const resHandle = require("../utils/responseHandle");
class Controller {
  createHdr = async (req, res) => {
    const bodyData = req.body;
    let inputs = {
      name: bodyData.name,
      url: bodyData.url,
      icon: bodyData.icon,
      rotationY: bodyData.rotationY,
      intensity: bodyData.intensity,
      category: bodyData.category,
      library: bodyData.library,
    };

    if (typeof bodyData.url === "string") {
      inputs.url = bodyData.url;
    } else {
      const data = await s3Upload(
        req.files.url[0],
        `HDRI/${req.body.category}`,
      );
      const url = data?.Location;
      inputs.url = url;
    }
    if (typeof bodyData.icon === "string") {
      inputs.icon = bodyData.icon;
    } else {
      const data = await s3Upload(req.files.icon[0], "iconurl");
      const url = data?.Location;
      inputs.icon = url;
    }
    let newHdr = new hdrSchema(inputs);
    await newHdr.save();
    return res.status(200).json({ data: newHdr });
  };
  getAllHdr = async (req, res) => {
    try {
      let AllHdr = await hdrSchema.find({ isShowroomDefault: true });
      return res.status(200).json({
        message: "Hdrs retrieved Successfully",
        success: true,
        data: AllHdr,
      });
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getHdrById = async (req, res) => {
    try {
      const { id } = req.params;
      const HdrToBeRetrieved = await hdrSchema.findOne({ _id: id });
      if (!HdrToBeRetrieved)
        return resHandle.handleError(res, 404, `Hdr Not Found`);
      resHandle.handleData(
        res,
        200,
        "Hdr Retrieved Successfully",
        true,
        HdrToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  deleteHdrById = async (req, res) => {
    try {
      const { id } = req.params;
      let deletedHdr = await hdrSchema.findByIdAndDelete(id);
      if (!deletedHdr) return resHandle.handleError(res, 404, "Hdr Not Found");
      resHandle.handleData(res, 200, "HDR Deleted Successfully", true);
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  updateById = async (req, res) => {
    const { id } = req.params;
    const hdr = await hdrSchema.findByIdAndUpdate(id, req.body, { new: true });
    if (!hdr) {
      return res.status(404).json("Hdr Not Found");
    }
    return res.status(200).json({ data: hdr });
  };
  getHdrWithAsset = async (req, res) => {
    try {
      const { id } = req.params;

      let HdrToBeRetrieved = await hdrSchema
        .findOne({ _id: id })
        .populate("asset_library");
      if (!HdrToBeRetrieved)
        return resHandle.handleError(res, 404, `Hdr Not Found`);
      resHandle.handleData(
        res,
        200,
        "Hdr Retrieved Successfully",
        true,
        HdrToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
}
const controller = new Controller();
module.exports = controller;
