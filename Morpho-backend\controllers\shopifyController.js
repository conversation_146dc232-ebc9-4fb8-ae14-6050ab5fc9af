const GlbModel = require("../models/GlbModel");
const projectSchema = require("../models/Project");
const assetLibraryModel = require("../models/AssetLibrary");
const axios = require("axios"); // For making HTTP requests
const expressError = require("../errors/expressError");
const resHandle = require("../utils/responseHandle");

exports.getAllProducts = async (req, res, next) => {
  // Validate shopify client
  if (!req.shopifyClient) {
    throw new expressError(
      "Shopify client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Parse limit parameter
  const limit = parseInt(req.query.limit, 10) || 5; // Default to 5 products if no limit is provided

  // Fetch products from Shopify
  const products = await req.shopifyClient.product.list({ limit });

  // Check if products exist
  if (!products || products.length === 0) {
    throw new expressError(
      "No products found in Shopify",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Return success response
  return resHandle.handleData(
    res,
    200,
    "Products retrieved successfully",
    true,
    products
  );
};

exports.getProductsForSelection = async (req, res, next) => {
  // Validate shopify client
  if (!req.shopifyClient) {
    throw new expressError(
      "Shopify client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Validate user authentication
  const userId = req.user;
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Parse parameters
  const limit = parseInt(req.body.limit, 10) || 250; // Default to 250 products (Shopify's API limit)
  const page = parseInt(req.body.page, 10) || 1; // For pagination

  // Get products from Shopify
  const products = await req.shopifyClient.product.list({ limit });

  // Check if products exist
  if (!products || products.length === 0) {
    throw new expressError(
      "No products found on Shopify",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Get the user's project
  const project = await projectSchema.findOne({ client: userId }).lean();
  if (!project) {
    throw new expressError(
      "Project not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Find existing products in Morpho that are already imported from Shopify
  const existingProducts = await GlbModel.find({
    integrationKey: { $ne: null, $exists: true },
    project: project._id,
  }).lean();

  // Create a map of existing integration keys for quick lookup
  const existingIntegrationKeys = new Set(
    existingProducts.map((product) => product.integrationKey)
  );

  // Format the products for the frontend
  const formattedProducts = products.map((product) => ({
    id: product.id.toString(),
    title: product.title,
    description: product.body_html,
    price: product.variants[0]?.price || "0.00",
    sku: product.variants[0]?.sku || "",
    imageUrl: product.image?.src || null,
    status: product.status,
    alreadyImported: existingIntegrationKeys.has(product.id.toString()),
  }));

  // Return success response
  return resHandle.handleData(
    res,
    200,
    "Products for selection retrieved successfully",
    true,
    {
      products: formattedProducts,
      total: formattedProducts.length,
    }
  );
};

exports.getStatistics = async (req, res, next) => {
  // Validate shopify client
  if (!req.shopifyClient) {
    throw new expressError(
      "Shopify client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Fetch the orders for the last 60 days (Shopify API automatically limits the date range)
  const orders = await req.shopifyClient.order.list({
    status: "any", // Include all orders (open, closed, cancelled, etc.)
    limit: 250, // Shopify's API limit per request
  });

  // Check if orders exist
  if (!orders || orders.length === 0) {
    throw new expressError(
      "No orders found in Shopify",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  const productStats = {};

  // Loop through the orders to collect data on sold products
  orders.forEach((order) => {
    order.line_items.forEach((item) => {
      const { product_id, title, quantity, price } = item;

      // Initialize product statistics if not already set
      if (!productStats[product_id]) {
        productStats[product_id] = {
          productId: product_id,
          name: title,
          totalSold: 0,
          revenue: 0,
        };
      }

      // Update the product's total sold and revenue
      productStats[product_id].totalSold += quantity;
      productStats[product_id].revenue += quantity * parseFloat(price);
    });
  });

  // Sort the products by the total quantity sold in descending order
  const topProducts = Object.values(productStats).sort(
    (a, b) => b.totalSold - a.totalSold
  );

  // Return success response
  return resHandle.handleData(
    res,
    200,
    "Statistics retrieved successfully",
    true,
    {
      totalOrders: orders.length,
      topProducts,
    }
  );
};

exports.saveAllProducts = async (req, res, next) => {
  try {
    const userId = req.user;
    // Get the user's project
    const project = await projectSchema.findOne({ client: userId }).lean();
    if (!project) {
      return res
        .status(404)
        .json({ success: false, message: "Project not found" });
    }
    const assetLibrary = await assetLibraryModel.findOne({
      project: project._id,
    });

    if (!assetLibrary) {
      return res.status(404).json({
        success: false,
        message: "Asset library not found",
      });
    }

    const assetLibraryId = assetLibrary._id;
    const projectId = project._id;

    // Check if specific product IDs were provided
    const { selectedProductIds } = req.body;
    let shopifyProducts;

    if (
      selectedProductIds &&
      Array.isArray(selectedProductIds) &&
      selectedProductIds.length > 0
    ) {
      // Fetch only the selected products

      // Fetch products one by one since Shopify API doesn't support bulk fetching by IDs
      const productPromises = selectedProductIds.map((id) =>
        req.shopifyClient.product.get(id).catch((err) => {
          return null;
        })
      );
      //test

      shopifyProducts = (await Promise.all(productPromises)).filter(Boolean);
    } else {
      // Fetch all products (up to 250, which is Shopify's limit)
      shopifyProducts = await req.shopifyClient.product.list({ limit: 250 });
    }

    if (!shopifyProducts || shopifyProducts.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No products found on Shopify.",
      });
    }

    const skippedProducts = [];
    const productPromises = shopifyProducts.map(async (product) => {
      try {
        // Check if the product already exists in the database
        const exists = await GlbModel.findOne({
          integrationKey: product.id.toString(),
        });
        if (exists) {
          skippedProducts.push({
            name: product.title,
            reason: "Already exists.",
          });
          return null; // Skip existing products
        }

        // Check if the product has a valid image URL
        if (!product.image?.src) {
          skippedProducts.push({
            name: product.title,
            id: product.id,
            reason: "Missing image URL.",
          });
          return null; // Skip products without images
        }

        // Extract sizes and width from Shopify product options
        const sizes = [];
        const width = [];
        if (product.options) {
          product.options.forEach((option) => {
            if (option.name.toLowerCase() === "size") {
              sizes.push(...option.values);
            } else if (option.name.toLowerCase() === "width") {
              width.push(...option.values);
            }
          });
        }

        // Clean the product description
        const cleanDescription = product.body_html
          ? product.body_html.replace(/<\/?[^>]+(>|$)/g, "").trim()
          : "";

        // Create productOptions array only if there are multiple variants
        let productOptions = [];
        if (product.variants && product.variants.length > 1) {
          productOptions = product.variants.map((variant) => ({
            shopifyVariantId: variant.id.toString(),
            url: "", // Empty url as requested
          }));
        }

        // Format product for database insertion
        return {
          name: product.title,
          description: cleanDescription,
          imageUrl: product.image.src,
          price: parseFloat(product.variants[0]?.price || 0),
          sku: product.variants[0]?.sku || "",
          published: product.status === "active",
          integrationKey: product.id.toString(),
          project: projectId,
          asset_library: assetLibraryId,
          sizes,
          width,
          customizations: [],
          productOptions: productOptions, // Add productOptions here
          flag: product.variants[0]?.sku ? null : "Sku is required.",
        };
      } catch (error) {
        skippedProducts.push({
          name: product.title,
          id: product.id,
          reason: error.message || "Unknown error.",
        });
        return null; // Skip product if error occurs
      }
    });

    const formattedProducts = await Promise.all(productPromises);
    const newProducts = formattedProducts.filter(Boolean); // Remove null values

    if (newProducts.length > 0) {
      await GlbModel.insertMany(newProducts);
    }

    res.status(201).json({
      success: true,
      message: `${newProducts.length} new product(s) added.`,
      data: newProducts,
      skippedProducts,
    });
  } catch (error) {
    console.error("Error in saveAllProducts:", error);
    res.status(500).json({
      success: false,
      message: "An error occurred while importing products from Shopify",
      error: error.message,
    });
  }
};

exports.getProductTest = async (req, res, next) => {
  try {
    const { productId, shopifyVariantId } = req.query;

    // Find the product by integrationKey (Shopify product ID)
    const product = await GlbModel.findOne({
      integrationKey: productId,
    }).lean();

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    let url;

    // If variant ID is provided, try to find the specific variant URL
    if (
      shopifyVariantId &&
      product.productOptions &&
      product.productOptions.length > 0
    ) {
      const variant = product.productOptions.find(
        (option) => option.shopifyVariantId === shopifyVariantId
      );

      // If variant found and has a URL, use it
      if (variant && variant.url) {
        url = variant.url;
      } else {
        // Fallback to the main product URL if variant doesn't have a URL
        url = product.url;
      }
    } else {
      // If no variant ID provided, use the main product URL
      url = product.url;
    }

    return res.json({
      success: true,
      url,
    });
  } catch (error) {
    console.error("Error in getProductTest:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while retrieving the product",
      error: error.message,
    });
  }
};

exports.syncProductsToShopify = async (req, res, next) => {
  try {
    const userId = req.user;
    const { productIds } = req.body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Please provide an array of product IDs to sync",
      });
    }

    // Get the user's project
    const project = await projectSchema.findOne({ client: userId }).lean();
    if (!project) {
      return res.status(404).json({
        success: false,
        message: "Project not found",
      });
    }

    // Fetch the selected products from Morpho database
    const morphoProducts = await GlbModel.find({
      _id: { $in: productIds },
      project: project._id,
    }).lean();

    if (!morphoProducts || morphoProducts.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No products found with the provided IDs",
      });
    }

    const results = {
      success: [],
      failed: [],
    };

    // Process each product
    for (const product of morphoProducts) {
      try {
        // Prepare product data for Shopify
        const shopifyProductData = {
          title: product.name,
          body_html: product.description || "",
          vendor: project.name || "Morpho",
          product_type: "Morpho 3D Model",
          status: product.published ? "active" : "draft",
          variants: [
            {
              price: product.price.toString(),
              sku: product.sku || "",
              inventory_management: "shopify",
              inventory_policy: "continue",
              inventory_quantity: 100, // Default inventory
              requires_shipping: false,
            },
          ],
          images: [],
        };

        // Add image if available
        if (product.imageUrl) {
          try {
            // For Shopify, we'll create the product first without images
            // and then add the image separately after the product is created
            // This approach often works better with external image URLs
            shopifyProductData.images = [];
          } catch (imageError) {
            console.error("Error processing image URL:", imageError.message);
            shopifyProductData.images = [];
          }
        }

        // Check if product already exists on Shopify (by integrationKey)
        let shopifyProduct;
        let isUpdate = false;

        if (product.integrationKey) {
          try {
            // Try to fetch the product from Shopify
            shopifyProduct = await req.shopifyClient.product.get(
              product.integrationKey
            );
            isUpdate = true;
          } catch (error) {
            // Product doesn't exist on Shopify yet
            isUpdate = false;
          }
        }

        // Create or update the product on Shopify
        try {
          if (isUpdate && shopifyProduct) {
            // Update existing product

            shopifyProduct = await req.shopifyClient.product.update(
              product.integrationKey,
              shopifyProductData
            );
          } else {
            // Create new product
            shopifyProduct = await req.shopifyClient.product.create(
              shopifyProductData
            );

            // Update the Morpho product with the Shopify integration key
            await GlbModel.findByIdAndUpdate(product._id, {
              integrationKey: shopifyProduct.id.toString(),
            });
          }

          // Now that the product is created, try to add the image
          if (product.imageUrl) {
            try {
              // Add a small delay to ensure the product is fully created in Shopify
              await new Promise((resolve) => setTimeout(resolve, 1000));

              // Create a direct image attachment using the Shopify Admin API

              // Try a completely different approach - download the image and upload as base64
              try {
                // Download the image from S3
                const imageResponse = await axios({
                  method: "GET",
                  url: product.imageUrl,
                  responseType: "arraybuffer",
                });

                // Get the content type
                const contentType = imageResponse.headers["content-type"];

                // Convert the image to base64
                const imageBase64 = Buffer.from(
                  imageResponse.data,
                  "binary"
                ).toString("base64");
                const base64Image = `data:${contentType};base64,${imageBase64}`;

                // Use the Shopify client to create the image
                const productImage =
                  await req.shopifyClient.productImage.create(
                    shopifyProduct.id,
                    {
                      attachment: imageBase64,
                      filename:
                        product.imageUrl.split("/").pop() ||
                        "product-image.jpg",
                      alt: product.name || "Product image",
                    }
                  );
              } catch (directApiError) {
                console.error(
                  "Error using direct API approach:",
                  directApiError.message
                );

                // Store the image URL in metafields as a fallback

                try {
                  await req.shopifyClient.metafield.create({
                    key: "morpho_image_url",
                    value: product.imageUrl,
                    value_type: "string",
                    namespace: "morpho",
                    owner_resource: "product",
                    owner_id: shopifyProduct.id,
                  });
                } catch (metafieldError) {
                  console.error(
                    "Failed to store image URL in metafields:",
                    metafieldError.message
                  );
                }

                // If all else fails, log a message with instructions
              }
            } catch (imageError) {
              console.error(
                "Failed to add image to product:",
                imageError.message
              );
              // Continue without the image - the product was still created successfully
            }
          }
        } catch (productError) {
          console.error(
            "Error creating/updating Shopify product:",
            productError
          );
          throw productError;
        }

        // Add to success results
        results.success.push({
          morphoId: product._id,
          shopifyId: shopifyProduct.id,
          name: product.name,
          action: isUpdate ? "updated" : "created",
        });
      } catch (error) {
        // Add to failed results
        results.failed.push({
          morphoId: product._id,
          name: product.name,
          error: error.message || "Unknown error",
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: `Successfully processed ${results.success.length} products. Failed: ${results.failed.length} products.`,
      data: results,
    });
  } catch (error) {
    console.error("Error in syncProductsToShopify:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while syncing products to Shopify",
      error: error.message,
    });
  }
};
