const User = require("../models/User");

/**
 * Middleware to verify if a user is within their product limit
 * This middleware should be used after verifyJWT middleware
 * as it relies on req.user being set
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 * @returns {void}
 */
const verifyProductLimit = async (req, res, next) => {
  try {
    // Get user ID from the request (set by verifyJWT middleware)
    const userId = req.user;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized: Authentication required",
      });
    }

    // Find the user and check their product limit
    const user = await User.findById(userId).select(
      "subscriptionPlan productLimit"
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Get the current product count for the user
    // This is a placeholder - you'll need to implement the actual count logic
    // based on your database structure
    const currentProductCount = await getCurrentProductCount(userId);

    // Check if the user is trying to exceed their product limit
    if (currentProductCount >= user.productLimit) {
      return res.status(403).json({
        success: false,
        message:
          "Product limit reached: Please upgrade your subscription to add more products",
        currentCount: currentProductCount,
        limit: user.productLimit,
        plan: user.subscriptionPlan,
      });
    }

    // If the user is within their product limit, proceed to the next middleware
    next();
  } catch (error) {
    console.error("Error verifying product limit:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while verifying product limit",
      error: error.message,
    });
  }
};

/**
 * Helper function to get the current product count for a user
 * Counts products associated with the user through collections and projects
 *
 * @param {string} userId - The user ID
 * @returns {Promise<number>} - The current product count
 */
async function getCurrentProductCount(userId) {
  const mongoose = require("mongoose");
  const GlbModel = require("../models/GlbModel");
  const Collection = require("../models/Collection");
  const Project = require("../models/Project");

  try {
    // Convert userId to ObjectId
    const userObjectId = new mongoose.Types.ObjectId(userId);

    // Count products in user's collections
    const collections = await Collection.find({ user: userObjectId });
    const collectionProductIds = collections.flatMap(
      (collection) => collection.products || []
    );

    // Count products in user's projects
    const userProjects = await Project.find({ client: userObjectId });
    const projectIds = userProjects.map((project) => project._id);

    // Count products associated with user's projects
    const projectProductsCount = await GlbModel.countDocuments({
      project: { $in: projectIds },
    });

    // Count unique products (some might be in both collections and projects)
    const collectionProductsCount = collectionProductIds.length;

    // Use the higher count as the user's product count
    // This is a simplification - in a real system, you might want to count unique products
    return Math.max(projectProductsCount, collectionProductsCount);
  } catch (error) {
    console.error("Error counting products:", error);
    // Return 0 in case of error to prevent blocking access
    return 0;
  }
}

module.exports = verifyProductLimit;
module.exports.getCurrentProductCount = getCurrentProductCount;
