const crypto = require('crypto');

/**
 * Shopify Webhook Verification Middleware
 * Verifies HMAC signatures according to Shopify's requirements
 * https://shopify.dev/docs/apps/build/webhooks/subscribe/https#step-2-validate-the-origin-of-your-webhook
 */
const verifyShopifyWebhook = (req, res, next) => {
  try {
    // Get HMAC header
    const hmacHeader = req.headers['x-shopify-hmac-sha256'];
    if (!hmacHeader) {
      return res.status(401).send('HMAC validation failed: missing header');
    }

    // Get raw request body
    const rawBody = req.body; // express.raw() provides buffer directly
    if (!rawBody) {
      return res.status(401).send('HMAC validation failed: missing body');
    }

    // Verify webhook secret exists
    if (!process.env.SHOPIFY_API_SECRET) {
      console.error('Missing Shopify API secret');
      return res.status(401).send('HMAC validation failed: configuration error');
    }

    // Calculate HMAC
    const generatedHash = crypto
      .createHmac('sha256', process.env.SHOPIFY_API_SECRET)
      .update(rawBody) // Use buffer directly
      .digest('base64');

    // Compare signatures
    const hashEquals = crypto.timingSafeEqual(
      Buffer.from(generatedHash),
      Buffer.from(hmacHeader)
    );

    if (!hashEquals) {
      return res.status(401).send('HMAC validation failed');
    }

    // Parse body for subsequent middleware
    try {
      req.body = JSON.parse(rawBody);
    } catch (parseError) {
      return res.status(401).send('Invalid webhook body');
    }

    // Attach Shopify metadata
    req.shopify = {
      shop: req.headers['x-shopify-shop-domain'],
      topic: req.headers['x-shopify-topic'],
      hmac: hmacHeader,
      apiVersion: req.headers['x-shopify-api-version']
    };

    next();
  } catch (error) {
    console.error('Webhook verification error:', error);
    return res.status(401).send('HMAC validation failed');
  }
};

module.exports = verifyShopifyWebhook; 