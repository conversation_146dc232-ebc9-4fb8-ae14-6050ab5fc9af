import { storage } from "../utils/storage";
import type { EcommerceProduct, ProductImportResult } from "../types/ecommerce";

const API_URL = "https://api.modularcx.link/morpho";
const TIMEOUT_MS = 60000;
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 2000;

const IMPORT_ERROR_CODES = {
  AUTH_FAILED: "auth_failed",
  INVALID_STORE: "invalid_store",
  STORE_NOT_FOUND: "store_not_found",
  INVALID_CREDENTIALS: "invalid_credentials",
  RATE_LIMIT: "rate_limit",
  API_ERROR: "api_error",
  NETWORK_ERROR: "network_error",
} as const;

const SHOPIFY_PATTERNS = {
  API_KEY: /^[a-f0-9]{32}$/i,
  API_SECRET: /^shpss_[a-f0-9]{32,64}$/i,
  ACCESS_TOKEN: /^shpat_[a-f0-9]{32,64}$/i,
  ADMIN_ACCESS_TOKEN: /^shpca_[a-f0-9]{32,64}$/i,
};

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

async function checkConnectivity(): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(`${API_URL}/ping`, {
      method: "HEAD",
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch {
    return false;
  }
}

async function makeRequest(
  url: string,
  options: RequestInit,
  retryAttempt = 0
): Promise<Response> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT_MS);

    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.status === 429 && retryAttempt < RETRY_ATTEMPTS) {
      await sleep(RETRY_DELAY * (retryAttempt + 1));
      return makeRequest(url, options, retryAttempt + 1);
    }

    return response;
  } catch (error) {
    if (retryAttempt < RETRY_ATTEMPTS) {
      await sleep(RETRY_DELAY * (retryAttempt + 1));
      return makeRequest(url, options, retryAttempt + 1);
    }
    throw error;
  }
}

export const ecommerceService = {
  async importProducts(
    platform: string,
    credentials: Record<string, string>
  ): Promise<ProductImportResult> {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
        errorCode: IMPORT_ERROR_CODES.AUTH_FAILED,
        products: [],
        total: 0,
        imported: 0,
      };
    }

    try {
      const headers: Record<string, string> = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      // For Shopify specifically, modify the request to match your API
      if (platform === "shopify") {
        try {
          const response = await fetch(`${API_URL}/shopify/save-products`, {
            method: "POST",
            headers,
            body: JSON.stringify({
              shopName: credentials.shopName,
              apiKey: credentials.apiKey,
              password: credentials.password,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            return {
              success: false,
              error:
                errorData.message || `HTTP error! status: ${response.status}`,
              errorCode:
                response.status === 401
                  ? IMPORT_ERROR_CODES.AUTH_FAILED
                  : response.status === 404
                  ? IMPORT_ERROR_CODES.STORE_NOT_FOUND
                  : IMPORT_ERROR_CODES.API_ERROR,
              products: [],
              total: 0,
              imported: 0,
            };
          }

          const data = await response.json();
          return {
            success: true,
            products: data.products || [],
            total: data.total || 0,
            imported: data.imported || 0,
            failedProducts: data.failedProducts || [],
          };
        } catch (error) {
          const isNetworkError =
            error instanceof TypeError &&
            (error.message === "Failed to fetch" ||
              error.message.includes("NetworkError"));

          return {
            success: false,
            error: isNetworkError
              ? "Unable to connect to the server. Please check your internet connection."
              : error instanceof Error
              ? error.message
              : "Import failed",
            errorCode: isNetworkError
              ? IMPORT_ERROR_CODES.NETWORK_ERROR
              : IMPORT_ERROR_CODES.API_ERROR,
            products: [],
            total: 0,
            imported: 0,
          };
        }
      } else {
        // Handle other platforms or show not implemented
        return {
          success: false,
          error: `Import for ${platform} is not implemented yet`,
          errorCode: IMPORT_ERROR_CODES.API_ERROR,
          products: [],
          total: 0,
          imported: 0,
        };
      }
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        errorCode: IMPORT_ERROR_CODES.API_ERROR,
        products: [],
        total: 0,
        imported: 0,
      };
    }
  },

  /**
   * Resyncs products from Shopify to update existing products or add new ones
   * @param credentials The shopify credentials (apiKey, password, shopName)
   * @returns ProductImportResult
   */
  resyncShopifyProducts: async (
    credentials: Record<string, string>
  ): Promise<ProductImportResult> => {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
        errorCode: "auth_failed",
        products: [],
        total: 0,
        imported: 0,
      };
    }

    try {
      const response = await fetch(`${API_URL}/shopify/save-products`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          shopName: credentials.shopName,
          apiKey: credentials.apiKey,
          password: credentials.password,
          resync: true, // Add a flag to indicate this is a resync operation
        }),
      });

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: `HTTP error! status: ${response.status}` }));
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
          errorCode:
            response.status === 401
              ? IMPORT_ERROR_CODES.AUTH_FAILED
              : response.status === 404
              ? IMPORT_ERROR_CODES.STORE_NOT_FOUND
              : IMPORT_ERROR_CODES.API_ERROR,
          products: [],
          total: 0,
          imported: 0,
        };
      }

      const data = await response.json();
      return {
        success: true,
        products: data.products || [],
        total: data.total || 0,
        imported: data.imported || 0,
        failedProducts: data.failedProducts || [],
      };
    } catch (error) {
      const isNetworkError =
        error instanceof TypeError &&
        (error.message === "Failed to fetch" ||
          error.message.includes("NetworkError"));

      return {
        success: false,
        error: isNetworkError
          ? "Unable to connect to the server. Please check your internet connection."
          : error instanceof Error
          ? error.message
          : "An unexpected error occurred during resync",
        errorCode: isNetworkError
          ? IMPORT_ERROR_CODES.NETWORK_ERROR
          : IMPORT_ERROR_CODES.API_ERROR,
        products: [],
        total: 0,
        imported: 0,
      };
    }
  },

  /**
   * Gets products from Shopify for selection before importing
   * @param credentials The shopify credentials (apiKey, password, shopName)
   * @returns List of products from Shopify with import status
   */
  getShopifyProductsForSelection: async (credentials: {
    shopName: string;
    apiKey: string;
    password: string;
  }) => {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
        data: [],
      };
    }

    try {
      const response = await fetch(
        `${API_URL}/shopify/products-for-selection`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: JSON.stringify({
            shopName: credentials.shopName,
            apiKey: credentials.apiKey,
            password: credentials.password,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: `HTTP error! status: ${response.status}` }));
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
          data: [],
        };
      }

      const responseData = await response.json();

      // The backend returns data in the format { message, success, data: { products: [...], total: n } }
      if (responseData && responseData.data && responseData.data.products) {
        return {
          success: true,
          data: {
            products: Array.isArray(responseData.data.products)
              ? responseData.data.products
              : [],
            total: responseData.data.total || 0,
          },
        };
      } else {
        return {
          success: false,
          error: "Invalid response format from server",
          data: { products: [], total: 0 },
        };
      }
    } catch (error) {
      const isNetworkError =
        error instanceof TypeError &&
        (error.message === "Failed to fetch" ||
          error.message.includes("NetworkError"));

      return {
        success: false,
        error: isNetworkError
          ? "Unable to connect to the server. Please check your internet connection."
          : error instanceof Error
          ? error.message
          : "An unexpected error occurred while fetching products",
        data: [],
      };
    }
  },

  /**
   * Imports selected products from Shopify to Morpho
   * @param options Object containing shopify credentials and selected product IDs to import
   * @returns Result of the import operation
   */
  importSelectedShopifyProducts: async (options: {
    shopName: string;
    apiKey: string;
    password: string;
    selectedProductIds: string[];
  }) => {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
        errorCode: IMPORT_ERROR_CODES.AUTH_FAILED,
        products: [],
        total: 0,
        imported: 0,
      };
    }

    try {
      const response = await fetch(`${API_URL}/shopify/save-products`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          shopName: options.shopName,
          apiKey: options.apiKey,
          password: options.password,
          selectedProductIds: options.selectedProductIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: `HTTP error! status: ${response.status}` }));
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
          errorCode:
            response.status === 401
              ? IMPORT_ERROR_CODES.AUTH_FAILED
              : response.status === 404
              ? IMPORT_ERROR_CODES.STORE_NOT_FOUND
              : IMPORT_ERROR_CODES.API_ERROR,
          products: [],
          total: options.selectedProductIds.length,
          imported: 0,
        };
      }

      const data = await response.json();
      return {
        success: true,
        message: data.message,
        products: data.data || [],
        skippedProducts: data.skippedProducts || [],
        total: options.selectedProductIds.length,
        imported: data.data ? data.data.length : 0,
        failedProducts: data.skippedProducts || [],
      };
    } catch (error) {
      const isNetworkError =
        error instanceof TypeError &&
        (error.message === "Failed to fetch" ||
          error.message.includes("NetworkError"));

      return {
        success: false,
        error: isNetworkError
          ? "Unable to connect to the server. Please check your internet connection."
          : error instanceof Error
          ? error.message
          : "An unexpected error occurred during import",
        errorCode: isNetworkError
          ? IMPORT_ERROR_CODES.NETWORK_ERROR
          : IMPORT_ERROR_CODES.API_ERROR,
        products: [],
        total: options.selectedProductIds.length,
        imported: 0,
      };
    }
  },

  /**
   * Syncs selected products from Morpho to Shopify
   * @param options Object containing shopify credentials and product IDs to sync
   * @returns Result of the sync operation
   */
  syncProductsToShopify: async (options: {
    shopName: string;
    apiKey: string;
    password: string;
    productIds: string[];
  }) => {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
        errorCode: IMPORT_ERROR_CODES.AUTH_FAILED,
      };
    }

    try {
      const response = await fetch(`${API_URL}/shopify/sync-to-shopify`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          shopName: options.shopName,
          apiKey: options.apiKey,
          password: options.password,
          productIds: options.productIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: `HTTP error! status: ${response.status}` }));
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
          errorCode:
            response.status === 401
              ? IMPORT_ERROR_CODES.AUTH_FAILED
              : response.status === 404
              ? IMPORT_ERROR_CODES.STORE_NOT_FOUND
              : IMPORT_ERROR_CODES.API_ERROR,
        };
      }

      const data = await response.json();
      return {
        success: true,
        message: data.message,
        data: data.data,
      };
    } catch (error) {
      const isNetworkError =
        error instanceof TypeError &&
        (error.message === "Failed to fetch" ||
          error.message.includes("NetworkError"));

      return {
        success: false,
        error: isNetworkError
          ? "Unable to connect to the server. Please check your internet connection."
          : error instanceof Error
          ? error.message
          : "An unexpected error occurred during sync",
        errorCode: isNetworkError
          ? IMPORT_ERROR_CODES.NETWORK_ERROR
          : IMPORT_ERROR_CODES.API_ERROR,
      };
    }
  },
};
