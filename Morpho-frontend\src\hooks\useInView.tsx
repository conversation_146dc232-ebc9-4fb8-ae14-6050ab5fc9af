import { useState, useEffect, useRef, RefObject } from 'react';

interface IntersectionOptions {
  threshold?: number;
  rootMargin?: string;
}

export const useInView = (
  ref: RefObject<Element>, 
  options: IntersectionOptions = { threshold: 0 }
): boolean => {
  const [isInView, setIsInView] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  
  // Add once option to observe only once when element comes into view
  const once = true;

  useEffect(() => {
    const element = ref.current;
    
    // Skip if no element
    if (!element) return;
    
    // Cleanup previous observer
    if (observerRef.current) {
      observerRef.current.disconnect();
    }
    
    // Create observer with simplified callback
    observerRef.current = new IntersectionObserver(([entry]) => {
      // Set view state directly without delays
      if (entry.isIntersecting) {
        setIsInView(true);
        
        // If once is true, disconnect observer after element comes into view
        if (once && observerRef.current) {
          observerRef.current.disconnect();
        }
      } else if (!once) {
        setIsInView(false);
      }
    }, options);
    
    observerRef.current.observe(element);
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [ref, options.threshold, options.rootMargin, once]);

  return isInView;
};