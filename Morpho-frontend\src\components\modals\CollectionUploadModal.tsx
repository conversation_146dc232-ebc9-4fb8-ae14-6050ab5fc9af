import React, { useState, useEffect } from "react";
import { Upload, X, AlertCircle, Plus, Search } from "lucide-react";
import { useModels } from "../../hooks/useModels";
import { useCollections } from "../../hooks/useCollection";
import { Model3D } from "../../types/models";

interface CollectionUploadModalProps {
  onClose: () => void;
  onSuccess?: () => void;
  collections?: Array<{ id: string; name: string }>;
  isNewCollection?: boolean;
}

const CollectionUploadModal: React.FC<CollectionUploadModalProps> = ({
  onClose,
  onSuccess,
  collections = [],
  isNewCollection = false,
}) => {
  const { models, loading: modelsLoading } = useModels(); // Get models from the library
  const { createCollection, addProductsToCollection } = useCollections();

  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [formData, setFormData] = useState({
    collectionName: "",
    collectionDescription: "",
    selectedCollection: "",
    category: "",
  });

  // Filter models based on search query
  const filteredModels = models.filter(
    (model) =>
      model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (model.description &&
        model.description.toLowerCase().includes(searchQuery.toLowerCase())),
  );

  const handleModelSelect = (modelId: string) => {
    setSelectedModels((prev) => {
      if (prev.includes(modelId)) {
        return prev.filter((id) => id !== modelId);
      } else {
        return [...prev, modelId];
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      if (isNewCollection) {
        // Validate required fields
        if (!formData.collectionName || !formData.collectionDescription) {
          setError("Collection name and description are required");
          setLoading(false);
          return;
        }

        // First create the collection
        const collectionResponse = await createCollection({
          name: formData.collectionName,
          description: formData.collectionDescription,
        });

        // Then, if there are selected models, add them to the new collection
        if (selectedModels.length > 0) {
          const collectionId = collectionResponse.data._id;
          await addProductsToCollection(collectionId, selectedModels);
        }
      } else {
        // Adding models to existing collection
        if (!formData.selectedCollection) {
          setError("Please select a collection");
          setLoading(false);
          return;
        }

        if (selectedModels.length === 0) {
          setError("Please select at least one model");
          setLoading(false);
          return;
        }

        await addProductsToCollection(
          formData.selectedCollection,
          selectedModels,
        );
      }

      // Call the success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close the modal
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-dark-300 rounded-xl w-full max-w-3xl max-h-[90vh] overflow-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-800 sticky top-0 bg-dark-300 z-10">
          <h2 className="text-xl font-light text-gray-100">
            {isNewCollection ? "Create New Collection" : "Add to Collection"}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-dark-200 text-gray-400 transition-colors"
          >
            <X size={20} strokeWidth={1.5} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2">
              <AlertCircle size={18} strokeWidth={1.5} />
              <span>{error}</span>
            </div>
          )}

          <div className="space-y-6">
            {/* Collection Details */}
            {isNewCollection ? (
              <>
                <div>
                  <label className="block text-sm font-light text-gray-400 mb-2">
                    Collection Name <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.collectionName}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        collectionName: e.target.value,
                      }))
                    }
                    className="input"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-light text-gray-400 mb-2">
                    Description <span className="text-red-400">*</span>
                  </label>
                  <textarea
                    value={formData.collectionDescription}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        collectionDescription: e.target.value,
                      }))
                    }
                    className="input min-h-[100px]"
                    required
                  />
                </div>
              </>
            ) : (
              <div>
                <label className="block text-sm font-light text-gray-400 mb-2">
                  Select Collection <span className="text-red-400">*</span>
                </label>
                <select
                  value={formData.selectedCollection}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      selectedCollection: e.target.value,
                    }))
                  }
                  className="input"
                  required
                >
                  <option value="">Choose a collection</option>
                  {collections.map((collection) => (
                    <option key={collection.id} value={collection.id}>
                      {collection.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Model Selection Section */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-light text-gray-400">
                  Select Models
                </label>
                <div className="text-xs text-gray-500">
                  {selectedModels.length} selected
                </div>
              </div>

              {/* Search input */}
              <div className="relative mb-4">
                <Search
                  size={18}
                  strokeWidth={1.5}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search models..."
                  className="w-full py-2 pl-10 pr-4 rounded-lg bg-dark-200 border border-gray-700 text-gray-200 placeholder-gray-500"
                />
              </div>

              {/* Models grid */}
              {modelsLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-300"></div>
                </div>
              ) : filteredModels.length === 0 ? (
                <div className="bg-dark-200 p-8 rounded-lg text-center text-gray-400">
                  No models found matching your search.
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3 max-h-[300px] overflow-y-auto p-1">
                  {filteredModels.map((model) => (
                    <div
                      key={model._id}
                      onClick={() => handleModelSelect(model._id)}
                      className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 flex gap-3 items-center
                        ${
                          selectedModels.includes(model._id)
                            ? "border-brand-500 bg-brand-500/10"
                            : "border-gray-700 hover:border-gray-600 bg-dark-200"
                        }`}
                    >
                      <div className="w-12 h-12 bg-dark-300 rounded-md overflow-hidden flex-shrink-0">
                        {model.imageUrl ? (
                          <img
                            src={model.imageUrl}
                            alt={model.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-500">
                            <AlertCircle size={16} strokeWidth={1.5} />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm text-gray-200 truncate">
                          {model.name}
                        </h4>
                        <p className="text-xs text-gray-500 truncate">
                          ${model.price.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-light text-gray-400 mb-2">
                Category
              </label>
              <select
                value={formData.category}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, category: e.target.value }))
                }
                className="input"
              >
                <option value="">Select category</option>
                <option value="furniture">Furniture</option>
                <option value="electronics">Electronics</option>
                <option value="fashion">Fashion</option>
                <option value="home">Home & Decor</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  <span>
                    {isNewCollection ? "Creating..." : "Adding Models..."}
                  </span>
                </>
              ) : (
                <>
                  <Upload size={18} strokeWidth={1.5} />
                  <span>
                    {isNewCollection
                      ? "Create Collection"
                      : "Add to Collection"}
                  </span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CollectionUploadModal;
