const mongoose = require("mongoose");

const planSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      enum: ["free", "basic", "pro", "enterprise"],
    },
    displayName: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    interval: {
      type: String,
      required: true,
      enum: ["month", "year"],
    },
    price: {
      type: Number,
      required: true,
    },
    productLimit: {
      type: Number,
      required: true,
    },
    features: [String],
    stripePriceId: {
      type: String,
      default: null,
    },
    paymentLink: {
      type: String,
      default: null,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true, collection: "plans" }
);

// Compound index to ensure uniqueness of name + interval combination
planSchema.index({ name: 1, interval: 1 }, { unique: true });

const planModel = mongoose.model("Plan", planSchema);
module.exports = planModel;
