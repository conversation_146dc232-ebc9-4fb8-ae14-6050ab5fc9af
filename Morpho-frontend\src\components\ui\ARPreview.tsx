import React, { useState } from 'react';
import { Smartphone, ArrowRight, CheckCircle2 } from 'lucide-react';
import { Button } from './Button';
import { GlassContainer } from './GlassContainer';

interface ARPreviewProps {
  productName?: string;
  previewImage?: string;
  className?: string;
}

export const ARPreview: React.FC<ARPreviewProps> = ({
  productName = "3D Product",
  previewImage = "",
  className = "",
}) => {
  const [showQR, setShowQR] = useState(false);
  const [viewedInAR, setViewedInAR] = useState(false);
  
  const handleButtonClick = () => {
    setShowQR(true);
    
    // Simulate AR view after 5 seconds (for demo purposes)
    setTimeout(() => {
      setViewedInAR(true);
    }, 5000);
  };
  
  return (
    <GlassContainer className={`p-4 sm:p-6 relative overflow-hidden ${className}`}>
      <div className="flex flex-col items-center text-center">
        <div className="text-blue-400 mb-3">
          <Smartphone size={28} />
        </div>
        
        <h3 className="text-white text-xl font-semibold mb-2">
          View in Augmented Reality
        </h3>
        
        <p className="text-gray-300 mb-4">
          Experience {productName} in your space using augmented reality
        </p>
        
        {!showQR ? (
          <Button intent="primary" onClick={handleButtonClick}>
            <span>View in your space</span>
            <ArrowRight size={16} className="ml-2" />
          </Button>
        ) : (
          <div className="w-full">
            <div className="relative mb-4">
              {/* QR code placeholder */}
              <div className="w-48 h-48 mx-auto bg-white p-4 rounded-lg flex items-center justify-center relative">
                {viewedInAR ? (
                  <div className="absolute inset-0 bg-blue-400/90 rounded-lg flex flex-col items-center justify-center text-white">
                    <CheckCircle2 size={40} />
                    <p className="mt-2 font-medium">AR Experience Viewed</p>
                  </div>
                ) : (
                  <>
                    <div className="w-full h-full relative">
                      <div className="absolute top-0 left-0 w-4 h-4 border-t-2 border-l-2 border-black"></div>
                      <div className="absolute top-0 right-0 w-4 h-4 border-t-2 border-r-2 border-black"></div>
                      <div className="absolute bottom-0 left-0 w-4 h-4 border-b-2 border-l-2 border-black"></div>
                      <div className="absolute bottom-0 right-0 w-4 h-4 border-b-2 border-r-2 border-black"></div>
                      <div className="text-black">
                        <div className="text-center">
                          <div className="font-mono">QR CODE</div>
                          <div className="text-xs mt-2">Scan to view in AR</div>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
              
              {!viewedInAR && (
                <div className="mt-2 text-center text-gray-300 text-sm animate-pulse">
                  Scan with your phone camera
                </div>
              )}
            </div>
            
            {viewedInAR ? (
              <div className="text-green-400 text-sm flex items-center justify-center">
                <CheckCircle2 size={16} className="mr-1" />
                <span>Successfully viewed in AR</span>
              </div>
            ) : (
              <ul className="text-sm text-gray-400 text-left mx-auto max-w-xs space-y-2">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">1.</span>
                  <span>Point your camera at the QR code</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">2.</span>
                  <span>Tap the notification on your phone</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-2">3.</span>
                  <span>Place the 3D product in your space</span>
                </li>
              </ul>
            )}
            
            <Button 
              intent="secondary"
              className="mt-4 mx-auto"
              onClick={() => setShowQR(false)}
            >
              {viewedInAR ? "Back to options" : "Cancel"}
            </Button>
          </div>
        )}
      </div>
    </GlassContainer>
  );
};