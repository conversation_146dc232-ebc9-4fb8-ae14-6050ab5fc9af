import React, { useRef } from 'react';
import { motion, useScroll, useTransform, MotionValue } from 'framer-motion';
import { useInView } from '../../hooks/useInView';

interface ScrollRevealProps {
  children: React.ReactNode;
  className?: string;
  animationType?: 'fade' | 'slide' | 'zoom' | 'flip' | 'rotate' | 'perspective';
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  threshold?: number;
  once?: boolean;
}

export const ScrollReveal: React.FC<ScrollRevealProps> = ({
  children,
  className = '',
  animationType = 'fade',
  delay = 0,
  direction = 'up',
  duration = 0.6,
  threshold = 0.1,
  once = true,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { threshold });

  // Define animation variants based on type
  const variants = {
    fade: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 },
    },
    slide: {
      hidden: {
        opacity: 0,
        x: direction === 'left' ? 50 : direction === 'right' ? -50 : 0,
        y: direction === 'up' ? 50 : direction === 'down' ? -50 : 0,
      },
      visible: { opacity: 1, x: 0, y: 0 },
    },
    zoom: {
      hidden: { opacity: 0, scale: 0.8 },
      visible: { opacity: 1, scale: 1 },
    },
    flip: {
      hidden: {
        opacity: 0,
        rotateX: direction === 'up' || direction === 'down' ? 90 : 0,
        rotateY: direction === 'left' || direction === 'right' ? 90 : 0,
      },
      visible: { opacity: 1, rotateX: 0, rotateY: 0 },
    },
    rotate: {
      hidden: { opacity: 0, rotate: 15 },
      visible: { opacity: 1, rotate: 0 },
    },
    perspective: {
      hidden: {
        opacity: 0,
        perspective: 1000,
        rotateX: direction === 'up' ? 30 : direction === 'down' ? -30 : 0,
        rotateY: direction === 'left' ? 30 : direction === 'right' ? -30 : 0,
        z: -150,
      },
      visible: { opacity: 1, rotateX: 0, rotateY: 0, z: 0 },
    },
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={isInView ? 'visible' : once ? 'visible' : 'hidden'}
      variants={variants[animationType]}
      transition={{ duration, delay, ease: [0.22, 1, 0.36, 1] }}
    >
      {children}
    </motion.div>
  );
};

export const useParallax = (value: MotionValue<number>, distance: number) => {
  return useTransform(value, [0, 1], [-distance, distance]);
};

export const ParallaxElement: React.FC<{
  children: React.ReactNode;
  className?: string;
  strength?: number;
}> = ({ children, className = '', strength = 100 }) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });
  
  const y = useParallax(scrollYProgress, strength);
  
  return (
    <motion.div ref={ref} className={className} style={{ y }}>
      {children}
    </motion.div>
  );
};