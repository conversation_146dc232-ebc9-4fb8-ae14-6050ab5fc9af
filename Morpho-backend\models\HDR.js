const mongoose = require("mongoose");

const HDRSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    url: {
      type: String,
      required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    icon: {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    rotationY: {
      type: Number,
    },
    intensity: {
      type: Number,
    },
    isShowroomDefault: {
      type: Boolean,
      default: false,
    },
    category: {
      type: String,
      enum: ["Indoor", "Outdoor", "Studio"],
    },
    library: {
      type: mongoose.Types.ObjectId,
      ref: "AssetLibrary",
    },
    user: {
      type: mongoose.Types.ObjectId,
      ref: "User",
    },
  },
  { timestamps: true, collection: "HDRs" },
);

const HDRModel = mongoose.model("HDR", HDRSchema);
module.exports = HDRModel;
