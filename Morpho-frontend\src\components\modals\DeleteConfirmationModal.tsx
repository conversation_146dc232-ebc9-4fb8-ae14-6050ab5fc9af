import React from "react";
import { X, AlertTriangle } from "lucide-react";

interface DeleteConfirmationModalProps {
  modelName: string;
  onClose: () => void;
  onConfirm: () => void;
  isDeleting: boolean;
  confirmationText?: string;
  confirmButtonText?: string;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  modelName,
  onClose,
  onConfirm,
  isDeleting,
  confirmationText = "Are you sure you want to delete this model? This action cannot be undone.",
  confirmButtonText = "Delete",
}) => {
  return (
    <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-dark-300 rounded-xl w-full max-w-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-light text-gray-100">
            Confirm {confirmButtonText}
          </h3>
          <button
            onClick={onClose}
            className="p-1 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors"
          >
            <X size={20} strokeWidth={1.5} />
          </button>
        </div>

        <div className="p-4 rounded-lg bg-red-500/10 flex items-start gap-3 mb-5">
          <AlertTriangle
            className="w-6 h-6 text-red-400 mt-0.5 flex-shrink-0"
            strokeWidth={1.5}
          />
          <div>
            <p className="text-gray-300 font-medium">{modelName}</p>
            <p className="text-sm text-gray-400 mt-1">{confirmationText}</p>
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            className="btn btn-secondary"
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="btn bg-red-500 hover:bg-red-600 text-white flex items-center gap-2"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                <span>
                  {confirmButtonText === "Delete"
                    ? "Deleting..."
                    : "Removing..."}
                </span>
              </>
            ) : (
              <span>{confirmButtonText}</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
