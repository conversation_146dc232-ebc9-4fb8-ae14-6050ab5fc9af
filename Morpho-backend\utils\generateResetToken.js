const jwt = require("jsonwebtoken");
const user = require("../models/User");
const generateResetToken = (user) => {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
    },
    process.env.RESET_PASSWORD_KEY,
    { expiresIn: "20m" },
  );
};
const saveResetLinkToUser = async (user, token) => {
  user.resetLink = token;
  await user.save();
};

const verifyResetLink = (resetLink) => {
  return new Promise((resolve, reject) => {
    jwt.verify(
      resetLink,
      process.env.RESET_PASSWORD_KEY,
      (err, decodedData) => {
        if (err) {
          reject(new Error("Incorrect Token or expired"));
        } else {
          resolve(decodedData);
        }
      },
    );
  });
};

const generateToken = (user, secretKey, time, role) => {
  return jwt.sign(
    {
      userId: user?.id,
      email: user?.email,
      role: user?.role || role?.role || "",
    },
    secretKey,
    { expiresIn: time },
  );
};

module.exports = {
  generateResetToken,
  saveResetLinkToUser,
  verifyResetLink,
  generateToken,
};
