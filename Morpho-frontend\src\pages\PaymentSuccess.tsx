import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { CheckCircle, Loader2 } from "lucide-react";
import { subscriptionService } from "../services/subscription";
import { toast } from "react-hot-toast";

const PaymentSuccess: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [plan, setPlan] = useState<string | null>(null);

  useEffect(() => {
    // Extract plan from URL query parameters
    const params = new URLSearchParams(location.search);
    const planParam = params.get("plan");
    if (planParam) {
      setPlan(planParam);
    }

    // Check for subscription updates
    const checkSubscription = async () => {
      setLoading(true);
      let checkCount = 0;
      const maxChecks = 10; // Check for up to 50 seconds (10 * 5 seconds)

      const checkInterval = setInterval(async () => {
        checkCount++;

        try {
          const result = await subscriptionService.getUserSubscription();

          if (result.success && result.data) {
            // If subscription is active and matches the plan (if provided)
            if (
              result.data.status === "active" &&
              (!planParam || result.data.plan === planParam)
            ) {
              clearInterval(checkInterval);
              setSuccess(true);
              setLoading(false);
              toast.success("Your subscription is now active!");
            }
          }
        } catch (error) {
          console.error("Error checking subscription status:", error);
        }

        if (checkCount >= maxChecks) {
          clearInterval(checkInterval);
          setLoading(false);
        }
      }, 5000); // Check every 5 seconds

      // Clean up interval on unmount
      return () => clearInterval(checkInterval);
    };

    checkSubscription();

    // Redirect to subscription page after 10 seconds
    const redirectTimeout = setTimeout(() => {
      navigate("/dashboard/subscription");
    }, 10000);

    return () => clearTimeout(redirectTimeout);
  }, [navigate, location.search]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-6rem)]">
      <div className="card rounded-xl p-8 bg-dark-300/90 backdrop-blur-lg max-w-md w-full text-center">
        {loading ? (
          <>
            <div className="flex justify-center mb-4">
              <Loader2 size={48} className="text-brand-300 animate-spin" />
            </div>
            <h1 className="text-2xl font-light text-gray-100 mb-2">
              Processing Your Payment
            </h1>
            <p className="text-gray-400">
              Please wait while we confirm your subscription...
            </p>
          </>
        ) : success ? (
          <>
            <div className="flex justify-center mb-4">
              <CheckCircle size={48} className="text-green-500" />
            </div>
            <h1 className="text-2xl font-light text-gray-100 mb-2">
              Payment Successful!
            </h1>
            <p className="text-gray-400 mb-6">
              {plan
                ? `Your ${
                    plan.charAt(0).toUpperCase() + plan.slice(1)
                  } plan subscription is now active.`
                : "Your subscription is now active."}
            </p>
            <button
              onClick={() => navigate("/dashboard/subscription")}
              className="btn btn-primary w-full"
            >
              View My Subscription
            </button>
          </>
        ) : (
          <>
            <div className="flex justify-center mb-4">
              <CheckCircle size={48} className="text-yellow-500" />
            </div>
            <h1 className="text-2xl font-light text-gray-100 mb-2">
              Payment Received
            </h1>
            <p className="text-gray-400 mb-6">
              Your payment was successful, but we're still setting up your
              subscription. This may take a few minutes.
            </p>
            <button
              onClick={() => navigate("/dashboard/subscription")}
              className="btn btn-primary w-full"
            >
              View My Subscription
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentSuccess;
