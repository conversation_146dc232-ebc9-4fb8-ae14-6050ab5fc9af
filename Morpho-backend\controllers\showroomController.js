const showroomModel = require("../models/showroomModel");
const { uploadToS3 } = require("../config/s3Service");
const {
  getCategories,
  handleCategory,
} = require("../services/showroomService");
const showroomCategoryModel = require("../models/showroomCategoriesModel");
class Controller {
  createShowroom = async (req, res) => {
    const bodyData = req.body;
    let inputs = {
      url: bodyData.url,
      debugUrl: bodyData.debugUrl,
      sku: bodyData.sku,
      position: bodyData.position,
      rotation: bodyData.rotation,
      scaling: bodyData.scaling,
      materialColorChanges: bodyData.materialColorChanges,
      imagesUrl: bodyData.imagesUrl,
      description: bodyData.description,
      fontFamily: bodyData.fontFamily,
      alignment: bodyData.alignment,
      italic: bodyData.italic,
      bold: bodyData.bold,
      uiUxButtonShapeStyle: bodyData.uiUxButtonShapeStyle,
      name: bodyData.name,
      cameraValues: bodyData.cameraValues,
      imagePosition: bodyData.imagePosition,
      iconImageUrl: bodyData.iconImageUrl,
      tags: bodyData.tags,
    };

    if (typeof bodyData.url === "string") {
      inputs.url = bodyData.url;
    } else {
      const data = await uploadToS3(req.files.url[0]);
      const url = data[0]?.Location;
      inputs.url = url;
    }

    if (typeof bodyData.debugUrl === "string") {
      inputs.debugUrl = bodyData.debugUrl;
    } else {
      const data = await uploadToS3(req.files.debugUrl[0]);
      const url = data[0]?.Location;
      inputs.debugUrl = url;
    }

    if (typeof bodyData.iconImageUrl === "string") {
      inputs.iconImageUrl = bodyData.iconImageUrl;
    } else {
      const data = await uploadToS3(req.files.iconImageUrl[0]);
      inputs.iconImageUrl = iconImageUrl;
    }
    //Deploy Comment
    const categories = await handleCategory(req.body.categories, false);
    let newShowroom = new showroomModel(inputs);
    newShowroom.categories = categories;
    await newShowroom.save();
    return res.status(200).json({
      data: newShowroom,
    });
  };

  getCategoriesOfShowroom = async (req, res) => {
    const categories = await getCategories(showroomModel);
    return res.status(200).json({ data: categories });
  };

  getAll = async (req, res) => {
    const allshowrooms = await showroomModel.find();
    return res.status(200).json({
      data: allshowrooms,
    });
  };
  getBySku = async (req, res) => {
    const { sku } = req.params;
    const showroomToBeRetrieved = await showroomModel.findOne({ sku: sku });
    if (!showroomToBeRetrieved) {
      return res.status(404).json({
        message: "Showoom Not Found",
      });
    }
    const url = showroomToBeRetrieved.url.split("/");
    const urlSplited = url[url.length - 1];

    const debugUrl = showroomToBeRetrieved.debugUrl.split("/");
    const debugUrlSplited = debugUrl[debugUrl.length - 1];

    showroomToBeRetrieved.debugUrl = debugUrlSplited;
    showroomToBeRetrieved.url = urlSplited;
    return res.status(200).json({
      data: showroomToBeRetrieved,
    });
  };
  getBySkuEditor = async (req, res, next) => {
    const { sku } = req.params;
    const showroomToBeRetrieved = await showroomModel
      .findOne({ sku: sku })
      .select("url sku name cameraValues");
    if (!showroomToBeRetrieved) {
      return res.status(404).json("Not Found");
    }
    return res.status(200).json({ data: showroomToBeRetrieved });
  };
  updateById = async (req, res, next) => {
    const { id } = req.params;
    const bodyData = req.body;
    const { categories } = req.body;
    let inputs = {
      cameraValues: {
        radius: bodyData.cameraValues.radius,
        lowerRadius: bodyData.cameraValues.lowerRadius,
        upperRadius: bodyData.cameraValues.upperRadius,
      },
    };
    if (typeof bodyData.url === "string") {
      inputs.url = bodyData.url;
    } else if (req.files?.url && req.files.url[0]) {
      const data = await uploadToS3(req.files.url[0]);
      const url = data[0]?.Location;
      inputs.url = url;
    }
    if (typeof bodyData.debugUrl === "string") {
      inputs.debugUrl = bodyData.debugUrl;
    } else if (req.files?.debugUrl && req.files.debugUrl[0]) {
      const data = await uploadToS3(req.files.debugUrl[0]);
      const url = data[0]?.Location;
      inputs.debugUrl = url;
    }
    if (typeof bodyData.iconImageUrl === "string") {
      inputs.iconImageUrl = bodyData.iconImageUrl;
    } else if (req.files?.iconImageUrl && req.files.iconImageUrl[0]) {
      const data = await uploadToS3(req.files.iconImageUrl[0]);
      const iconImageUrl = data[0]?.Location;
      inputs.iconImageUrl = iconImageUrl;
    }
    if (req.body.category) {
      const categoryIds = await handleCategory(categories, false);
      inputs.categories = categoryIds;
    }
    // const radius = req.body.cameraValues.radius;
    // inputs.cameraValues.radius = radius;
    const showroomToBeUpdated = await showroomModel.findOneAndUpdate(
      { _id: id },
      inputs,
      { new: true },
    );

    if (!showroomToBeUpdated) {
      return res.status(404).send({ message: "Showroom not found" });
    }

    return res.status(200).json({ data: showroomToBeUpdated });
  };
  deleteById = async (req, res, next) => {
    const { id } = req.params;
    const showroomToBeDeleted = await showroomModel.findByIdAndDelete(id);
    if (!showroomToBeDeleted) {
      return res.status(404).send("Showroom Not Found");
    }
    return res.status(200).json("Showroom Deleted");
  };
}

const controller = new Controller();
module.exports = controller;
