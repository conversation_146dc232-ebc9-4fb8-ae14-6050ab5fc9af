const router = require("express").Router();
const controller = require("../../controllers/glbController");
const projectController = require("../../controllers/projectController");
const categoryController = require("../../controllers/categoryController");
const assetController = require("../../controllers/assetController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.post(
  "/create-asset-library",
  asyncError<PERSON>and<PERSON>(assetController.createAsset)
);
router.post(
  "/create-project",
  asyncError<PERSON>and<PERSON>(projectController.createProject)
);
router.post(
  "/create-category",
  asyncErrorHandler(categoryController.createCategory)
);
router.post("/create-product", asyncErrorHandler(controller.createGlbModel));
router.get(
  "/get-product-by-sku-and-project",
  asyncError<PERSON>andler(controller.getBySkuAndProject)
);
router.post(
  "/get-all-products-by-project",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getAllGlbModelsByProjectId)
);
router.post(
  "/get-product-by-id",
  asyncErrorHandler(controller.getGlbModelById)
);
router.post(
  "/get-product-by-id-populated",
  asyncErrorHandler(controller.getGlbModelWithAllInformations)
);

module.exports = router;
