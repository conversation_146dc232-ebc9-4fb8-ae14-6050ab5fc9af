/**
 * Custom error class for handling operational errors
 * Operational errors are expected errors that can be handled gracefully
 */
class expressError extends Error {
  /**
   * Create a new expressError
   * @param {String} message - Error message
   * @param {Number} statusCode - HTTP status code
   * @param {String} errorCode - Optional error code for frontend handling
   */
  constructor(message, statusCode, errorCode = null) {
    super(message);
    this.statusCode = statusCode;
    this.status = statusCode >= 400 && statusCode < 500 ? "fail" : "error";
    this.isOperational = true;
    this.errorCode = errorCode;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error codes for common errors
 * These can be used by the frontend to display appropriate messages or take specific actions
 */
expressError.CODES = {
  // Authentication errors
  AUTHENTICATION_REQUIRED: "AUTHENTICATION_REQUIRED",
  INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
  SESSION_EXPIRED: "SESSION_EXPIRED",

  // Authorization errors
  PERMISSION_DENIED: "PERMISSION_DENIED",
  SUBS<PERSON>IPTION_REQUIRED: "SUBSCRIPTION_REQUIRED",

  // Resource errors
  RESOURCE_NOT_FOUND: "RESOURCE_NOT_FOUND",
  RESOURCE_ALREADY_EXISTS: "RESOURCE_ALREADY_EXISTS",

  // Validation errors
  VALIDATION_ERROR: "VALIDATION_ERROR",
  INVALID_DATA_FORMAT: "INVALID_DATA_FORMAT",
  MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD",

  // File errors
  FILE_UPLOAD_ERROR: "FILE_UPLOAD_ERROR",
  FILE_TOO_LARGE: "FILE_TOO_LARGE",
  INVALID_FILE_TYPE: "INVALID_FILE_TYPE",

  // Server errors
  SERVER_ERROR: "SERVER_ERROR",
  DATABASE_ERROR: "DATABASE_ERROR",
  EXTERNAL_API_ERROR: "EXTERNAL_API_ERROR",

  // Rate limiting
  RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED",

  // Product limits
  PRODUCT_LIMIT_EXCEEDED: "PRODUCT_LIMIT_EXCEEDED",
};

module.exports = expressError;
