const router = require("express").Router();
const controller = require("../../controllers/materialLibraryController");
const {
  validateMaterialLibrarySchema,
} = require("../../validation/middleware");
const asyncErrorController = require("../../errors/asyncErrorHandler");
router
  .route("/create-materiallibrary")
  .post(
    validateMaterialLibrarySchema,
    asyncErrorController(controller.createMaterialLibrary)
  );
router.get(
  "/get-all-materialslibrary",
  asyncErrorController(controller.getAllMaterialsLibrary)
);
router.post(
  "/get-materialslibrary-by-project",
  asyncErrorController(controller.getMaterialLibraryByProjectId)
);
router.post(
  "/get-materiallibrary-by-id",
  asyncErrorController(controller.getMaterialLibraryById)
);
router.post(
  "/get-materials-by-project",
  asyncErrorController(controller.getMaterialsByProjectId)
);
router.delete(
  "/delete-materiallibrary",
  asyncErrorController(controller.deleteMaterialLibraryById)
);
router.put(
  "update-materiallibrary-by-id",
  asyncErrorController(controller.updateMaterialLibraryById)
);

module.exports = router;
