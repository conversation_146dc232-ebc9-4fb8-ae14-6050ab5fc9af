/**
 * This is an example of how to use the verifySubscription middleware
 * to protect routes that require an active subscription.
 * 
 * You can apply this pattern to any API routes that should be restricted
 * to users with active subscriptions.
 */

const router = require("express").Router();
const verifyJWT = require("../middleware/verifyJWT");
const verifySubscription = require("../middleware/verifySubscription");

// Example controller function
const exampleController = (req, res) => {
  return res.status(200).json({
    success: true,
    message: "This is a subscription-protected API endpoint",
    data: {
      userId: req.user,
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * Example of a route that requires both authentication and an active subscription
 * 
 * The middleware chain works as follows:
 * 1. verifyJWT - Verifies the user's JWT token and sets req.user
 * 2. verifySubscription - Checks if the user has an active subscription
 * 3. exampleController - Handles the actual request if the user passes both checks
 */
router.get(
  "/subscription-protected",
  verifyJWT,
  verifySubscription,
  exampleController
);

/**
 * To use this middleware with existing routes, simply add verifySubscription
 * to the middleware chain after verifyJWT:
 * 
 * router.get("/some-premium-feature", verifyJWT, verifySubscription, yourController);
 * router.post("/another-premium-endpoint", verifyJWT, verifySubscription, anotherController);
 */

module.exports = router;
