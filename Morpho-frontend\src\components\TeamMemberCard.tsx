import React from 'react';
import { Mail, Phone, MapPin } from 'lucide-react';

interface TeamMemberProps {
  name: string;
  role: string;
  email: string;
  phone: string;
  location: string;
  avatarUrl: string;
}

const TeamMemberCard: React.FC<TeamMemberProps> = ({
  name,
  role,
  email,
  phone,
  location,
  avatarUrl
}) => {
  return (
    <div className="card rounded-xl p-5 card-hover">
      <div className="flex items-start gap-4">
        <img
          src={avatarUrl}
          alt={name}
          className="w-12 h-12 rounded-lg object-cover"
        />
        <div className="flex-1">
          <h3 className="text-gray-100 font-light tracking-wide">{name}</h3>
          <p className="text-sm text-indigo-400 font-light mt-0.5">{role}</p>
          <div className="mt-3 space-y-2">
            <div className="flex items-center text-sm text-gray-400">
              <Mail size={14} strokeWidth={1.5} className="mr-2" />
              <span className="font-light">{email}</span>
            </div>
            <div className="flex items-center text-sm text-gray-400">
              <Phone size={14} strokeWidth={1.5} className="mr-2" />
              <span className="font-light">{phone}</span>
            </div>
            <div className="flex items-center text-sm text-gray-400">
              <MapPin size={14} strokeWidth={1.5} className="mr-2" />
              <span className="font-light">{location}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamMemberCard;