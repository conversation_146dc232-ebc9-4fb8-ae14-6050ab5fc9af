import React from 'react';
import { Model3D } from '../../types/models';

interface InfoTabProps {
  model: Model3D;
  isEditing: boolean;
  onModelUpdate: (model: Model3D) => void;
}

const InfoTab: React.FC<InfoTabProps> = ({ model, isEditing, onModelUpdate }) => {
  return (
    <div className="card rounded-xl p-6">
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-light text-gray-400 mb-2">
            Description
          </label>
          {isEditing ? (
            <textarea
              value={model.description}
              onChange={(e) => onModelUpdate({ ...model, description: e.target.value })}
              className="input min-h-[100px]"
              placeholder="Enter product description"
            />
          ) : (
            <p className="text-gray-300 font-light">{model.description}</p>
          )}
        </div>
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-light text-gray-400 mb-2">
              SKU
            </label>
            {isEditing ? (
              <input
                type="text"
                value={model.sku}
                onChange={(e) => onModelUpdate({ ...model, sku: e.target.value })}
                className="input"
              />
            ) : (
              <p className="text-gray-300 font-light">{model.sku}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-light text-gray-400 mb-2">
              Price
            </label>
            {isEditing ? (
              <input
                type="number"
                value={model.price}
                onChange={(e) => onModelUpdate({ ...model, price: parseFloat(e.target.value) })}
                className="input"
              />
            ) : (
              <p className="text-gray-300 font-light">
                ${model.price.toLocaleString()}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InfoTab;