import React from 'react';
import { ViewerSettings } from '../../types/viewer';
import ModelViewer from '../ModelViewer';
import { useModels } from '../../hooks/useModels';
import { AlertCircle, Loader } from 'lucide-react';

interface ViewerPreviewProps {
  settings: ViewerSettings;
  selectedProductId?: string;
}

const ViewerPreview: React.FC<ViewerPreviewProps> = ({ settings, selectedProductId }) => {
  const { models, loading, error } = useModels();
  const selectedModel = selectedProductId ? models.find(m => m._id === selectedProductId) : null;
  
  if (loading) {
    return (
      <div className="aspect-video w-full bg-dark-200/50 rounded-lg flex items-center justify-center">
        <div className="text-center p-8">
          <Loader size={32} className="text-brand-300 mx-auto mb-4 animate-spin" strokeWidth={1.5} />
          <p className="text-gray-400 text-sm">Loading models...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="aspect-video w-full bg-dark-200/50 rounded-lg flex items-center justify-center">
        <div className="text-center p-8">
          <AlertCircle size={32} className="text-red-400 mx-auto mb-4" strokeWidth={1.5} />
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!selectedModel) {
    return (
      <div className="aspect-video w-full bg-dark-200/50 rounded-lg flex items-center justify-center">
        <div className="text-center p-8">
          <AlertCircle size={32} className="text-gray-400 mx-auto mb-4" strokeWidth={1.5} />
          <p className="text-gray-400 text-sm">
            {models.length === 0 
              ? 'No models available. Upload some models first.'
              : 'Select a product to preview the viewer settings'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="aspect-video w-full relative bg-dark-200/50 rounded-lg overflow-hidden">
      <ModelViewer
        url={selectedModel.url}
        title={selectedModel.name}
        modelId={selectedModel._id}
        settings={settings}
        source="embed"
        className="w-full h-full"
        onError={(error) => console.warn('Model viewer error:', error)}
      />
      <div className="absolute bottom-4 left-4 right-4 bg-dark-300/90 backdrop-blur-sm rounded-lg p-4 border border-gray-800/50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm text-gray-300">{selectedModel.name}</h3>
            <p className="text-xs text-gray-500 mt-1">SKU: {selectedModel.sku}</p>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-2 h-2 rounded-full bg-brand-300 animate-pulse"></span>
            <span className="text-xs text-gray-400">Preview Mode</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewerPreview;