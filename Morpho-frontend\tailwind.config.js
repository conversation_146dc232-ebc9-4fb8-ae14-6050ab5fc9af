/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
    },
    extend: {
      colors: {
        brand: {
          DEFAULT: '#0066cc',
          50: '#e6f0ff',
          100: '#cce0ff',
          200: '#99c2ff',
          300: '#66a3ff',
          400: '#3385ff',
          500: '#0066cc',
          600: '#0052a3',
          700: '#003d7a',
          800: '#002952',
          900: '#001429'
        },
        dark: {
          DEFAULT: '#0F1115',
          50: '#1E2028',
          100: '#1A1C24',
          200: '#16181F',
          300: '#13151B',
          400: '#0F1115',
          500: '#0B0D10',
          600: '#07080B',
          700: '#030405',
          800: '#000000',
          900: '#000000'
        }
      },
      boxShadow: {
        'glow': '0 0 20px rgba(0, 102, 204, 0.15)',
        'glow-strong': '0 0 30px rgba(0, 102, 204, 0.25)',
        'glass': '0 8px 32px 0 rgba(0, 102, 204, 0.1)',
        'glass-strong': '0 8px 32px 0 rgba(0, 102, 204, 0.2)'
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-mesh': 'linear-gradient(to right bottom, rgba(0, 102, 204, 0.03), transparent)'
      }
    },
  },
  plugins: [],
};
