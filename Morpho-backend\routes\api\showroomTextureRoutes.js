const router = require("express").Router();
const controller = require("../../controllers/showroomTextureController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
const { upload } = require("../../middleware/multer-middleware");

router.post(
  "/create",
  upload.fields([
    { name: "url", maxCount: 1 },
    { name: "iconUrl", maxCount: 1 },
  ]),
  asyncError<PERSON>and<PERSON>(controller.create)
);
router.get("/get-all", async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getAll));
router.get(
  "/get-all-categories",
  async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.getAllCategoriesOfTextures)
);
router.get("/get-by-id/:id", async<PERSON><PERSON>r<PERSON><PERSON><PERSON>(controller.getById));
router.delete("/delete-by-id/:id", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.deleteById));
router.put("/update-by-id/:id", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.updateById));

module.exports = router;
