const router = require("express").Router();
const controller = require("../../controllers/subscriptionController");
const verifyJWT = require("../../middleware/verifyJWT");
const express = require("express");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
// Get subscription plans
router.get(
  "/plans",
  verifyJWT,
  asyncErrorHandler(controller.getSubscriptionPlans)
);

// Get Stripe publishable key
router.get(
  "/config",
  verifyJWT,
  asyncErrorHandler(controller.getPublishableKey)
);

// Webhook handler for Stripe events
// Note: This route should not use verifyJWT as <PERSON>e needs to access it directly
router.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  (req, res, next) => {
    // Log the raw body for debugging
    const rawBody = req.body.toString("utf8");

    // Store the raw body on the request object
    req.rawBody = rawBody;

    // Parse the JSON for our controller
    if (rawBody) {
      try {
        req.body = JSON.parse(rawBody);
      } catch (err) {
        console.error("Error parsing webhook body:", err);
        // Don't parse if it fails, let the controller handle it
      }
    }

    next();
  },
  asyncErrorHandler(controller.handleWebhook)
);

// Get current user subscription status
router.get(
  "/status",
  verifyJWT,
  asyncErrorHandler(controller.getUserSubscription)
);

// Simulate webhook event (for testing)
router.post("/simulate-webhook", asyncErrorHandler(controller.simulateWebhook));

// Direct update subscription plan
router.post(
  "/direct-update-plan",
  verifyJWT,
  asyncErrorHandler(controller.directUpdatePlan)
);

// Manual webhook trigger
router.post(
  "/manual-webhook",
  verifyJWT,
  asyncErrorHandler(controller.manualWebhook)
);

// Update subscription (for testing)
router.post(
  "/update",
  verifyJWT,
  asyncErrorHandler(controller.updateSubscription)
);

// Cancel subscription
router.post(
  "/cancel",
  verifyJWT,
  asyncErrorHandler(controller.cancelSubscription)
);

module.exports = router;
