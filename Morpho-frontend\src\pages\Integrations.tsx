import React, { useState, useEffect } from "react";
import {
  Store,
  ShoppingBag,
  Link2,
  AlertCircle,
  CheckCircle2,
  ShoppingBasket,
  LayoutGrid,
  ShoppingCart,
  Globe,
  RefreshCw,
} from "lucide-react";
import ProductImportModal from "../components/ProductImportModal";
import type { ProductImportResult } from "../types/ecommerce";
import { ecommerceService } from "../services/ecommerce";
import { toast } from "react-hot-toast";

interface Platform {
  id: string;
  name: string;
  icon: typeof Store;
  description: string;
  status: "connected" | "disconnected";
  lastSync?: string;
  products?: number;
  credentials?: Record<string, string>; // Store credentials for resyncing
}

const Integrations = () => {
  const [platforms, setPlatforms] = useState<Platform[]>([
    {
      id: "shopify",
      name: "Shopify",
      icon: Store,
      description: "Integrate your Shopify store products with 3D models",
      status: "disconnected",
    },
    {
      id: "woocommerce",
      name: "WooCommerce",
      icon: ShoppingBag,
      description: "Connect your WooCommerce products with 3D visualization",
      status: "disconnected",
    },
    {
      id: "magento",
      name: "Magent<PERSON>",
      icon: ShoppingBasket,
      description: "Import products from your Magento 2 store",
      status: "disconnected",
    },
    {
      id: "bigcommerce",
      name: "BigCommerce",
      icon: LayoutGrid,
      description: "Sync your BigCommerce product catalog",
      status: "disconnected",
    },
    {
      id: "prestashop",
      name: "PrestaShop",
      icon: ShoppingCart,
      description: "Connect your PrestaShop store products",
      status: "disconnected",
    },
    {
      id: "webflow",
      name: "Webflow",
      icon: Globe,
      description: "Import products from your Webflow collections",
      status: "disconnected",
    },
  ]);

  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(
    null
  );
  const [isResyncing, setIsResyncing] = useState<string | null>(null);

  // Load connections from localStorage on component mount
  useEffect(() => {
    const savedConnections = localStorage.getItem("platform_connections");
    if (savedConnections) {
      try {
        const parsed = JSON.parse(savedConnections);
        setPlatforms((prev) =>
          prev.map((platform) => {
            const savedPlatform = parsed.find((p: any) => p.id === platform.id);
            return savedPlatform ? { ...platform, ...savedPlatform } : platform;
          })
        );
      } catch (error) {
        console.error("Error loading saved connections:", error);
      }
    }
  }, []);

  // Save connections to localStorage when they change
  useEffect(() => {
    const connectionsToSave = platforms
      .filter((p) => p.status === "connected")
      .map(({ id, status, lastSync, products, credentials }) => ({
        id,
        status,
        lastSync,
        products,
        credentials,
      }));

    localStorage.setItem(
      "platform_connections",
      JSON.stringify(connectionsToSave)
    );
  }, [platforms]);

  const handleImportComplete = (
    result: ProductImportResult,
    credentials: Record<string, string>
  ) => {
    if (selectedPlatform) {
      setPlatforms((prevPlatforms) => {
        return prevPlatforms.map((platform) => {
          if (platform.id === selectedPlatform.id) {
            return {
              ...platform,
              status: "connected",
              lastSync: new Date().toISOString(),
              products: result.total,
              credentials: credentials, // Store credentials for future resync
            };
          }
          return platform;
        });
      });
    }
    setShowImportModal(false);
    setSelectedPlatform(null);
  };

  const handleResync = async (platformId: string) => {
    const platform = platforms.find((p) => p.id === platformId);
    if (!platform || !platform.credentials || platform.status !== "connected") {
      toast.error("Cannot resync. Platform not properly connected.");
      return;
    }

    try {
      setIsResyncing(platformId);

      // Only implemented for Shopify for now
      if (platformId === "shopify") {
        const result = await ecommerceService.resyncShopifyProducts(
          platform.credentials
        );

        if (result.success) {
          setPlatforms((prevPlatforms) => {
            return prevPlatforms.map((p) => {
              if (p.id === platformId) {
                return {
                  ...p,
                  lastSync: new Date().toISOString(),
                  products: result.total,
                };
              }
              return p;
            });
          });
          toast.success(
            `Successfully resynced ${result.imported} products from ${platform.name}`
          );
        } else {
          toast.error(result.error || "Failed to resync products");
        }
      } else {
        toast.error("Resync not implemented for this platform yet");
      }
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "An error occurred during resync"
      );
    } finally {
      setIsResyncing(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light tracking-wide text-gray-100">
            Integrations
          </h1>
          <p className="text-gray-400 mt-2">
            Connect your e-commerce platforms to import and manage products
          </p>
        </div>
      </div>

      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {platforms.map((platform) => {
          const Icon = platform.icon;
          const isConnected = platform.status === "connected";
          const isCurrentlyResyncing = isResyncing === platform.id;

          return (
            <div
              key={platform.id}
              className={`card rounded-xl p-6 space-y-4 text-left hover:border-brand-300/30 transition-all duration-300 w-full transform ${
                isConnected ? "border-green-500/30 shadow-glow-success" : ""
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-lg bg-brand-500/10 flex items-center justify-center">
                    <Icon
                      className="w-6 h-6 text-brand-300"
                      strokeWidth={1.5}
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-light text-gray-100">
                      {platform.name}
                    </h3>
                    <p className="text-sm text-gray-400 mt-1">
                      {platform.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      isConnected
                        ? "bg-green-500/10 text-green-400"
                        : "bg-gray-500/10 text-gray-400"
                    }`}
                  >
                    {isConnected ? "Connected" : "Not Connected"}
                  </span>
                </div>
              </div>

              {isConnected && platform.lastSync && (
                <div className="pt-4 border-t border-gray-800">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-400">Last Synced</p>
                      <p className="text-sm text-gray-300 mt-1">
                        {new Date(platform.lastSync).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Products</p>
                      <p className="text-sm text-gray-300 mt-1">
                        {platform.products || 0} items
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="pt-4 flex justify-end gap-3">
                {isConnected && (
                  <button
                    onClick={() => handleResync(platform.id)}
                    disabled={isCurrentlyResyncing}
                    className={`btn btn-secondary flex items-center gap-2 ${
                      isCurrentlyResyncing
                        ? "opacity-75 cursor-not-allowed"
                        : ""
                    }`}
                  >
                    <RefreshCw
                      size={16}
                      strokeWidth={1.5}
                      className={isCurrentlyResyncing ? "animate-spin" : ""}
                    />
                    <span>
                      {isCurrentlyResyncing ? "Resyncing..." : "Resync"}
                    </span>
                  </button>
                )}
                <button
                  onClick={() => {
                    setSelectedPlatform(platform);
                    setShowImportModal(true);
                  }}
                  className={`btn ${
                    isConnected ? "btn-secondary" : "btn-primary"
                  } flex items-center gap-2`}
                >
                  <Link2 size={16} strokeWidth={1.5} />
                  <span>{isConnected ? "Reconnect" : "Connect"}</span>
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {showImportModal && (
        <ProductImportModal
          onClose={() => {
            setShowImportModal(false);
            setSelectedPlatform(null);
          }}
          selectedPlatform={selectedPlatform?.id}
          onImportComplete={(result, credentials) =>
            handleImportComplete(result, credentials)
          }
        />
      )}
    </div>
  );
};

export default Integrations;
