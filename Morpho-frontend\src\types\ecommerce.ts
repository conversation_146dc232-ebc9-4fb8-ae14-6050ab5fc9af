export interface EcommerceProduct {
  id: string;
  platform: string;
  name: string;
  description: string;
  sku: string;
  price: number;
  currency: string;
  images: string[];
  categories: string[];
  variants: {
    id: string;
    sku: string;
    price: number;
    attributes: Record<string, string>;
  }[];
  metadata: {
    platform_id: string;
    store_id: string;
    [key: string]: any;
  };
}

export interface ProductImportResult {
  success: boolean;
  error?: string;
  errorCode?: string;
  validationErrors?: Record<string, string>;
  products: EcommerceProduct[];
  total: number;
  imported: number;
  failedProducts?: Array<{
    id: string;
    reason: string;
  }>;
}

export interface PlatformCredentials {
  shopify?: {
    storeUrl: string;
    accessToken: string;
  };
  woocommerce?: {
    siteUrl: string;
    consumerKey: string;
    consumerSecret: string;
  };
  magento?: {
    baseUrl: string;
    accessToken: string;
  };
  bigcommerce?: {
    storeHash: string;
    accessToken: string;
    clientId: string;
  };
  prestashop?: {
    shopUrl: string;
    webserviceKey: string;
  };
  webflow?: {
    siteId: string;
    accessToken: string;
    collectionId: string;
  };
  [key: string]: any;
}