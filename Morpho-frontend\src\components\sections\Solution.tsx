import React, { useRef, useState } from 'react';
import { GlassContainer } from '../ui/GlassContainer';
import { useInView } from '../../hooks/useInView';
import { H2, H3, Lead, Body, SectionIntro } from '../ui/Typography';
import { CheckCircle, ArrowRight } from 'lucide-react';

// Enhanced feature data with icons and additional metadata
const features = [
  {
    id: 'viewer',
    title: 'Shopify 3D Product Viewer App',
    description: 'Not a general 3D model viewer forced to work with e-commerce',
    icon: <CheckCircle size={22} className="text-blue-400" />,
    gradient: 'from-blue-500/20 to-blue-400/20'
  },
  {
    id: 'integration',
    title: 'Native Shopify 3D Integration',
    description: 'Installs in seconds with zero coding required',
    icon: <CheckCircle size={22} className="text-blue-400" />,
    gradient: 'from-purple-500/20 to-blue-500/20'
  },
  {
    id: 'creation',
    title: 'Multiple 3D Model Creation Methods',
    description: 'Scan with iOS or upload existing 3D product models',
    icon: <CheckCircle size={22} className="text-blue-400" />,
    gradient: 'from-teal-500/20 to-blue-500/20'
  },
  {
    id: 'management',
    title: 'E-commerce 3D Product Management',
    description: 'Designed around your Shopify product catalog',
    icon: <CheckCircle size={22} className="text-blue-400" />,
    gradient: 'from-blue-500/20 to-purple-500/20'
  },
  {
    id: 'rendering',
    title: 'Web-Optimized 3D Rendering',
    description: 'Models load in under 2 seconds without affecting store performance',
    icon: <CheckCircle size={22} className="text-blue-400" />,
    gradient: 'from-blue-400/20 to-teal-500/20'
  }
];

export const Solution: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);

  return (
    <section ref={sectionRef} id="solution" className="py-24 relative">
      {/* Enhanced background elements with multiple gradients */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2/3 h-2/3 bg-[#0066CC]/10 rounded-full filter blur-[100px] opacity-70"></div>
        <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-[#9747FF]/10 rounded-full filter blur-[80px] opacity-50"></div>
        <div className="absolute top-1/4 left-1/4 w-1/4 h-1/4 bg-[#26D9C2]/10 rounded-full filter blur-[70px] opacity-40"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <SectionIntro
          className={`transition-all duration-700 ${
            isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          subtitle="Morpho is the only complete 3D model viewer and product visualization platform designed specifically for Shopify merchants who want to increase conversion rates without learning complex 3D modeling websites."
        >
          Introducing Morpho: The Complete 3D Product Viewer for Shopify Online Stores
        </SectionIntro>

        <div className={`grid md:grid-cols-2 gap-12 items-center transition-all duration-700 delay-300 ${
          isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="md:col-span-1">
            {/* Enhanced 3D dashboard container with perspective effects */}
            <div className="relative group perspective-[1200px]">
              {/* Multiple layered backgrounds for enhanced depth */}
              <div className="absolute -inset-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-lg opacity-70 group-hover:opacity-100 transition-all duration-700"></div>
              
              {/* 3D Dashboard Container with increased height */}
              <div className="relative w-full h-[500px] md:h-[550px] bg-[#0B0E17] rounded-xl overflow-hidden
               transform-gpu transition-all duration-500 border border-blue-500/30 
               shadow-[0_20px_50px_rgba(8,_112,_184,_0.4)] group-hover:shadow-[0_20px_80px_rgba(8,_112,_184,_0.6)]">
                {/* Top navigation bar */}
                <div className="h-14 flex items-center justify-between px-6 border-b border-[#1A1F2E]/70 bg-[#0D1117]">
                  <div className="flex items-center">
                    {/* Logo */}
                    <div className="w-8 h-8 rounded-md bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mr-3 shadow-md">
                      <svg className="w-5 h-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5" />
                      </svg>
                    </div>
                    <div className="text-xl font-semibold text-white">morpho</div>
                  </div>
                  <div className="text-xl font-semibold text-white">Dashboard</div>
                  <div className="flex items-center space-x-2">
                    <div className="px-3 py-1 bg-[#111827]/80 rounded-md text-gray-400 text-sm">Week</div>
                    <div className="px-3 py-1 bg-[#1D4ED8] rounded-md text-white text-sm shadow-[0_0_10px_rgba(29,78,216,0.5)]">Month</div>
                    <div className="px-3 py-1 bg-[#111827]/80 rounded-md text-gray-400 text-sm">Year</div>
                  </div>
                </div>
                
                {/* Main content with perspective and floating 3D sections */}
                <div className="p-6 perspective-[1000px] h-[calc(100%-56px)] overflow-y-auto">
                  <div className="text-gray-400 text-sm mb-5">Overview of your 3D product management</div>
                  
                  {/* Floating stats cards section with 3D effects */}
                  <div className="grid grid-cols-4 gap-5 mb-8">
                    {/* 3D Models card with floating effect */}
                    <div className="bg-[#111827] rounded-xl p-4 border border-[#1A1F2E]/70 shadow-lg transform-gpu transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:rotate-y-1 hover:shadow-[0_10px_25px_rgba(0,102,204,0.3)]">
                      <div className="flex items-start justify-between">
                        <div className="w-10 h-10 rounded-lg bg-[#1A237E]/30 flex items-center justify-center shadow-[0_0_15px_rgba(26,35,126,0.2)]">
                          <svg viewBox="0 0 24 24" className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5" />
                          </svg>
                        </div>
                        <div className="text-xs text-green-400 bg-green-400/10 px-2 py-0.5 rounded-full">+12%</div>
                      </div>
                      <div className="mt-4">
                        <div className="text-gray-300 text-sm">3D Models</div>
                        <div className="text-white text-2xl font-semibold">122</div>
                      </div>
                    </div>
                    
                    {/* Active Requests card with floating effect */}
                    <div className="bg-[#111827] rounded-xl p-4 border border-[#1A1F2E]/70 shadow-lg transform-gpu transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:rotate-y-1 hover:shadow-[0_10px_25px_rgba(151,71,255,0.3)]">
                      <div className="flex items-start justify-between">
                        <div className="w-10 h-10 rounded-lg bg-[#6A1B9A]/30 flex items-center justify-center shadow-[0_0_15px_rgba(106,27,154,0.2)]">
                          <svg viewBox="0 0 24 24" className="w-6 h-6 text-purple-400" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                        </div>
                        <div className="text-xs text-green-400 bg-green-400/10 px-2 py-0.5 rounded-full">+5%</div>
                      </div>
                      <div className="mt-4">
                        <div className="text-gray-300 text-sm">Active Requests</div>
                        <div className="text-white text-2xl font-semibold">0</div>
                      </div>
                    </div>
                    
                    {/* Total Views card with floating effect */}
                    <div className="bg-[#111827] rounded-xl p-4 border border-[#1A1F2E]/70 shadow-lg transform-gpu transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:rotate-y-1 hover:shadow-[0_10px_25px_rgba(38,217,194,0.3)]">
                      <div className="flex items-start justify-between">
                        <div className="w-10 h-10 rounded-lg bg-[#00695C]/30 flex items-center justify-center shadow-[0_0_15px_rgba(0,105,92,0.2)]">
                          <svg viewBox="0 0 24 24" className="w-6 h-6 text-teal-400" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        </div>
                        <div className="text-xs text-green-400 bg-green-400/10 px-2 py-0.5 rounded-full">+23%</div>
                      </div>
                      <div className="mt-4">
                        <div className="text-gray-300 text-sm">Total Views</div>
                        <div className="text-white text-2xl font-semibold">12,453</div>
                      </div>
                    </div>
                    
                    {/* Conversion Rate card with floating effect */}
                    <div className="bg-[#111827] rounded-xl p-4 border border-[#1A1F2E]/70 shadow-lg transform-gpu transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:rotate-y-1 hover:shadow-[0_10px_25px_rgba(255,107,0,0.3)]">
                      <div className="flex items-start justify-between">
                        <div className="w-10 h-10 rounded-lg bg-[#E65100]/30 flex items-center justify-center shadow-[0_0_15px_rgba(230,81,0,0.2)]">
                          <svg viewBox="0 0 24 24" className="w-6 h-6 text-orange-400" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                          </svg>
                        </div>
                        <div className="text-xs text-green-400 bg-green-400/10 px-2 py-0.5 rounded-full">+0.8%</div>
                      </div>
                      <div className="mt-4">
                        <div className="text-gray-300 text-sm">Conversion Rate</div>
                        <div className="text-white text-2xl font-semibold">3.2%</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Chart section with 3D floating effect */}
                  <div className="bg-[#111827] rounded-xl p-5 border border-[#1A1F2E]/70 mb-6 h-[180px] shadow-lg transform-gpu transition-all duration-300 hover:scale-[1.01] hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(0,102,204,0.2)]">
                    <div className="flex justify-between items-center mb-4">
                      <div className="text-white text-sm font-medium">Performance Overview</div>
                      <div className="flex space-x-4">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
                          <span className="text-xs text-gray-400">Views</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-purple-400 mr-2"></div>
                          <span className="text-xs text-gray-400">Conversions</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Enhanced 3D chart visualization */}
                    <div className="h-[110px] flex items-end">
                      {/* Chart grid */}
                      <div className="absolute inset-x-5 bottom-5 h-[100px] border-b border-l border-gray-700/50">
                        {/* Horizontal grid lines */}
                        <div className="absolute left-0 bottom-1/4 w-full h-px bg-gray-700/30"></div>
                        <div className="absolute left-0 bottom-1/2 w-full h-px bg-gray-700/30"></div>
                        <div className="absolute left-0 bottom-3/4 w-full h-px bg-gray-700/30"></div>
                      </div>
                      
                      {/* Chart bars with 3D effect */}
                      <div className="relative w-full h-[100px] flex items-end justify-between px-10">
                        {[35, 65, 40, 70, 55, 40, 60].map((height, i) => (
                          <div key={i} className="relative mx-1 flex flex-col items-center group">
                            {/* Bar for Views - with 3D depth effect */}
                            <div className="relative w-6">
                              {/* Side of the bar for 3D effect */}
                              <div className="absolute bottom-0 right-0 w-1 h-full bg-blue-500/30 skew-y-[45deg] origin-bottom-right" style={{ height: `${height}%` }}></div>
                              {/* Top of the bar for 3D effect */}
                              <div className="absolute bottom-full w-full h-1 bg-blue-500/40 skew-x-[45deg] origin-bottom-left" style={{ bottom: `${height}%` }}></div>
                              {/* Front face of the bar */}
                              <div className="absolute bottom-0 left-0 w-5 bg-gradient-to-t from-blue-600/70 to-blue-400/70" style={{ height: `${height}%` }}></div>
                            </div>
                            <div className="text-xs text-gray-500 mt-2">{['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][i]}</div>
                            
                            {/* Value tooltip on hover */}
                            <div className="absolute bottom-full mb-2 text-xs bg-blue-500 px-2 py-1 rounded text-white opacity-0 transform-gpu scale-0 group-hover:opacity-100 group-hover:scale-100 transition-all duration-200">
                              {Math.round(height * 150)}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Recent Activity Section */}
                  <div className="grid grid-cols-3 gap-5 mb-6">
                    <div className="col-span-2 bg-[#111827] rounded-xl p-5 border border-[#1A1F2E]/70 shadow-lg transform-gpu transition-all duration-300 hover:scale-[1.01] hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(0,102,204,0.2)]">
                      <h3 className="text-white text-sm font-medium mb-4">Recent Activity</h3>
                      <div className="space-y-4">
                        {/* Activity item 1 */}
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center shadow-[0_0_10px_rgba(0,102,204,0.2)]">
                            <svg viewBox="0 0 24 24" className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-white text-sm font-medium">Modern Chair Collection</p>
                            <p className="text-gray-400 text-xs">New 3D model uploaded</p>
                            <p className="text-gray-600 text-xs mt-1">13:22:01</p>
                          </div>
                        </div>
                        
                        {/* Activity item 2 */}
                        <div className="flex items-start space-x-3">
                          <div className="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center shadow-[0_0_10px_rgba(151,71,255,0.2)]">
                            <svg viewBox="0 0 24 24" className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-white text-sm font-medium">Dining Table Set</p>
                            <p className="text-gray-400 text-xs">Modeling request approved</p>
                            <p className="text-gray-600 text-xs mt-1">10:22:01</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Quick Stats */}
                    <div className="bg-[#111827] rounded-xl p-5 border border-[#1A1F2E]/70 shadow-lg flex flex-col justify-between transform-gpu transition-all duration-300 hover:scale-[1.01] hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(0,102,204,0.2)]">
                      <h3 className="text-white text-sm font-medium mb-4">Quick Stats</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-400">Active models</span>
                          <span className="text-sm text-blue-400 font-medium">122/200</span>
                        </div>
                        <div className="h-1.5 w-full bg-[#1E293B] rounded-full overflow-hidden">
                          <div className="h-full bg-blue-500 rounded-full" style={{width: '61%'}}></div>
                        </div>
                        
                        <div className="flex justify-between items-center mt-4">
                          <span className="text-xs text-gray-400">Storage used</span>
                          <span className="text-sm text-teal-400 font-medium">2.1/5 GB</span>
                        </div>
                        <div className="h-1.5 w-full bg-[#1E293B] rounded-full overflow-hidden">
                          <div className="h-full bg-teal-500 rounded-full" style={{width: '42%'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Action buttons with enhanced 3D effect */}
                  <div className="grid grid-cols-3 gap-5">
                    {/* Upload action */}
                    <div className="bg-[#111827] rounded-xl border border-[#1A1F2E]/70 shadow-lg overflow-hidden transform-gpu transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(0,102,204,0.25)]">
                      <div className="px-5 py-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center shadow-[0_0_15px_rgba(0,102,204,0.2)]">
                            <svg viewBox="0 0 24 24" className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                          </div>
                          <div>
                            <div className="text-white text-sm font-medium">Upload New Model</div>
                            <div className="text-gray-400 text-xs">Add to your library</div>
                          </div>
                        </div>
                      </div>
                      {/* Bottom accent bar for depth */}
                      <div className="h-1 w-full bg-gradient-to-r from-blue-400 to-blue-600"></div>
                    </div>
                    
                    {/* Request action */}
                    <div className="bg-[#111827] rounded-xl border border-[#1A1F2E]/70 shadow-lg overflow-hidden transform-gpu transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(151,71,255,0.25)]">
                      <div className="px-5 py-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-lg bg-purple-500/20 flex items-center justify-center shadow-[0_0_15px_rgba(151,71,255,0.2)]">
                            <svg viewBox="0 0 24 24" className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                          </div>
                          <div>
                            <div className="text-white text-sm font-medium">Create Request</div>
                            <div className="text-gray-400 text-xs">Submit modeling request</div>
                          </div>
                        </div>
                      </div>
                      {/* Bottom accent bar for depth */}
                      <div className="h-1 w-full bg-gradient-to-r from-purple-400 to-purple-600"></div>
                    </div>
                    
                    {/* Analytics action */}
                    <div className="bg-[#111827] rounded-xl border border-[#1A1F2E]/70 shadow-lg overflow-hidden transform-gpu transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1 hover:shadow-[0_10px_25px_rgba(38,217,194,0.25)]">
                      <div className="px-5 py-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-lg bg-teal-500/20 flex items-center justify-center shadow-[0_0_15px_rgba(38,217,194,0.2)]">
                            <svg viewBox="0 0 24 24" className="w-5 h-5 text-teal-400" fill="none" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                          </div>
                          <div>
                            <div className="text-white text-sm font-medium">View Analytics</div>
                            <div className="text-gray-400 text-xs">Check model performance</div>
                          </div>
                        </div>
                      </div>
                      {/* Bottom accent bar for depth */}
                      <div className="h-1 w-full bg-gradient-to-r from-teal-400 to-teal-600"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="space-y-8">
            <div>
              <H3 className="mb-6 relative">
                <span className="relative z-10">What Makes Morpho Different</span>
                <span className="block h-1 w-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2"></span>
              </H3>
              
              {/* Enhanced feature list with interactions and visual styling */}
              <div className="space-y-5">
                {features.map((feature, index) => (
                  <div 
                    key={feature.id}
                    className={`relative transition-all duration-500 ${
                      hoveredFeature === feature.id ? 'scale-[1.03]' : ''
                    }`}
                    onMouseEnter={() => setHoveredFeature(feature.id)}
                    onMouseLeave={() => setHoveredFeature(null)}
                  >
                    {/* Backdrop gradient that appears on hover */}
                    <div 
                      className={`absolute -inset-3 bg-gradient-to-r ${feature.gradient} rounded-lg opacity-0 transition-opacity duration-300 ${
                        hoveredFeature === feature.id ? 'opacity-30' : ''
                      }`}
                    ></div>
                    
                    {/* Feature item with enhanced styling */}
                    <div className="relative bg-[#12121A]/50 backdrop-blur-sm rounded-lg border border-blue-500/20 p-4 transition-all duration-300 hover:border-blue-500/50">
                      <div className="flex items-start space-x-3">
                        {/* Animated icon container */}
                        <div className={`mt-0.5 flex-shrink-0 flex items-center justify-center w-6 h-6 transition-transform duration-300 ${
                          hoveredFeature === feature.id ? 'scale-125' : ''
                        }`}>
                          {feature.icon}
                        </div>
                        
                        <div className="flex-1">
                          {/* Feature title with animated underline */}
                          <h4 className="text-white font-medium mb-1 relative inline-block">
                            {feature.title}
                            {hoveredFeature === feature.id && (
                              <span className="absolute bottom-0 left-0 h-0.5 bg-blue-400 w-full animate-pulse"></span>
                            )}
                          </h4>
                          
                          {/* Feature description with enhanced typography */}
                          <p className={`text-gray-300 transition-all duration-300 ${
                            hoveredFeature === feature.id ? 'text-blue-200' : ''
                          }`}>
                            {feature.description}
                          </p>
                        </div>
                        
                        {/* Arrow indicator that appears on hover */}
                        <div className={`flex-shrink-0 transition-opacity duration-300 ${
                          hoveredFeature === feature.id ? 'opacity-100' : 'opacity-0'
                        }`}>
                          <ArrowRight size={16} className="text-blue-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* CTA button with enhanced styling */}
            <div className="mt-8 pt-4 border-t border-white/10">
              <a 
                href="#features" 
                className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-md shadow-lg hover:shadow-blue-500/30 transition-all duration-300 group"
              >
                <span>Explore All Features</span>
                <ArrowRight size={16} className="ml-2 transform group-hover:translate-x-1 transition-transform duration-300" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};