import React from 'react';
import { useModels } from '../../hooks/useModels';
import { Search } from 'lucide-react';

interface CollectionSelectorProps {
  selectedCollection: string | null;
  selectedProducts: string[];
  previewProductId: string | null;
  onCollectionChange: (collectionId: string | null) => void;
  onProductsChange: (productIds: string[]) => void;
  onPreviewChange: (productId: string | null) => void;
}

const CollectionSelector: React.FC<CollectionSelectorProps> = ({
  selectedCollection,
  selectedProducts,
  previewProductId,
  onCollectionChange,
  onProductsChange,
  onPreviewChange
}) => {
  const { models, loading } = useModels();
  const collections = [
    { id: '1', name: 'All Products' },
    { id: '2', name: 'Featured Products' },
    { id: '3', name: 'New Arrivals' }
  ];

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm text-gray-300 mb-2">Collection</label>
        <select
          value={selectedCollection || ''}
          onChange={(e) => onCollectionChange(e.target.value || null)}
          className="input"
        >
          <option value="">Select a collection</option>
          {collections.map(collection => (
            <option key={collection.id} value={collection.id}>
              {collection.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm text-gray-300 mb-2">Products</label>
        <div className="relative">
          <Search size={18} strokeWidth={1.5} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search products..."
            className="input pl-10"
          />
        </div>
        <div className="mt-2 space-y-2 max-h-48 overflow-y-auto">
          {loading && (
            <div className="text-center py-4">
              <div className="w-6 h-6 border-2 border-brand-300/20 border-t-brand-300 rounded-full animate-spin mx-auto"></div>
            </div>
          )}
          {!loading && models.length === 0 && (
            <div className="text-center py-4">
              <p className="text-gray-400 text-sm">No models available</p>
            </div>
          )}
          {models.map(model => (
            <label
              key={model._id}
              className={`flex items-center gap-2 p-2 rounded-lg hover:bg-dark-200/50 cursor-pointer ${
                previewProductId === model._id ? 'bg-brand-500/10' : ''
              }`}
            >
              <div className="flex-1 flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={selectedProducts.includes(model._id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      onProductsChange([...selectedProducts, model._id]);
                      // Auto-select for preview if no preview is selected
                      if (!previewProductId) {
                        onPreviewChange(model._id);
                      }
                    } else {
                      onProductsChange(selectedProducts.filter(id => id !== model._id));
                      // Clear preview if this was the previewed model
                      if (previewProductId === model._id) {
                        onPreviewChange(null);
                      }
                    }
                  }}
                  className="form-checkbox"
                />
                <div>
                  <span className="text-sm text-gray-300">{model.name}</span>
                  <span className="text-xs text-gray-500 ml-2">SKU: {model.sku}</span>
                </div>
              </div>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  onPreviewChange(previewProductId === model._id ? null : model._id);
                }}
                className={`px-2 py-1 text-xs rounded-lg transition-colors ${
                  previewProductId === model._id
                    ? 'bg-brand-500/20 text-brand-300'
                    : 'bg-dark-200/50 text-gray-400 hover:text-gray-300'
                }`}
              >
                {previewProductId === model._id ? 'Previewing' : 'Preview'}
              </button>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CollectionSelector;