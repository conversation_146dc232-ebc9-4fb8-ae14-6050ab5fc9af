const mongoose = require("mongoose");

const categorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    production: {
      type: mongoose.Types.ObjectId,
      ref: "Production",
    },
    hasCollection: {
      type: Boolean,
      default: false,
    },
    imageUrl: {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    price: String,
  },
  { timestamps: true, collection: "categories" }
);

const categoryModel = mongoose.model("Category", categorySchema);
module.exports = categoryModel;
