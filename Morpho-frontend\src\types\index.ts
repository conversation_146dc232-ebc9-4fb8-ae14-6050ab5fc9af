import React from 'react';
import { Box, Library, Settings, ShoppingCart, Users } from 'lucide-react';
import { useLocation, Link, Outlet } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

interface NavItem {
  name: string;
  href: string;
  icon: React.FC<any>;
}

const Layout = () => {
  const location = useLocation();
  const { logout } = useAuth();
  
  const isLibraryRoute = location.pathname.startsWith('/library');
  
  const navigation: NavItem[] = [
    { name: 'Dashboard', href: '/', icon: Box },
    { name: '3D Library', href: '/library', icon: Library },
    { name: 'Modeling Requests', href: '/requests', icon: ShoppingCart },
    { name: 'Team', href: '/team', icon: Users },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-dark-400 overflow-hidden">
      <div className="flex h-screen">
        {/* Sidebar */}
        <div className="hidden md:flex md:flex-shrink-0 relative">
          <div className="flex flex-col w-72">
            <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-dark-300/90 backdrop-blur-lg border-r border-gray-800/50">
              <div className="flex items-center flex-shrink-0 px-4">
                <Box className="w-8 h-8 text-brand-300" strokeWidth={1.5} />
                <span className="ml-2 text-xl font-light tracking-wider bg-gradient-to-r from-brand-300 to-brand-100 text-transparent bg-clip-text">morpho</span>
              </div>
              <div className="mt-8 flex flex-col h-full">
                <nav className="flex-1 space-y-1">
                  {navigation.map((item) => {
                    const Icon = item.icon;
                    const isActive = item.href === '/' 
                      ? location.pathname === item.href
                      : location.pathname.startsWith(item.href);
                    
                    return (
                      <Link
                        key={item.name}
                        to={item.href}
                        className={`${
                          isActive
                            ? 'nav-link-active'
                            : 'text-gray-400 nav-link'
                        } group flex items-center px-4 py-3 text-sm font-light tracking-wide`}
                      >
                        <Icon
                          className={`${
                            isActive ? 'text-brand-300' : 'text-gray-500 group-hover:text-gray-400'
                          } mr-3 h-5 w-5 flex-shrink-0`}
                          strokeWidth={1.5}
                        />
                        {item.name}
                      </Link>
                    );
                  })}
                </nav>
                <div className="mt-auto p-4">
                  <button
                    onClick={logout}
                    className="w-full btn btn-secondary text-sm"
                  >
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden relative backdrop-blur-sm">
          <main className="flex-1 relative overflow-y-auto focus:outline-none">
            <div className="py-6">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                <Outlet />
              </div>
            </div>
          </main>
          <div className="absolute inset-0 pointer-events-none bg-gradient-mesh opacity-50" />
        </div>
      </div>
    </div>
  );
};

export default Layout;

interface Product {
  id: string;
  name: string;
  sku: string;
  description: {
    short: string;
    full: string;
    features: string[];
    specifications: string[];
  };
  price: number;
  compareAtPrice?: number;
  currency: string;
  category: string;
  subcategory?: string;
  tags: string[];
  metadata: {
    shopifyProductId?: string;
    shopifyVariantId?: string;
    woocommerceProductId?: string;
    customFields: {
      [key: string]: string;
    };
  };
  compatibility: {
    platforms: ('shopify' | 'woocommerce' | 'magento' | 'custom')[];
    browsers: string[];
    devices: string[];
    viewerVersions: string[];
  };
  assets: {
    modelUrl: string;
    thumbnailUrl: string;
    previewImages: string[];
    animations: {
      name: string;
      url: string;
      duration: number;
    }[];
    textures: {
      name: string;
      url: string;
      type: string;
    }[];
  };
  rendering: {
    defaultCamera: {
      position: { x: number; y: number; z: number };
      target: { x: number; y: number; z: number };
    };
    lighting: {
      type: 'studio' | 'outdoor' | 'custom';
      intensity: number;
      shadows: boolean;
    };
    background: {
      type: 'color' | 'environment' | 'transparent';
      value: string;
    };
  };
  brand: {
    id: string;
    name: string;
    logo?: string;
    contact?: {
      email: string;
      phone?: string;
    };
  };
  variants: ProductVariant[];
  specifications: {
    dimensions: {
      length: number;
      width: number;
      height: number;
      unit: 'cm' | 'in';
    };
    weight: {
      value: number;
      unit: 'kg' | 'lb';
    };
    colors: {
      name: string;
      hex: string;
      texture?: string;
      material?: string;
    }[];
    assembly?: {
      required: boolean;
      timeEstimate?: string;
      instructions?: string;
    };
    certifications: string[];
  };
  shipping: {
    dimensions: {
      length: number;
      width: number;
      height: number;
      unit: 'cm' | 'in';
    };
    weight: {
      value: number;
      unit: 'kg' | 'lb';
    };
    restrictions?: {
      hazardousMaterials: boolean;
      oversized: boolean;
      specialHandling: boolean;
    };
    estimatedDays: {
      min: number;
      max: number;
    };
  };
  seo: {
    title: string;
    description: string;
    keywords: string[];
    structuredData: {
      type: 'Product' | 'Model3D';
      additionalProperties: Record<string, unknown>;
    };
    openGraph: {
      title: string;
      description: string;
      image: string;
    };
  };
  status: 'draft' | 'published';
  versions: ProductVersion[];
  inventory: {
    sku: string;
    quantity: number;
    lowStockThreshold: number;
    allowBackorder: boolean;
    warehouseLocation?: string;
    reorderPoint?: number;
    supplierInfo?: {
      name: string;
      code: string;
      leadTime: number;
    };
  };
  analytics: {
    views: number;
    downloads: number;
    conversions: number;
    averageRating?: number;
    reviewCount?: number;
  };
  moderation: {
    status: 'pending' | 'approved' | 'rejected';
    reviewedBy?: string;
    reviewDate?: string;
    comments?: string;
  };
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  lastModifiedBy: string;
}