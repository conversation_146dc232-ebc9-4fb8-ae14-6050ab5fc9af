const { uploadToS3NewVersion } = require("../config/s3Service");
const showroomCategoryModel = require("../models/showroomCategoriesModel");

exports.handleCategory = async (categoryNames, returnSingle = true) => {
  if (
    typeof categoryNames === "string" &&
    categoryNames.startsWith("[") &&
    categoryNames.endsWith("]")
  ) {
    try {
      categoryNames = JSON.parse(categoryNames);
    } catch (error) {
      console.error("Error parsing categoryNames:", error);
      categoryNames = [categoryNames];
    }
  }
  const categoryNamesArray = Array.isArray(categoryNames)
    ? categoryNames
    : [categoryNames];
  const categoryIdsResult = [];
  for (const categoryName of categoryNamesArray) {
    let category;
    if (categoryName) {
      category = await showroomCategoryModel.findOne({ name: categoryName });
    }
    if (!category) {
      category = new showroomCategoryModel({ name: categoryName });
      await category.save();
    }
    categoryIdsResult.push(category._id);
  }
  if (returnSingle && categoryIdsResult.length === 1) {
    return categoryIdsResult[0];
  }
  return categoryIdsResult;
};

processField = async (value, files, uploadField, folder, bucket) => {
  if (typeof value === "string") {
    return value;
  }
  const path = `${bucket}/${folder}`;
  if (files && files[uploadField]) {
    const data = await uploadToS3NewVersion(
      files[uploadField][0],
      folder,
      path,
    );
    return data?.Location;
  }
  if (value === false) return null;
  return value;
};

const processNestedObject = async (
  nestedObject,
  files,
  config,
  folder,
  bucket,
) => {
  let processedObject = {};
  for (const [field, uploadField] of Object.entries(config)) {
    processedObject[field] = await processField(
      nestedObject[field],
      files,
      uploadField,
      folder,
      bucket,
    );
  }
  return processedObject;
};

exports.createShowroomRelated = async (
  bodyData,
  files,
  config,
  model,
  folder,
  bucket,
) => {
  const filteredConfig = Object.fromEntries(
    Object.entries(config).filter(([key, value]) => value != null),
  );

  let inputs = {};

  try {
    for (const [field, uploadField] of Object.entries(filteredConfig)) {
      if (
        typeof bodyData[field] === "object" &&
        bodyData[field] !== null &&
        !Array.isArray(bodyData[field])
      ) {
        inputs[field] = await processNestedObject(
          bodyData[field],
          files,
          uploadField,
          folder,
          bucket,
        );
      } else if (Array.isArray(bodyData[field])) {
        inputs[field] = bodyData[field];
      } else {
        inputs[field] = await processField(
          bodyData[field],
          files,
          uploadField,
          folder,
          bucket,
        );
      }
    }

    if (bodyData.showroomCategory) {
      inputs.category = await handleCategory(bodyData.showroomCategory);
    }
    inputs.logo.imageUrl = bodyData.logoUrl;
    const newModel = new model(inputs);
    await newModel.save();
    return newModel;
  } catch (error) {
    throw new Error("Error processing showroom related data: " + error.message);
  }
};

exports.createCategories = async () => {};

exports.getCategories = async (model) => {
  let categoryIds;
  categoryIds = await model.distinct("category");
  if (categoryIds.length == 0) categoryIds = await model.distinct("categories");

  const categories = await showroomCategoryModel
    .find({
      _id: { $in: categoryIds },
    })
    .select("name -_id");
  return categories;
};
