const router = require("express").Router();
const controller = require("../../controllers/CustomizedMaterialController");
const { validateCustomizedSchema } = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");

const { admin, designer, developer } = require("../../constants");

router
  .route("/create-customized-material")
  .post(
    validateCustomizedSchema,
    verifyRoles([admin, designer]),
    controller.createCustomizedMaterial,
  );
router.get(
  "/get-all-customized-material",
  verifyRoles([admin, developer, designer]),
  controller.getAllCustomizedMaterial,
),
  router.get(
    "/get-customized-material-by-id",
    verifyR<PERSON><PERSON>([admin, developer, designer]),
    controller.getCustomizedMaterialById,
  );
router.get(
  "/get-customized-material-with-model",
  verifyRoles([admin, developer, designer]),
  controller.getCustomizedMaterialWithModel,
);
router.delete(
  "/delete-customized-material",
  verifyRoles([admin, designer]),
  controller.deleteCustomizedMaterial,
);
module.exports = router;
