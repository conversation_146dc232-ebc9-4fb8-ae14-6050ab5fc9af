const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

console.log(`${colors.bright}${colors.cyan}=== Morpho Frontend Production Build ====${colors.reset}\n`);

// Step 1: Check if the API URL is correctly set for production
console.log(`${colors.yellow}Checking API configuration...${colors.reset}`);
const apiConfigPath = path.join(__dirname, 'src', 'config', 'apiConfig.ts');
const apiConfigContent = fs.readFileSync(apiConfigPath, 'utf8');

if (!apiConfigContent.includes('api.modularcx.link/morpho')) {
  console.error(`${colors.red}ERROR: API URL is not configured for production!${colors.reset}`);
  console.error(`Please update the API URL in src/config/apiConfig.ts to use the production URL.`);
  process.exit(1);
}

console.log(`${colors.green}✓ API configuration looks good${colors.reset}`);

// Step 2: Install dependencies
console.log(`\n${colors.yellow}Installing dependencies...${colors.reset}`);
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log(`${colors.green}✓ Dependencies installed successfully${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}ERROR: Failed to install dependencies${colors.reset}`);
  console.error(error);
  process.exit(1);
}

// Step 3: Build the application
console.log(`\n${colors.yellow}Building the application...${colors.reset}`);
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log(`${colors.green}✓ Build completed successfully${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}ERROR: Build failed${colors.reset}`);
  console.error(error);
  process.exit(1);
}

// Step 4: Verify the build
console.log(`\n${colors.yellow}Verifying build...${colors.reset}`);
const buildDir = path.join(__dirname, 'build');
if (!fs.existsSync(buildDir)) {
  console.error(`${colors.red}ERROR: Build directory not found!${colors.reset}`);
  process.exit(1);
}

const indexHtml = path.join(buildDir, 'index.html');
if (!fs.existsSync(indexHtml)) {
  console.error(`${colors.red}ERROR: index.html not found in build directory!${colors.reset}`);
  process.exit(1);
}

console.log(`${colors.green}✓ Build verification passed${colors.reset}`);

// Step 5: Success message
console.log(`\n${colors.bright}${colors.green}=== Build Successful! ====${colors.reset}`);
console.log(`\nThe production build is ready in the ${colors.cyan}build/${colors.reset} directory.`);
console.log(`You can now deploy the contents of this directory to your production server.`);
console.log(`\n${colors.yellow}Remember to ensure your backend is properly configured for production as well.${colors.reset}`);
