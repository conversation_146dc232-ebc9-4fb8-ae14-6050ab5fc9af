/**
 * Utility class for handling API responses consistently
 */
class resHandle {
  /**
   * Handle error responses with consistent format
   * @param {Object} res - Express response object
   * @param {Number} number - HTTP status code
   * @param {String} error - Error message
   * @param {Boolean} success - Success flag (default: false)
   * @param {String} errorCode - Optional error code for frontend handling
   * @returns {Object} JSON response
   */
  static handleError(res, number, error, success = false, errorCode = null) {
    // Format error message for better readability
    let formattedError = error;

    // If error is an Error object, extract the message
    if (error instanceof Error) {
      formattedError = error.message;
    }

    // If error is just "Error" followed by object representation, clean it up
    if (
      typeof formattedError === "string" &&
      formattedError.startsWith("Error")
    ) {
      formattedError = formattedError.replace(/Error:?\s?/, "");
      // Remove object notation if present
      formattedError = formattedError.replace(/\{.*\}/, "").trim();

      if (!formattedError) {
        formattedError = "An unexpected error occurred";
      }
    }

    return res.status(number).json({
      message: formattedError,
      success: success,
      errorCode: errorCode,
    });
  }

  /**
   * Handle successful data responses with consistent format
   * @param {Object} res - Express response object
   * @param {Number} number - HTTP status code
   * @param {String} message - Success message
   * @param {Boolean} success - Success flag
   * @param {Any} data - Response data
   * @returns {Object} JSON response
   */
  static handleData(res, number, message, success, data) {
    return res.status(number).json({
      message: message,
      success: success,
      data: data,
    });
  }

  /**
   * Handle paginated data responses
   * @param {Object} res - Express response object
   * @param {Number} number - HTTP status code
   * @param {String} message - Success message
   * @param {Boolean} success - Success flag
   * @param {Any} data - Response data
   * @param {Object} pagination - Pagination info (total, page, perPage)
   * @returns {Object} JSON response
   */
  static handlePaginatedData(res, number, message, success, data, pagination) {
    return res.status(number).json({
      message: message,
      success: success,
      data: data,
      pagination: {
        total: pagination.total,
        page: pagination.page,
        perPage: pagination.perPage,
        totalPages: Math.ceil(pagination.total / pagination.perPage),
      },
    });
  }

  /**
   * Handle file upload responses
   * @param {Object} res - Express response object
   * @param {Number} number - HTTP status code
   * @param {String} message - Success message
   * @param {Boolean} success - Success flag
   * @param {Object} fileInfo - File information
   * @returns {Object} JSON response
   */
  static handleFileUpload(res, number, message, success, fileInfo) {
    return res.status(number).json({
      message: message,
      success: success,
      file: fileInfo,
    });
  }
}

module.exports = resHandle;
