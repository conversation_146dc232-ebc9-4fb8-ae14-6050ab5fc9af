const mongoose = require("mongoose");

const mediaModelSchema = new mongoose.Schema(
  {
    url: {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    name: {
      type: String,
      required: true,
    },
    isMedia: {
      type: Boolean,
      default: true,
    },
    model: {
      type: mongoose.Types.ObjectId,
      ref: "GlbModel",
    },
  },
  { timestamps: true, collection: "medias" }
);

const mediaModel = mongoose.model("MediaModel", mediaModelSchema, "MediaModel");
module.exports = mediaModel;
