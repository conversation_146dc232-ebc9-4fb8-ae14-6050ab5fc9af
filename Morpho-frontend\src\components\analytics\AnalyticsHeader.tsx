import React from 'react';

interface AnalyticsHeaderProps {
  title: string;
  description?: string;
  timeframe: string;
  onTimeframeChange: (timeframe: string) => void;
  timeframeOptions: string[];
}

const AnalyticsHeader: React.FC<AnalyticsHeaderProps> = ({
  title,
  description,
  timeframe,
  onTimeframeChange,
  timeframeOptions
}) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-light tracking-wide text-gray-100">{title}</h1>
        {description && <p className="text-gray-400 mt-2">{description}</p>}
      </div>
      <div className="flex items-center gap-2">
        {timeframeOptions.map((option) => (
          <button
            key={option}
            onClick={() => onTimeframeChange(option)}
            className={`px-3 py-1 rounded-lg text-sm ${
              timeframe === option 
                ? 'bg-brand-500/20 text-brand-300'
                : 'text-gray-400 hover:bg-dark-200'
            }`}
          >
            {option.charAt(0).toUpperCase() + option.slice(1)}
          </button>
        ))}
      </div>
    </div>
  );
};

export default AnalyticsHeader;