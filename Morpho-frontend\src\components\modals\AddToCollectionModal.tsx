import React, { useState } from "react";
import { X, <PERSON>ert<PERSON><PERSON><PERSON>, FolderPlus } from "lucide-react";
import { useCollections } from "../../hooks/useCollection";
import { Model3D } from "../../types/models";

interface AddToCollectionModalProps {
  model: Model3D;
  onClose: () => void;
  onSuccess: () => void;
}

const AddToCollectionModal: React.FC<AddToCollectionModalProps> = ({
  model,
  onClose,
  onSuccess,
}) => {
  const { collections, loading, error, addProductsToCollection } =
    useCollections();
  const [selectedCollection, setSelectedCollection] = useState<string>("");
  const [submitting, setSubmitting] = useState(false);
  const [modalError, setModalError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCollection) {
      setModalError("Please select a collection");
      return;
    }

    try {
      setSubmitting(true);
      setModalError(null);

      await addProductsToCollection(selectedCollection, [model._id]);
      onSuccess();
      onClose();
    } catch (err) {
      setModalError(
        err instanceof Error
          ? err.message
          : "Failed to add model to collection",
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-dark-300 rounded-xl w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <h2 className="text-xl font-light text-gray-100">
            Add to Collection
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-dark-200 text-gray-400 transition-colors"
          >
            <X size={20} strokeWidth={1.5} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {modalError && (
            <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2">
              <AlertCircle size={18} strokeWidth={1.5} />
              <span>{modalError}</span>
            </div>
          )}

          {error && (
            <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2">
              <AlertCircle size={18} strokeWidth={1.5} />
              <span>{error}</span>
            </div>
          )}

          <div>
            <p className="text-gray-300 mb-2">
              Add{" "}
              <span className="text-brand-300 font-medium">{model.name}</span>{" "}
              to:
            </p>

            {loading ? (
              <div className="flex items-center justify-center py-10">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-300"></div>
              </div>
            ) : collections.length === 0 ? (
              <div className="p-4 text-center bg-dark-200 rounded-lg">
                <p className="text-gray-400 mb-2">
                  You don't have any collections yet.
                </p>
                <p className="text-gray-500 text-sm">
                  Create a collection first to add models to it.
                </p>
              </div>
            ) : (
              <select
                value={selectedCollection}
                onChange={(e) => setSelectedCollection(e.target.value)}
                className="input w-full"
              >
                <option value="">Select a collection</option>
                {collections.map((collection) => (
                  <option key={collection._id} value={collection._id}>
                    {collection.name}
                  </option>
                ))}
              </select>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-secondary"
              disabled={submitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting || collections.length === 0}
              className="btn btn-primary flex items-center gap-2"
            >
              {submitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  <span>Adding...</span>
                </>
              ) : (
                <>
                  <FolderPlus size={18} strokeWidth={1.5} />
                  <span>Add to Collection</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddToCollectionModal;
