import React, { useState, useEffect } from "react";
import { X, Upload, AlertCircle, Image } from "lucide-react";
import { api } from "../../services/api";
import { Model3D } from "../types/models";
import { useCollections } from "../../hooks/useCollection";

interface SingleUploadModalProps {
  onClose: () => void;
  onUpload?: (file: File) => void;
  onSuccess: () => void;
}

const SingleUploadModal: React.FC<SingleUploadModalProps> = ({
  onClose,
  onUpload,
  onSuccess,
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [sku, setSku] = useState("");
  const [price, setPrice] = useState("");
  const [uploadStatus, setUploadStatus] = useState<
    "idle" | "uploading" | "success" | "error"
  >("idle");
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  // Add collection selection state
  const { collections, fetchCollections } = useCollections();
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>("");

  // Fetch collections when the modal opens
  useEffect(() => {
    fetchCollections();
  }, [fetchCollections]);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setName(selectedFile.name.split(".")[0]); // Set default name from filename
    }
  };

  // Handle image file selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setImageFile(selectedFile);

      // Create preview URL for image
      const url = URL.createObjectURL(selectedFile);
      setPreviewUrl(url);
    }
  };

  // Handle drag events
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      setFile(droppedFile);
      setName(droppedFile.name.split(".")[0]);
    }
  };

  // Clean up preview URL on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError("Please select a 3D model file to upload");
      return;
    }

    if (!name) {
      setError("Name is required");
      return;
    }

    try {
      setUploadStatus("uploading");
      setError(null);

      // Call onUpload prop if provided
      if (onUpload) {
        onUpload(file);
      }

      // Upload the file using the API
      const formData = new FormData();

      // Add files with correct field names as expected by the API
      formData.append("file", file);

      // Add image if selected
      if (imageFile) {
        formData.append("imageUrl", imageFile);
      }

      // Add other form fields
      formData.append("name", name);
      formData.append("description", description);
      formData.append("price", price || "0");
      formData.append("sku", sku || description);

      const response = await api.uploadModel(formData);

      if (response && response.success) {
        // If a collection is selected, add the model to the collection
        if (selectedCollectionId && response.data && response.data._id) {
          try {
            await api.addModelToCollection(
              selectedCollectionId,
              response.data._id,
            );
          } catch (collectionError) {
            console.error(
              "Failed to add model to collection:",
              collectionError,
            );
            // We don't want to fail the whole upload if just the collection part fails
          }
        }

        setUploadStatus("success");
        setTimeout(() => {
          onSuccess();
          onClose();
        }, 1500);
      } else {
        throw new Error(response.message || "Upload failed");
      }
    } catch (err) {
      setUploadStatus("error");
      setError(
        err instanceof Error ? err.message : "An error occurred during upload",
      );
    }
  };

  return (
    <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-dark-300 rounded-xl w-full max-w-2xl">
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <h2 className="text-xl font-light text-gray-100">Upload 3D Model</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-dark-200 text-gray-400 transition-colors"
          >
            <X size={20} strokeWidth={1.5} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2">
              <AlertCircle size={18} strokeWidth={1.5} />
              <span>{error}</span>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 3D Model Upload Area */}
            <div
              className={`border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-colors ${
                dragActive
                  ? "border-brand-300 bg-brand-500/5"
                  : "border-gray-700 hover:border-gray-600"
              }`}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
              onClick={() => document.getElementById("file-upload")?.click()}
            >
              <input
                id="file-upload"
                type="file"
                accept=".glb,.gltf,.obj,.fbx,.stl,.usdz"
                onChange={handleFileChange}
                className="hidden"
              />

              <div className="space-y-2">
                <div className="w-16 h-16 mx-auto rounded-lg bg-dark-200 flex items-center justify-center">
                  <Upload
                    size={24}
                    strokeWidth={1.5}
                    className={file ? "text-brand-300" : "text-gray-500"}
                  />
                </div>
                {file ? (
                  <>
                    <p className="text-gray-300 font-light">{file.name}</p>
                    <p className="text-sm text-gray-500">
                      {(file.size / (1024 * 1024)).toFixed(2)} MB
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-gray-400 font-light">Upload 3D Model</p>
                    <p className="text-xs text-gray-500">
                      GLB, GLTF, OBJ, FBX, STL, USDZ
                    </p>
                  </>
                )}
              </div>
            </div>

            {/* Image Upload Area */}
            <div
              className="border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-colors border-gray-700 hover:border-gray-600"
              onClick={() => document.getElementById("image-upload")?.click()}
            >
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />

              {previewUrl ? (
                <div className="flex flex-col items-center">
                  <div className="w-24 h-24 mb-2 rounded-lg overflow-hidden">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <p className="text-gray-300 font-light">{imageFile?.name}</p>
                  <p className="text-xs text-gray-500">
                    {imageFile
                      ? (imageFile.size / (1024 * 1024)).toFixed(2) + " MB"
                      : ""}
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="w-16 h-16 mx-auto rounded-lg bg-dark-200 flex items-center justify-center">
                    <Image
                      size={24}
                      strokeWidth={1.5}
                      className="text-gray-500"
                    />
                  </div>
                  <p className="text-gray-400 font-light">Upload Thumbnail</p>
                  <p className="text-xs text-gray-500">JPG, PNG, WEBP</p>
                </div>
              )}
            </div>
          </div>

          {/* Model Details */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-light text-gray-400 mb-2">
                Name <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="input"
                placeholder="Enter model name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-light text-gray-400 mb-2">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="input min-h-[80px]"
                placeholder="Enter model description"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-light text-gray-400 mb-2">
                  SKU
                </label>
                <input
                  type="text"
                  value={sku}
                  onChange={(e) => setSku(e.target.value)}
                  className="input"
                  placeholder="Enter SKU"
                />
              </div>

              <div>
                <label className="block text-sm font-light text-gray-400 mb-2">
                  Price
                </label>
                <input
                  type="number"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="input"
                  placeholder="Enter price"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            {/* Collection Selection Dropdown */}
            <div>
              <label className="block text-sm font-light text-gray-400 mb-2">
                Add to Collection (Optional)
              </label>
              <select
                value={selectedCollectionId}
                onChange={(e) => setSelectedCollectionId(e.target.value)}
                className="input"
              >
                <option value="">None</option>
                {collections.map((collection) => (
                  <option key={collection._id} value={collection._id}>
                    {collection.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="btn btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={uploadStatus === "uploading"}
              className="btn btn-primary flex items-center gap-2"
            >
              {uploadStatus === "uploading" ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : uploadStatus === "success" ? (
                <>
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span>Uploaded!</span>
                </>
              ) : (
                <>
                  <Upload size={18} strokeWidth={1.5} />
                  <span>Upload Model</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SingleUploadModal;
