import React from "react";
import { useSubscription } from "../hooks/useSubscription";
import { Link } from "react-router-dom";

/**
 * SubscriptionBanner component displays information about the user's subscription
 * and product limits at the top of relevant pages
 */
const SubscriptionBanner: React.FC = () => {
  const { subscription, loading, refreshSubscription } = useSubscription();

  // No need for additional polling here as the useSubscription hook
  // already handles WebSocket updates and fallback polling

  if (loading) {
    return null;
  }

  // Don't show banner for enterprise users or if subscription data is missing
  if (!subscription || subscription.plan === "enterprise") {
    return null;
  }

  // Calculate product usage percentage
  const productLimit = subscription.productLimit || 1;

  // Use the actual product count from the subscription, or default to 0
  const productCount = subscription.productCount || 0;

  // For infinite product limit, set usage to 0%
  const usagePercentage =
    productLimit === Infinity
      ? 0
      : Math.min(Math.round((productCount / productLimit) * 100), 100);

  // Determine banner color based on usage
  let bannerColor = "bg-green-500/10 border-green-500/20 text-green-400";
  if (usagePercentage >= 90) {
    bannerColor = "bg-red-500/10 border-red-500/20 text-red-400";
  } else if (usagePercentage >= 75) {
    bannerColor = "bg-yellow-500/10 border-yellow-500/20 text-yellow-400";
  }

  return (
    <div
      className={`w-full px-4 py-2 border-b ${bannerColor} flex items-center justify-between`}
    >
      <div className="flex items-center space-x-2">
        <span className="text-sm">
          {subscription.plan === "free" ? (
            <>
              You're on the <strong>Free Plan</strong> with {productLimit}{" "}
              product limit.
            </>
          ) : (
            <>
              You're on the{" "}
              <strong className="capitalize">{subscription.plan} Plan</strong>{" "}
              with {productLimit === Infinity ? "unlimited" : productLimit}{" "}
              product limit.
            </>
          )}
        </span>

        {usagePercentage >= 75 && (
          <span className="text-sm font-medium">
            {productCount}/{productLimit} products used ({usagePercentage}%)
          </span>
        )}
      </div>

      {subscription.plan !== "enterprise" && (
        <Link
          to="/dashboard/subscription"
          className="text-sm font-medium hover:underline"
        >
          {usagePercentage >= 90 ? "Upgrade Now" : "Manage Subscription"}
        </Link>
      )}
    </div>
  );
};

export default SubscriptionBanner;
