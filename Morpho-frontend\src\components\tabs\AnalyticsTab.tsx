import React, { useEffect } from 'react';
import { TrendingUp, Eye, Download, ShoppingCart, Clock } from 'lucide-react';
import { Model3D } from '../../types/models';
import { useModelAnalytics } from '../../hooks/useModelAnalytics';
import { analyticsService } from '../../services/analytics';

interface AnalyticsTabProps {
  model: Model3D;
}

const AnalyticsTab: React.FC<AnalyticsTabProps> = ({ model }) => {
  const { data: analytics, loading, error } = useModelAnalytics(model._id);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-300" />
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400">
        {error || 'Failed to load analytics'}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-brand-500/10 flex items-center justify-center">
              <Eye className="w-5 h-5 text-brand-300" strokeWidth={1.5} />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Views</p>
              <p className="text-2xl font-light text-gray-100 mt-1">
                {analytics.views.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="card rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-indigo-500/10 flex items-center justify-center">
              <Download className="w-5 h-5 text-indigo-400" strokeWidth={1.5} />
            </div>
            <div>
              <p className="text-sm text-gray-400">Downloads</p>
              <p className="text-2xl font-light text-gray-100 mt-1">
                {analytics.downloads.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="card rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-green-500/10 flex items-center justify-center">
              <ShoppingCart className="w-5 h-5 text-green-400" strokeWidth={1.5} />
            </div>
            <div>
              <p className="text-sm text-gray-400">Conversions</p>
              <p className="text-2xl font-light text-gray-100 mt-1">
                {analytics.conversions.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="card rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-yellow-500/10 flex items-center justify-center">
              <Clock className="w-5 h-5 text-yellow-400" strokeWidth={1.5} />
            </div>
            <div>
              <p className="text-sm text-gray-400">Avg. Time Spent</p>
              <p className="text-2xl font-light text-gray-100 mt-1">
                {analytics.avgTimeSpent}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card rounded-xl p-6">
          <h3 className="text-lg font-light text-gray-200 mb-6">Views by Device</h3>
          <div className="relative pt-6">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm text-gray-400">Desktop</p>
              <p className="text-sm text-gray-300">{analytics.viewsByDevice.desktop}%</p>
            </div>
            <div className="w-full h-2 bg-dark-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-brand-400 rounded-full"
                style={{ width: `${analytics.viewsByDevice.desktop}%` }}
              />
            </div>

            <div className="flex items-center justify-between mb-2 mt-4">
              <p className="text-sm text-gray-400">Mobile</p>
              <p className="text-sm text-gray-300">{analytics.viewsByDevice.mobile}%</p>
            </div>
            <div className="w-full h-2 bg-dark-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-indigo-400 rounded-full"
                style={{ width: `${analytics.viewsByDevice.mobile}%` }}
              />
            </div>

            <div className="flex items-center justify-between mb-2 mt-4">
              <p className="text-sm text-gray-400">Tablet</p>
              <p className="text-sm text-gray-300">{analytics.viewsByDevice.tablet}%</p>
            </div>
            <div className="w-full h-2 bg-dark-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-green-400 rounded-full"
                style={{ width: `${analytics.viewsByDevice.tablet}%` }}
              />
            </div>
          </div>
        </div>

        <div className="card rounded-xl p-6">
          <h3 className="text-lg font-light text-gray-200 mb-6">Daily Views</h3>
          <div className="relative h-48">
            <div className="absolute inset-0 flex items-end justify-between gap-2">
              {analytics.dailyViews.map((value, index) => (
                <div key={index} className="w-full">
                  <div 
                    className="w-full bg-brand-500/20 hover:bg-brand-500/30 transition-colors rounded-sm"
                    style={{ height: `${(value / Math.max(...analytics.dailyViews)) * 100}%` }}
                  />
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-between mt-4 text-sm text-gray-500">
            <span>Mon</span>
            <span>Tue</span>
            <span>Wed</span>
            <span>Thu</span>
            <span>Fri</span>
            <span>Sat</span>
            <span>Sun</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsTab;