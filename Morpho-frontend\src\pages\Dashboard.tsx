import React, { useState, useEffect } from "react";
import {
  Box,
  ShoppingCart,
  Users,
  TrendingUp,
  Clock,
  Package,
  Activity,
  BarChart2,
  Calendar,
  CreditCard,
} from "lucide-react";
import { useModels } from "../hooks/useModels";
import { Link } from "react-router-dom";

const Dashboard = () => {
  const { models, loading: modelsLoading } = useModels();
  const [timeframe, setTimeframe] = useState<"week" | "month" | "year">(
    "month"
  );

  // Analytics data (mock data - replace with real API data)
  const [analytics, setAnalytics] = useState({
    totalViews: 12453,
    monthlyGrowth: 23,
    activeProjects: 8,
    conversionRate: 3.2,
    recentActivity: [
      {
        id: 1,
        type: "model_upload",
        title: "Modern Chair Collection",
        description: "New 3D model uploaded",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 2,
        type: "request_status",
        title: "Dining Table Set",
        description: "Modeling request approved",
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
      },
    ],
    performanceMetrics: {
      views: [1200, 1500, 1300, 1800, 1600, 2000, 1900],
      conversions: [25, 30, 28, 35, 32, 40, 38],
      dates: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    },
  });

  const stats = [
    {
      name: "3D Models",
      value: models.length,
      change: "+12%",
      icon: Box,
      color: "text-brand-300",
      bg: "bg-brand-500/10",
      link: "/dashboard/library?view=all",
    },
    {
      name: "Active Requests",
      value: 0, // Assuming no active requests
      change: "+5%",
      icon: ShoppingCart,
      color: "text-indigo-400",
      bg: "bg-indigo-500/10",
      link: "/dashboard/requests?status=active",
    },
    {
      name: "Total Views",
      value: analytics.totalViews.toLocaleString(),
      change: `+${analytics.monthlyGrowth}%`,
      icon: TrendingUp,
      color: "text-green-400",
      bg: "bg-green-500/10",
      link: "/dashboard/analytics?metric=views",
    },
    {
      name: "Conversion Rate",
      value: `${analytics.conversionRate}%`,
      change: "+0.8%",
      icon: Activity,
      color: "text-yellow-400",
      bg: "bg-yellow-500/10",
      link: "/dashboard/analytics?metric=conversions",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-light tracking-wide text-gray-100">
            Dashboard
          </h1>
          <p className="text-gray-400 mt-2">
            Overview of your 3D product management
          </p>
        </div>
        <div className="flex items-center gap-2">
          {["week", "month", "year"].map((t) => (
            <button
              key={t}
              onClick={() => setTimeframe(t as typeof timeframe)}
              className={`px-3 py-1 rounded-lg text-sm ${
                timeframe === t
                  ? "bg-brand-500/20 text-brand-300"
                  : "text-gray-400 hover:bg-dark-200"
              }`}
            >
              {t.charAt(0).toUpperCase() + t.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          const Component = stat.link ? Link : "div";
          return (
            <Component
              key={stat.name}
              to={stat.link}
              className="card rounded-xl overflow-hidden p-6 card-hover"
            >
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div
                    className={`w-12 h-12 rounded-lg ${stat.bg} flex items-center justify-center`}
                  >
                    <Icon
                      className={`h-6 w-6 ${stat.color}`}
                      strokeWidth={1.5}
                    />
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-light text-gray-400">
                    {stat.name}
                  </p>
                  <div className="flex items-baseline mt-1">
                    <p className="text-2xl font-light text-gray-100">
                      {stat.value}
                    </p>
                    <span
                      className={`ml-2 text-xs ${
                        stat.change.startsWith("+")
                          ? "text-green-400"
                          : "text-red-400"
                      }`}
                    >
                      {stat.change}
                    </span>
                  </div>
                </div>
              </div>
            </Component>
          );
        })}
      </div>

      {/* Charts and Activity */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
        {/* Performance Chart */}
        <div className="lg:col-span-2 card rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-light text-gray-200">
              Performance Overview
            </h2>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-brand-400"></span>
                <span className="text-sm text-gray-400">Views</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-indigo-400"></span>
                <span className="text-sm text-gray-400">Conversions</span>
              </div>
            </div>
          </div>
          <div className="h-64 relative">
            {/* Chart visualization would go here */}
            <div className="absolute inset-0 flex items-end justify-between gap-2">
              {analytics.performanceMetrics.views.map((value, index) => (
                <div
                  key={index}
                  className="w-full flex flex-col items-center gap-2"
                >
                  <div className="w-full flex flex-col gap-1">
                    <div
                      className="w-full bg-brand-500/20 rounded-sm"
                      style={{ height: `${(value / 2000) * 100}%` }}
                    />
                    <div
                      className="w-full bg-indigo-500/20 rounded-sm"
                      style={{
                        height: `${
                          (analytics.performanceMetrics.conversions[index] /
                            40) *
                          100
                        }%`,
                      }}
                    />
                  </div>
                  <span className="text-xs text-gray-500">
                    {analytics.performanceMetrics.dates[index]}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card rounded-xl p-6">
          <h2 className="text-lg font-light text-gray-200 mb-6">
            Recent Activity
          </h2>
          <div className="space-y-6">
            {analytics.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start gap-4">
                <div
                  className={`w-8 h-8 rounded-lg ${
                    activity.type === "model_upload"
                      ? "bg-brand-500/10"
                      : "bg-indigo-500/10"
                  } flex items-center justify-center`}
                >
                  {activity.type === "model_upload" ? (
                    <Package
                      className="w-4 h-4 text-brand-300"
                      strokeWidth={1.5}
                    />
                  ) : (
                    <Clock
                      className="w-4 h-4 text-indigo-400"
                      strokeWidth={1.5}
                    />
                  )}
                </div>
                <div>
                  <p className="text-sm text-gray-300">{activity.title}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {activity.description}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(activity.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        <Link
          to="/dashboard/library?action=upload"
          className="card rounded-xl p-6 hover:border-brand-300/30 transition-all duration-300"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-brand-500/10 flex items-center justify-center">
              <Package className="w-6 h-6 text-brand-300" strokeWidth={1.5} />
            </div>
            <div>
              <h3 className="text-gray-200 font-light">Upload New Model</h3>
              <p className="text-sm text-gray-400 mt-1">
                Add a new 3D model to your library
              </p>
            </div>
          </div>
        </Link>

        <Link
          to="/dashboard/subscription"
          className="card rounded-xl p-6 hover:border-brand-300/30 transition-all duration-300"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-indigo-500/10 flex items-center justify-center">
              <CreditCard
                className="w-6 h-6 text-indigo-300"
                strokeWidth={1.5}
              />
            </div>
            <div>
              <h3 className="text-gray-200 font-light">Manage Subscription</h3>
              <p className="text-sm text-gray-400 mt-1">
                View or upgrade your subscription plan
              </p>
            </div>
          </div>
        </Link>

        <Link
          to="/dashboard/requests?action=new"
          className="card rounded-xl p-6 hover:border-brand-300/30 transition-all duration-300"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-indigo-500/10 flex items-center justify-center">
              <ShoppingCart
                className="w-6 h-6 text-indigo-400"
                strokeWidth={1.5}
              />
            </div>
            <div>
              <h3 className="text-gray-200 font-light">Create Request</h3>
              <p className="text-sm text-gray-400 mt-1">
                Submit a new modeling request
              </p>
            </div>
          </div>
        </Link>

        <Link
          to="/dashboard/analytics"
          className="card rounded-xl p-6 hover:border-brand-300/30 transition-all duration-300"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-green-500/10 flex items-center justify-center">
              <BarChart2 className="w-6 h-6 text-green-400" strokeWidth={1.5} />
            </div>
            <div>
              <h3 className="text-gray-200 font-light">View Analytics</h3>
              <p className="text-sm text-gray-400 mt-1">
                Check your model performance
              </p>
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
};

export default Dashboard;
