const router = require("express").Router();
const controller = require("../../controllers/userController");
const verifyJWT = require("../../middleware/verifyJWT");
const verifyRoles = require("../../middleware/verifyRole");
const { designer, admin, client } = require("../../constants");
const { validateUserSchema } = require("../../validation/middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
// const verifyRoles = require("../../middleware/verifyRole");

router.get(
  "/getAll",
  verifyJWT,
  verifyRoles([admin]),
  asyncError<PERSON>andler(controller.getAllUsers)
);
router.get(
  "/getById",
  verifyJWT,
  verifyRoles([admin]),
  asyncErrorHandler(controller.getUserByIdWithRole)
);
router.post(
  "/getAllUsersByRole",
  verifyJWT,
  // verifyRoles([admin]),
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getAllUsersByRole)
);
router.post(
  "/get-users-by-role",
  verifyJWT,
  // verifyRoles([admin]),
  asyncErrorHandler(controller.getUsersByRole)
);
router.get(
  "/getUserDetails",
  asyncErrorHandler(controller.getUserDetailsUsingCookie)
); // dont add the verifyJWT middleware
router.put(
  "/updateInfo",
  verifyJWT,
  asyncErrorHandler(controller.updateUserInfo)
);
router.put(
  "/softDelete",
  verifyRoles([admin]),
  asyncErrorHandler(controller.SoftdeleteUserById)
);
router.delete(
  "/deleteById",
  verifyJWT,
  verifyRoles([admin]),
  asyncErrorHandler(controller.deleteUserById)
);

module.exports = router;
