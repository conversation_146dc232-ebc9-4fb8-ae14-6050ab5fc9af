import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { api } from "../services/api";
import { storage } from "../utils/storage";
import type { LoginCredentials, SignupCredentials } from "../types/auth";

interface AuthState {
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  token: string | null;
}

export const useAuth = () => {
  const [state, setState] = useState<AuthState>({
    isAuthenticated: storage.isAuthenticated(),
    loading: false,
    error: null,
    token: storage.getToken(),
  });

  const navigate = useNavigate();

  const login = async (credentials: LoginCredentials) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const loginResult = await api.login(
        credentials.email,
        credentials.password
      );
      const token = storage.getToken();

      setState((prev) => ({
        ...prev,
        isAuthenticated: true,
        loading: false,
        token: token,
      }));
      navigate("/dashboard");
    } catch (err) {
      setState((prev) => ({
        ...prev,
        isAuthenticated: false,
        error: err instanceof Error ? err.message : "Invalid credentials",
        loading: false,
      }));
    }
  };

  const logout = () => {
    storage.clearAuth();
    storage.clearSubscriptionStatus(); // Clear subscription status on logout
    storage.clearShopifyCredentials(); // Also clear any Shopify credentials
    setState({
      isAuthenticated: false,
      loading: false,
      error: null,
      token: null,
    });
    navigate("/login");
  };

  const signup = async (credentials: SignupCredentials) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const result = await api.signup(credentials);
      setState((prev) => ({ ...prev, loading: false }));
      return result;
    } catch (err) {
      setState((prev) => ({
        ...prev,
        error: err instanceof Error ? err.message : "Signup failed",
        loading: false,
      }));
      throw err;
    }
  };

  const verifyOTP = async (userId: string, otp: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const result = await api.verifyOTP(userId, otp);

      // If verification is successful and we get a token
      if (result.accessToken) {
        setState((prev) => ({
          ...prev,
          isAuthenticated: true,
          loading: false,
          token: result.accessToken,
        }));
      } else {
        setState((prev) => ({ ...prev, loading: false }));
      }

      return result;
    } catch (err) {
      setState((prev) => ({
        ...prev,
        error: err instanceof Error ? err.message : "OTP verification failed",
        loading: false,
      }));
      throw err;
    }
  };

  const resendOTP = async (userId: string, email: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const result = await api.resendOTP(userId, email);
      setState((prev) => ({ ...prev, loading: false }));
      return result;
    } catch (err) {
      setState((prev) => ({
        ...prev,
        error: err instanceof Error ? err.message : "Failed to resend OTP",
        loading: false,
      }));
      throw err;
    }
  };

  return {
    login,
    logout,
    signup,
    verifyOTP,
    resendOTP,
    ...state,
  };
};
