const controller = require("../../controllers/customizedShowroomController");
const router = require("express").Router();
const { upload } = require("../../middleware/multer-middleware");

const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.post(
  "/create",
  upload.fields([
    { name: "url", maxCount: 1 },
    { name: "logoUrl", maxCount: 1 },
    { name: "imageUrls", maxCount: 6 },
    { name: "hdrUrl", maxCount: 1 },
    { name: "hdrIcon", maxCount: 1 },
  ]),
  asyncError<PERSON>and<PERSON>(controller.create)
);
router.get("/get-by-id-and-sku", async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getByIdAndSku));
router.get("/get-by-id/:id", asyncError<PERSON><PERSON><PERSON>(controller.getById));
router.get("/get-skus", asyncError<PERSON><PERSON><PERSON>(controller.getMostUsedSkus));
router.get(
  "/get-all-categories",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getAllCategoriesOfShowroom)
);
router.get("/get-all-by-user", asyncErrorHandler(controller.getAllByUser));
router.put(
  "/update-by-id/:id",
  upload.fields([
    { name: "url", maxCount: 1 },
    { name: "logo", maxCount: 1 },
    { name: "imageUrls", maxCount: 6 },
    { name: "hdrUrl", maxCount: 1 },
    { name: "hdrIcon", maxCount: 1 },
  ]),
  asyncErrorHandler(controller.updateById)
);
router.delete("/delete-by-id/:id", asyncErrorHandler(controller.deleteById));

module.exports = router;
