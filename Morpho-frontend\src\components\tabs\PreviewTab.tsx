import React from 'react';
import { FileCode, Box, Smartphone } from 'lucide-react';
import { Model3D } from '../../types/models';


interface PreviewTabProps {
  model: Model3D;
}

const PreviewTab: React.FC<PreviewTabProps> = ({ model }) => {
  if (!model) {
    return null;
  }

  // Get the format safely
  const getFormat = () => {
    if (!model.url) return 'Unknown';
    return model.url.endsWith('.glb') ? 'GLB' : 'GLTF';
  };

  return (
    <div className="grid gap-6">
      <div className="card rounded-xl p-6">
        <div className="space-y-4">
          <h3 className="text-lg font-light text-gray-200">Technical Specifications</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <FileCode size={16} strokeWidth={1.5} />
                <span>Format: {getFormat()}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <Box size={16} strokeWidth={1.5} />
                <span>AR Compatible</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <Smartphone size={16} strokeWidth={1.5} />
                <span>Mobile Optimized</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <span className="w-4 h-4 rounded bg-brand-500/20 flex items-center justify-center">
                  <span className="w-2 h-2 rounded-sm bg-brand-400"></span>
                </span>
                <span>Textures: PBR Materials</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <span className="w-4 h-4 rounded bg-brand-500/20 flex items-center justify-center">
                  <span className="w-2 h-2 rounded-sm bg-brand-400"></span>
                </span>
                <span>Polygons: Optimized</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <span className="w-4 h-4 rounded bg-brand-500/20 flex items-center justify-center">
                  <span className="w-2 h-2 rounded-sm bg-brand-400"></span>
                </span>
                <span>UV Maps: Clean</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="card rounded-xl p-6">
        <div className="space-y-4">
          <h3 className="text-lg font-light text-gray-200">Usage Instructions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">Viewer Controls</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li className="flex items-center gap-2">
                  <span className="w-6 h-6 rounded bg-dark-200 flex items-center justify-center text-xs">🖱️</span>
                  Left click + drag to rotate
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-6 h-6 rounded bg-dark-200 flex items-center justify-center text-xs">⚡</span>
                  Right click + drag to pan
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-6 h-6 rounded bg-dark-200 flex items-center justify-center text-xs">↕️</span>
                  Scroll to zoom
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">AR Features</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li className="flex items-center gap-2">
                  <span className="w-6 h-6 rounded bg-dark-200 flex items-center justify-center text-xs">📱</span>
                  Compatible with iOS and Android
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-6 h-6 rounded bg-dark-200 flex items-center justify-center text-xs">🎯</span>
                  Place in your space
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-6 h-6 rounded bg-dark-200 flex items-center justify-center text-xs">📏</span>
                  True to scale visualization
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewTab;