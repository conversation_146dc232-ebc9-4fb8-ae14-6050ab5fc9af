import React from 'react';

interface GlassContainerProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
  style?: React.CSSProperties;
  hoverEffect?: 'glow' | 'lift' | 'none';
}

export const GlassContainer: React.FC<GlassContainerProps> = ({
  children,
  className = '',
  as: Component = 'div',
  style = {},
  hoverEffect = 'none', // Default to none for better performance
  ...props
}) => {
  // Removed hover state tracking for performance

  // Base styles with simplified effects
  const baseStyles = "bg-[#12121A]/40 backdrop-blur-md border border-white/10 transition-all duration-300";

  return (
    <Component
      className={`${baseStyles} ${className}`}
      style={{
        // Simple box shadow without effects
        boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
        ...style
      }}
      {...props}
    >
      {children}
    </Component>
  );
};