import React, { memo } from 'react';
import { Model3D } from '../types/models';
import PreviewTab from './tabs/PreviewTab';
import InfoTab from './tabs/InfoTab';
import PublishTab from './tabs/PublishTab';
import AnalyticsTab from './tabs/AnalyticsTab';

interface ProductTabsProps {
  activeTab: 'preview' | 'info' | 'publish' | 'analytics';
  setActiveTab: (tab: 'preview' | 'info' | 'publish' | 'analytics') => void;
  model: Model3D;
  isEditing: boolean;
  onModelUpdate: (model: Model3D) => void;
}

const ProductTabs: React.FC<ProductTabsProps> = memo(({
  activeTab,
  setActiveTab,
  model,
  isEditing,
  onModelUpdate,
}) => {
  const tabs = [
    { id: 'preview', label: '3D Preview' },
    { id: 'info', label: 'Product Information' },
    { id: 'analytics', label: 'Analytics' },
    { id: 'publish', label: 'Publish' },
  ];

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-800">
        <nav className="flex gap-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as 'preview' | 'info' | 'publish')}
              className={`pb-3 text-sm font-light border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-brand-300 text-brand-300'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className="space-y-6">
        {activeTab === 'preview' && (
          <PreviewTab model={model} />
        )}
        {activeTab === 'info' && (
          <InfoTab 
            model={model} 
            isEditing={isEditing}
            onModelUpdate={onModelUpdate}
          />
        )}
        {activeTab === 'publish' && (
          <PublishTab model={model} />
        )}
        {activeTab === 'analytics' && (
          <AnalyticsTab model={model} />
        )}
      </div>
    </div>
  );
});

ProductTabs.displayName = 'ProductTabs';
export default ProductTabs;