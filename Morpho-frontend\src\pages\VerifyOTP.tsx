import React, { useState, useEffect } from "react";
import { Box, ArrowLeft } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import { useAuth } from "../hooks/useAuth";

interface OTPVerificationState {
  userId: string;
  email: string;
  otp: string;
  loading: boolean;
  error: string | null;
  verified: boolean;
}

const VerifyOTP: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    verifyOTP,
    resendOTP,
    loading,
    error: authError,
    isAuthenticated,
  } = useAuth();
  const [state, setState] = useState<OTPVerificationState>({
    userId: "",
    email: "",
    otp: "",
    loading: false,
    error: null,
    verified: false,
  });

  // Extract userId and email from location state
  useEffect(() => {
    // If user is already authenticated, redirect to dashboard
    if (isAuthenticated) {
      navigate("/dashboard");
      return;
    }

    if (location.state) {
      const { userId, email } = location.state as {
        userId: string;
        email: string;
      };
      if (userId && email) {
        setState((prev) => ({ ...prev, userId, email }));
      } else {
        setState((prev) => ({
          ...prev,
          error: "Missing user information. Please try signing up again.",
        }));
      }
    } else {
      setState((prev) => ({
        ...prev,
        error: "Missing user information. Please try signing up again.",
      }));
    }
  }, [location.state, isAuthenticated, navigate]);

  const handleOTPChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    // Only allow numbers and limit to 4 digits
    if (/^\d*$/.test(value) && value.length <= 4) {
      setState((prev) => ({ ...prev, otp: value }));
    }
  };

  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (state.otp.length !== 4) {
      setState((prev) => ({
        ...prev,
        error: "Please enter a valid 4-digit OTP",
      }));
      return;
    }

    try {
      // Use the auth hook's verifyOTP method
      const result = await verifyOTP(state.userId, state.otp);

      // If verification is successful
      setState((prev) => ({ ...prev, verified: true }));
      toast.success(result.message || "Email verified successfully!");

      // If the backend returns a token, redirect to dashboard immediately
      if (result.accessToken) {
        // No need for timeout, redirect immediately
        navigate("/dashboard");
      } else {
        // If no token, redirect to login after showing success message
        setTimeout(() => {
          navigate("/login");
        }, 2000);
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to verify OTP",
      }));
      toast.error(
        error instanceof Error ? error.message : "Failed to verify OTP"
      );
    }
  };

  const handleResendOTP = async () => {
    if (!state.userId || !state.email) {
      setState((prev) => ({
        ...prev,
        error: "Missing user information. Please try signing up again.",
      }));
      return;
    }

    try {
      // Use the auth hook's resendOTP method
      const result = await resendOTP(state.userId, state.email);
      toast.success(result.message || "OTP resent successfully!");
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to resend OTP",
      }));
      toast.error(
        error instanceof Error ? error.message : "Failed to resend OTP"
      );
    }
  };

  if (state.verified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-400 px-4">
        <div className="card rounded-xl w-full max-w-md p-8 text-center">
          <div className="flex items-center justify-center mb-8">
            <Box className="w-10 h-10 text-brand-300" strokeWidth={1.5} />
            <span className="ml-2 text-2xl font-light tracking-wider bg-gradient-to-r from-brand-300 to-brand-100 text-transparent bg-clip-text">
              morpho
            </span>
          </div>

          <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-400 mb-6">
            <h2 className="text-xl font-medium mb-2">Email Verified!</h2>
            <p>Your account has been successfully verified.</p>
            <p className="mt-2 text-sm">Redirecting you to the dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-400 px-4">
      <div className="card rounded-xl w-full max-w-md p-8">
        <div className="flex items-center justify-center mb-8">
          <Box className="w-10 h-10 text-brand-300" strokeWidth={1.5} />
          <span className="ml-2 text-2xl font-light tracking-wider bg-gradient-to-r from-brand-300 to-brand-100 text-transparent bg-clip-text">
            morpho
          </span>
        </div>

        <h1 className="text-2xl font-light text-center mb-6">
          Verify Your Email
        </h1>

        {state.error && (
          <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm mb-4">
            {state.error}
          </div>
        )}

        <div className="text-center mb-6">
          <p className="text-gray-300">We've sent a verification code to:</p>
          <p className="text-brand-300 font-medium mt-1">
            {state.email || "your email address"}
          </p>
        </div>

        <form onSubmit={handleVerifyOTP} className="space-y-6">
          <div>
            <label
              htmlFor="otp"
              className="block text-sm font-light text-gray-400 mb-2"
            >
              Enter 4-Digit Code
            </label>
            <input
              id="otp"
              type="text"
              value={state.otp}
              onChange={handleOTPChange}
              className="input text-center text-2xl tracking-widest"
              placeholder="0000"
              maxLength={4}
              required
              autoFocus
            />
          </div>

          <button
            type="submit"
            disabled={state.loading || state.otp.length !== 4}
            className="btn btn-primary w-full flex items-center justify-center"
          >
            {state.loading ? (
              <span className="inline-block w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
            ) : (
              "Verify Email"
            )}
          </button>
        </form>

        <div className="mt-6 flex flex-col items-center justify-center gap-4">
          <button
            onClick={handleResendOTP}
            disabled={state.loading}
            className="text-brand-300 hover:text-brand-200 text-sm transition-colors"
          >
            Didn't receive the code? Resend
          </button>

          <Link
            to="/login"
            className="text-gray-400 hover:text-gray-300 text-sm flex items-center gap-1 transition-colors"
          >
            <ArrowLeft size={14} />
            Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
};

export default VerifyOTP;
