const wooCommerce = require("../utils/wooCommerce");
const GlbModel = require("../models/GlbModel");
const projectSchema = require("../models/Project");
const assetLibraryModel = require("../models/AssetLibrary");

exports.getAllProducts = async (req, res, next) => {
  const limit = parseInt(req.query.limit, 10) || 5;
  const { data } = await req.wooCommerceClient.get("products", {
    per_page: limit,
  });
  res.status(200).json({ success: true, data });
};

exports.getStatistics = async (req, res, next) => {
  // Fetch all orders
  const { data: orders } = await req.wooCommerceClient.get("orders", {
    per_page: 100, // Adjust as needed for WooCommerce limits
  });

  const productStats = {};
  let totalOrders = orders.length;

  orders.forEach((order) => {
    order.line_items.forEach((item) => {
      const { product_id, name, quantity, price } = item;
      if (!productStats[product_id]) {
        productStats[product_id] = {
          productId: product_id,
          name,
          totalSold: 0,
          revenue: 0,
        };
      }
      productStats[product_id].totalSold += quantity;
      productStats[product_id].revenue += quantity * price;
    });
  });

  // Convert object to array and sort by totalSold in descending order
  const topProducts = Object.values(productStats).sort(
    (a, b) => b.totalSold - a.totalSold,
  );

  res.status(200).json({
    success: true,
    data: {
      totalOrders,
      topProducts,
    },
  });
};

exports.saveAllProducts = async (req, res, next) => {
  const userId = req.user;

  // Get the user's project
  const project = await projectSchema.findOne({ client: userId }).lean();
  if (!project) {
    return res
      .status(404)
      .json({ success: false, message: "Project not found" });
  }
  const assetLibrary = await assetLibraryModel.findOne({
    project: project._id,
  });
  const assetLibraryId = assetLibrary._id;
  const projectId = project._id;

  const allProducts = [];
  let page = 1;
  const perPage = 100;
  let fetchedProducts;

  // Fetch all products with pagination
  do {
    const { data } = await req.wooCommerceClient.get("products", {
      params: { page, per_page: perPage },
    });
    fetchedProducts = data;
    allProducts.push(...fetchedProducts);
    page++;
  } while (fetchedProducts.length === perPage);

  // Arrays to track results
  const addedProducts = [];
  const skippedProducts = [];

  // Map WooCommerce data to match your GlbModel structure
  for (const product of allProducts) {
    try {
      // Validate and map product data
      if (!product.images || !product.images.length) {
        skippedProducts.push({
          name: product.name,
          id: product.id,
          reason: "Missing image URL.",
        });
        continue;
      }

      // Clean the product description
      const cleanDescription = product.description
        ? product.description.replace(/<\/?[^>]+(>|$)/g, "").trim()
        : "";

      const mappedProduct = {
        name: product.name,
        description: cleanDescription, // Cleaned description
        url: product.permalink || "",
        multiUrls: product.images.map((img) => img.src),
        imageUrl: product.images[0]?.src || "",
        price: parseFloat(product.price),
        sku: product.sku || "",
        sizes: [],
        width: [],
        published: product.status === "publish",
        project: null,
        category: null,
        collectionId: null,
        asset_library: null,
        hdr: {
          url: "",
          level: null,
        },
        customizations: [],
        asset_library: assetLibraryId,
        variants: product.variations?.map((variant) => ({
          title: variant.attributes.map((attr) => attr.option).join("/"),
          materials: [],
          options: variant.attributes.map((attr) => attr.option),
          price: parseFloat(variant.price),
          sku: variant.sku,
        })),
        integrationKey: product.id.toString(),
        usdzUrl: "",
      };

      const existingProduct = await GlbModel.findOne({
        integrationKey: mappedProduct.integrationKey,
      });
      if (existingProduct) {
        continue;
      }

      const newProduct = await new GlbModel(mappedProduct).save();
      addedProducts.push({
        name: newProduct.name,
        description: newProduct.description,
        imageUrl: newProduct.imageUrl,
        price: newProduct.price,
        sku: newProduct.sku,
        published: newProduct.published,
        integrationKey: newProduct.integrationKey,
        project: projectId,
      });
    } catch (err) {
      skippedProducts.push({
        name: product.name,
        id: product.id,
        reason: err.message,
      });
    }
  }
  res.status(200).json({
    success: true,
    message: `${addedProducts.length} new product(s) added.`,
    data: addedProducts,
    skippedProducts,
  });
};
