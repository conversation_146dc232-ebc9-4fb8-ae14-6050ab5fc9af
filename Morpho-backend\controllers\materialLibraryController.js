const materialLibraryModel = require("../models/materialLibrary");
const resHandle = require("../utils/responseHandle");

class Controller {
  createMaterialLibrary = async (req, res) => {
    try {
      const materialToBeCreated = await materialLibraryModel.create(req.body);
      resHandle.handleData(
        res,
        200,
        "Material Library Created Successfully",
        true,
        materialToBeCreated,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
  getAllMaterialsLibrary = async (req, res) => {
    try {
      const materialsLibraryToBeFetched = await materialLibraryModel.find();
      if (!materialsLibraryToBeFetched)
        return resHandle.handleError(res, 404, `Material Library Not Found`);
      resHandle.handleData(
        res,
        200,
        "Materials Retrieved Successfully",
        true,
        materialsLibraryToBeFetched,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
  getMaterialLibraryById = async (req, res) => {
    try {
      const { _id } = req.body;
      let materialLibraryToBeFetched = await materialLibraryModel.findById(_id);
      if (!materialLibraryToBeFetched)
        return resHandle.handleError(res, 404, `Material Library Not Found`);
      resHandle.handleData(
        res,
        200,
        "Material Library Retrieved Successfully",
        true,
        materialLibraryToBeFetched,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
  getMaterialLibraryByProjectId = async (req, res) => {
    try {
      const { project } = req.body;
      let materialLibraryToBeFetched = await materialLibraryModel.find({
        project,
      });
      if (!materialLibraryToBeFetched)
        return resHandle.handleError(res, 404, `Material Library Not Found`);
      resHandle.handleData(
        req,
        200,
        "Material Library Retrieved Successfully",
        true,
        materialLibraryToBeFetched,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
  getMaterialsByProjectId = async (req, res) => {
    try {
      const { project } = req.body;
      let materialsToBeFetched = await materialLibraryModel
        .findOne({
          project: project,
        })
        .populate("materials");
      if (!materialsToBeFetched)
        return resHandle.handleError(res, 404, `Materials Not Found`);
      resHandle.handleData(
        req,
        200,
        "Material Retrieved Successfully",
        true,
        materialsToBeFetched,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
  deleteMaterialLibraryById = async (req, res) => {
    try {
      const { _id } = req.body;
      const materialLibraryToBeDeleted =
        await materialLibraryModel.findByIdAndDelete(_id);
      if (!materialLibraryToBeDeleted)
        return resHandle.handleError(res, 404, `Material Library Not Found`);
      resHandle.handleData(
        req,
        200,
        "Material Library Deleted Successfully",
        true,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
  updateMaterialLibraryById = async (req, res) => {
    try {
      const { _id } = req.body;
      let materialToBeUpdated = await materialLibraryModel.findByIdAndUpdated(
        {
          _id: _id,
        },
        { ...req.body },
        { new: true },
      );
      if (!materialToBeUpdated)
        return resHandle.handleError(res, 404, `Material Not Found`);
      resHandle.handleData(
        req,
        200,
        "Material Updated Successfully",
        true,
        materialToBeUpdated,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${error}`);
    }
  };
}

const controller = new Controller();
module.exports = controller;
