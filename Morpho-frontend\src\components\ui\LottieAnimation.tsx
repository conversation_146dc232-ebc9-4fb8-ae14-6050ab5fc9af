import React, { useEffect, useRef, useState } from 'react';
import Lottie, { LottieRefCurrentProps } from 'lottie-react';
import { useInView } from '../../hooks/useInView';

interface LottieAnimationProps {
  animationData: any;
  loop?: boolean;
  autoplay?: boolean;
  className?: string;
  style?: React.CSSProperties;
  playOnHover?: boolean;
  playOnView?: boolean;
  onComplete?: () => void;
  segmentFrom?: number;
  segmentTo?: number;
  speed?: number;
}

export const LottieAnimation: React.FC<LottieAnimationProps> = ({
  animationData,
  loop = true,
  autoplay = true,
  className = '',
  style = {},
  playOnHover = false,
  playOnView = false,
  onComplete,
  segmentFrom,
  segmentTo,
  speed = 1,
}) => {
  const lottieRef = useRef<LottieRefCurrentProps>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { threshold: 0.1 });
  const [isHovered, setIsHovered] = useState(false);
  const wasInView = useRef(false);

  // Performance optimization - only load animation when in viewport
  const shouldLoad = playOnView ? isInView || wasInView.current : true;

  // Set up segments if specified
  useEffect(() => {
    if (!shouldLoad || !lottieRef.current) return;
    
    if (segmentFrom !== undefined && segmentTo !== undefined) {
      lottieRef.current.setSpeed(speed);
      lottieRef.current.playSegments([segmentFrom, segmentTo], true);
    }
  }, [segmentFrom, segmentTo, speed, shouldLoad]);

  // Handle playOnView
  useEffect(() => {
    if (!shouldLoad || !lottieRef.current) return;

    if (playOnView) {
      if (isInView) {
        wasInView.current = true;
        lottieRef.current.play();
      } else {
        lottieRef.current.pause();
      }
    }
  }, [isInView, playOnView, shouldLoad]);

  // Handle playOnHover
  useEffect(() => {
    if (!shouldLoad || !lottieRef.current || !playOnHover) return;

    if (isHovered) {
      lottieRef.current.play();
    } else {
      lottieRef.current.pause();
    }
  }, [isHovered, playOnHover, shouldLoad]);

  // Optimize Lottie render settings
  const rendererSettings = {
    preserveAspectRatio: 'xMidYMid slice',
    progressiveLoad: true,
    hideOnTransparent: true,
    className: 'lottie-svg'
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div 
      ref={containerRef}
      className={`lottie-container ${className}`}
      style={style}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {shouldLoad && (
        <Lottie
          lottieRef={lottieRef}
          animationData={animationData}
          loop={loop}
          autoplay={playOnHover || playOnView ? false : autoplay}
          onComplete={onComplete}
          style={{ width: '100%', height: '100%' }}
          rendererSettings={rendererSettings}
        />
      )}
    </div>
  );
};