const router = require("express").Router();
const controller = require("../../controllers/shopifyController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
const setShopifyClient = require("../../utils/shopify");
const verifySubscription = require("../../middleware/verifySubscription");
const verifyJWT = require("../../middleware/verifyJWT");

router.post(
  "/products",
  setShopifyClient,
  asyncErrorHandler(controller.getAllProducts)
);

router.post(
  "/products-for-selection",
  verifyJWT,
  verifySubscription,
  setShopifyClient,
  asyncError<PERSON>andler(controller.getProductsForSelection)
);
router.get("/test", asyncErrorHandler(controller.getProductTest));
router.get(
  "/statistics",
  setShopifyClient,
  asyncErrorHandler(controller.getStatistics)
);
router.post(
  "/save-products",
  verifyJWT,
  verifySubscription,
  setShopifyClient,
  asyncError<PERSON><PERSON><PERSON>(controller.saveAllProducts)
);

router.post(
  "/sync-to-shopify",
  verifyJWT,
  setShopifyClient,
  asyncErrorHandler(controller.syncProductsToShopify)
);

module.exports = router;
