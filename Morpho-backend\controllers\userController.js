const userModel = require("../models/User");
const user = require("../models/User");
const jwt = require("jsonwebtoken");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");

class Controller {
  getAllUsers = async (req, res) => {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await userModel.countDocuments();

    // Check if users exist
    if (totalCount === 0) {
      throw new expressError(
        "No users found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch users with pagination and populate role
    const allUsers = await user
      .find({})
      .skip(skip)
      .limit(limit)
      .populate("role");

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "Users retrieved successfully",
      true,
      allUsers,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };

  //GetAllUsersByRole
  getAllUsersByRole = async (req, res) => {
    // Parse pagination parameters
    const { role } = req.body;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build aggregation pipeline
    let pipeline = [
      {
        $lookup: {
          from: "roles",
          localField: "role",
          foreignField: "_id",
          as: "role",
        },
      },
      {
        $unwind: {
          path: "$role",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ];

    // Add role filter if provided
    if (role) {
      pipeline.push({
        $match: {
          "role.role": role,
        },
      });
    }

    // Get the total count
    let totalCount = await user.aggregate([
      ...pipeline,
      { $count: "totalCount" },
    ]);

    totalCount = totalCount.length > 0 ? totalCount[0].totalCount : 0;

    // Check if users exist
    if (totalCount === 0) {
      throw new expressError(
        role
          ? `No users found with role: ${role}`
          : "No users found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Add pagination to pipeline
    pipeline.push({ $skip: skip }, { $limit: limit });

    // Execute the query
    const usersToBeFound = await user.aggregate(pipeline).exec();

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "Users retrieved successfully",
      true,
      usersToBeFound,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };

  //getUsersByRole
  getUsersByRole = async (req, res) => {
    const { role } = req.body;
    let users;

    // Fetch users based on role
    if (role) {
      users = await user.aggregate([
        {
          $lookup: {
            from: "roles",
            localField: "role",
            foreignField: "_id",
            as: "role",
          },
        },
        {
          $unwind: "$role",
        },
        {
          $match: {
            "role.role": role,
          },
        },
        {
          $sort: {
            createdAt: -1,
          },
        },
      ]);
    } else {
      users = await user.find();
    }

    // Check if users exist
    if (!users || users.length === 0) {
      throw new expressError(
        role
          ? `No users found with role: ${role}`
          : "No users found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Users retrieved successfully",
      true,
      users
    );
  };

  getUserByIdWithRole = async (req, res) => {
    // Validate required fields
    if (!req.body.id) {
      throw new expressError(
        "User ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!req.body.id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid user ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find user by ID and populate role
    const userFound = await user.findOne({ _id: req.body.id }).populate("role");

    // Check if user exists
    if (!userFound) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "User retrieved successfully",
      true,
      userFound
    );
  };

  getUserDetailsUsingCookie = async (req, res) => {
    // Check if cookie exists
    const cookie = req.headers.cookies;
    if (!cookie) {
      throw new expressError(
        "Authentication required",
        403,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Extract and decode JWT token
    const jwtToken = cookie.split("=")[1];
    const decodedToken = jwt.decode(jwtToken);

    // Validate token
    if (!decodedToken || !decodedToken.userId) {
      throw new expressError(
        "Invalid authentication token",
        401,
        expressError.CODES.INVALID_CREDENTIALS
      );
    }

    // Find user by ID from token
    const userDetails = await user
      .findById(decodedToken.userId)
      .populate("role");

    // Check if user exists
    if (!userDetails) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "User retrieved successfully",
      true,
      userDetails
    );
  };

  updateUserInfo = async (req, res) => {
    // Get user details from request body
    const userDetails = req.body;

    // Validate user ID
    if (!req.user) {
      throw new expressError(
        "User ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Update user
    const userTest = await user.findOneAndUpdate(
      { _id: req.user },
      { ...userDetails }
    );

    // Check if user exists
    if (!userTest) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch updated user
    const userUpdated = await user.findOne({ _id: req.user });

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "User updated successfully",
      true,
      userUpdated
    );
  };

  deleteUserById = async (req, res) => {
    // Get user ID from request body
    const { id } = req.body;

    // Validate user ID
    if (!id) {
      throw new expressError(
        "User ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid user ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete user
    const deletedUser = await user.findByIdAndDelete({ _id: id });

    // Check if user exists
    if (!deletedUser) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(res, 200, "User deleted successfully", true);
  };

  SoftdeleteUserById = async (req, res) => {
    // Get user ID from request body
    const { _id } = req.body;

    // Validate user ID
    if (!_id) {
      throw new expressError(
        "User ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!_id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid user ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Update user to mark as deleted
    await user.findByIdAndUpdate(_id, {
      isDeleted: true,
    });

    // Fetch updated user
    const userUpdated = await user.findById({ _id });

    // Check if user exists
    if (!userUpdated) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "User has been soft deleted successfully",
      true,
      userUpdated
    );
  };
}

const controller = new Controller();

module.exports = controller;
