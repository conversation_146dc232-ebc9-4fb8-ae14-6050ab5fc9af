import { storage } from "../utils/storage";
import { API_URL } from "../config/apiConfig";

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  interval: "month" | "year";
  features: string[];
  paymentLink: string | null;
  productLimit: number;
}

export interface SubscriptionStatus {
  status:
    | "inactive"
    | "active"
    | "past_due"
    | "canceled"
    | "canceling"
    | "none";
  plan: "free" | "basic" | "pro" | "enterprise" | null;
  subscriptionId: string | null;
  productLimit: number;
  productCount?: number;
}

export const subscriptionService = {
  /**
   * Update subscription (for testing)
   * @param plan The plan to update to
   * @returns Result of update
   */
  /**
   * Directly update subscription plan
   * @param plan The plan to update to
   * @returns Result of update
   */
  async directUpdatePlan(plan: string): Promise<{
    success: boolean;
    message?: string;
    data?: SubscriptionStatus;
    error?: string;
  }> {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(
        `${API_URL}/subscription/direct-update-plan`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: JSON.stringify({ plan }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();

      // Store subscription status in localStorage for quick access
      if (data.success && data.data) {
        storage.setSubscriptionStatus(data.data);
      }

      return {
        success: true,
        message: data.message,
        data: data.data,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  },

  async simulateWebhook(plan: string): Promise<{
    success: boolean;
    message?: string;
    data?: SubscriptionStatus;
    error?: string;
  }> {
    const token = storage.getToken();
    const userId = storage.getUserId(); // Assuming you have a method to get the user ID

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(`${API_URL}/subscription/simulate-webhook`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ userId, plan }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();

      // Immediately fetch the updated subscription status
      const statusResponse = await this.getUserSubscription();

      return {
        success: true,
        message: data.message,
        data: statusResponse.data,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  },

  async updateSubscription(plan: string): Promise<{
    success: boolean;
    message?: string;
    data?: SubscriptionStatus;
    error?: string;
  }> {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(`${API_URL}/subscription/update`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ plan }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();

      // Immediately fetch the updated subscription status
      const statusResponse = await this.getUserSubscription();

      return {
        success: true,
        message: data.message,
        data: statusResponse.data,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  },
  /**
   * Get available subscription plans
   * @returns List of subscription plans
   */
  async getSubscriptionPlans(): Promise<{
    success: boolean;
    data?: SubscriptionPlan[];
    error?: string;
  }> {
    const token = storage.getToken();

    if (!token) {
      console.error("No authentication token found");
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(`${API_URL}/subscription/plans`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();

      // Debug: Check if the payment links are valid

      return {
        success: true,
        data: data.data,
      };
    } catch (error) {
      const isNetworkError =
        error instanceof TypeError &&
        (error.message === "Failed to fetch" ||
          error.message.includes("NetworkError"));

      return {
        success: false,
        error: isNetworkError
          ? "Unable to connect to the server. Please check your internet connection."
          : error instanceof Error
          ? error.message
          : "An unexpected error occurred while fetching subscription plans",
      };
    }
  },

  /**
   * Get Stripe publishable key
   * @returns Stripe publishable key
   */
  async getStripePublishableKey(): Promise<{
    success: boolean;
    publishableKey?: string;
    error?: string;
  }> {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(`${API_URL}/subscription/config`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        publishableKey: data.publishableKey,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  },

  /**
   * Get current user subscription status
   * @returns User subscription status
   */
  async getUserSubscription(): Promise<{
    success: boolean;
    data?: SubscriptionStatus;
    error?: string;
  }> {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(`${API_URL}/subscription/status`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        // Add cache: 'no-cache' to ensure we always get fresh data
        cache: "no-cache",
      });

      if (!response.ok) {
        console.error(`Subscription status API error: ${response.status}`);
        try {
          const errorData = await response.json();
          console.error("Error details:", errorData);
          return {
            success: false,
            error:
              errorData.message || `HTTP error! status: ${response.status}`,
          };
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          return {
            success: false,
            error: `HTTP error! status: ${response.status}`,
          };
        }
      }

      const data = await response.json();

      // Store subscription status in localStorage for quick access
      if (data.success && data.data) {
        storage.setSubscriptionStatus(data.data);
      }

      return {
        success: true,
        data: data.data,
      };
    } catch (error) {
      console.error("Error in getUserSubscription:", error);

      // If we have cached subscription data, return it as a fallback
      const cachedStatus = storage.getSubscriptionStatus();
      if (cachedStatus) {
        return {
          success: true,
          data: cachedStatus,
        };
      }

      // If no cached data, create a default free tier status
      const defaultStatus: SubscriptionStatus = {
        status: "inactive",
        plan: "free",
        subscriptionId: null,
        productLimit: 1,
        productCount: 0,
      };

      // Store this default status
      storage.setSubscriptionStatus(defaultStatus);

      return {
        success: true,
        data: defaultStatus,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  },

  /**
   * Cancel current subscription
   * @returns Result of cancellation
   */
  /**
   * Manually trigger a webhook event for a specific subscription
   * @param subscriptionId The subscription ID to trigger the webhook for
   * @param eventType Optional event type (defaults to invoice.payment_succeeded)
   * @returns Result of the manual webhook
   */
  async manualWebhook(
    subscriptionId: string,
    eventType?: string
  ): Promise<{
    success: boolean;
    message?: string;
    data?: SubscriptionStatus;
    error?: string;
  }> {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const response = await fetch(`${API_URL}/subscription/manual-webhook`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          subscriptionId,
          event_type: eventType || "invoice.payment_succeeded",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();

      // Store subscription status in localStorage for quick access
      if (data.success && data.data) {
        storage.setSubscriptionStatus(data.data);
      }

      return {
        success: true,
        message: data.message,
        data: data.data,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  },

  async cancelSubscription(): Promise<{
    success: boolean;
    message?: string;
    error?: string;
    data?: {
      status: string;
      periodEnd?: string;
    };
  }> {
    const token = storage.getToken();

    if (!token) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    try {
      const baseUrl = API_URL;

      const response = await fetch(`${baseUrl}/subscription/cancel`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.message || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        message: data.message,
        data: data.data,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
  },
};
