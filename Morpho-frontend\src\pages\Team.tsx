import React from 'react';
import { Search, UserPlus } from 'lucide-react';
import TeamMemberCard from '../components/TeamMemberCard';

const Team = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Lead 3D Designer',
      email: 'sarah.and<PERSON>@modular.cx',
      phone: '+****************',
      location: 'San Francisco, CA',
      avatarUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80'
    },
    {
      name: '<PERSON>',
      role: '3D Modeling Specialist',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&q=80'
    },
    {
      name: '<PERSON>',
      role: 'Project Manager',
      email: 'emily.rod<PERSON><PERSON><PERSON>@modular.cx',
      phone: '+****************',
      location: 'Austin, TX',
      avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&q=80'
    }
  ];

  return (
    <div>
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-light tracking-wide text-gray-100">Team</h1>
        <button className="flex items-center px-4 py-2 rounded-lg bg-indigo-500 text-white hover:bg-indigo-600 transition-colors">
          <UserPlus size={18} strokeWidth={1.5} className="mr-2" />
          <span className="text-sm font-light tracking-wide">Add Member</span>
        </button>
      </div>

      <div className="mt-8">
        <div className="relative">
          <Search size={18} strokeWidth={1.5} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search team members..."
            className="w-full pl-10 pr-4 py-2 rounded-lg bg-dark-300 border border-gray-700/50 text-gray-100 placeholder-gray-500 focus:outline-none focus:border-indigo-500/50 transition-colors"
          />
        </div>
      </div>

      <div className="mt-8 grid gap-6 grid-cols-1 lg:grid-cols-2">
        {teamMembers.map((member) => (
          <TeamMemberCard key={member.email} {...member} />
        ))}
      </div>
    </div>
  );
};

export default Team;