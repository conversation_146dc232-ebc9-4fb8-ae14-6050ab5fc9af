import React, { ButtonHTMLAttributes, useState } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  intent?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
  as?: React.ElementType;
  href?: string;
}

export const Button: React.FC<ButtonProps> = ({
  intent = 'primary',
  size = 'medium',
  as: Component = 'button',
  className = '',
  children,
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const baseStyles = "inline-flex items-center justify-center rounded-md font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 overflow-hidden relative";
  
  // Enhanced neon glow styles
  const intentStyles = {
    primary: "bg-blue-400 hover:bg-blue-500 text-white border border-blue-400 shadow-neon-primary hover:shadow-neon-primary-hover",
    secondary: "bg-transparent border border-blue-400/40 text-white hover:border-blue-400 hover:bg-white/5 shadow-neon-secondary hover:shadow-neon-secondary-hover"
  };
  
  const sizeStyles = {
    small: "text-sm px-3 py-1.5",
    medium: "text-base px-4 py-2",
    large: "text-lg px-6 py-3"
  };
  
  // Apply neon text glow effect to primary buttons
  const textGlowStyles = intent === 'primary' ? 'text-shadow-neon' : '';
  
  const combinedClassName = `${baseStyles} ${intentStyles[intent]} ${sizeStyles[size]} ${textGlowStyles} ${className} ${isHovered ? 'button-pulse' : ''}`;

  // Handle hover state
  const handleMouseEnter = (e: React.MouseEvent) => {
    setIsHovered(true);
    if (props.onMouseEnter) props.onMouseEnter(e);
  };

  const handleMouseLeave = (e: React.MouseEvent) => {
    setIsHovered(false);
    setIsPressed(false);
    if (props.onMouseLeave) props.onMouseLeave(e);
  };

  // Handle pressed state for pulse effect
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsPressed(true);
    if (props.onMouseDown) props.onMouseDown(e);
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    setIsPressed(false);
    if (props.onMouseUp) props.onMouseUp(e);
  };
  
  return (
    <Component 
      className={combinedClassName} 
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      {...(Component === 'a' ? { href: props.href } : {})}
      {...props}
    >
      {/* Neon glow overlay */}
      <span 
        className={`absolute inset-0 rounded-md transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`} 
        style={{ 
          boxShadow: intent === 'primary' 
            ? '0 0 15px #0066CC, 0 0 25px rgba(0, 102, 204, 0.6)' 
            : '0 0 10px #0066CC, 0 0 15px rgba(0, 102, 204, 0.4)',
          zIndex: 0
        }}
      />
      
      {/* Neon pulse effect when pressed */}
      <span 
        className={`absolute inset-0 bg-blue-400/20 rounded-md transition-transform duration-500 origin-center ${
          isPressed ? 'scale-[2] opacity-30' : 'scale-0 opacity-0'
        }`}
      />
      
      {/* Button content */}
      <span className="relative z-10">{children}</span>
    </Component>
  );
};