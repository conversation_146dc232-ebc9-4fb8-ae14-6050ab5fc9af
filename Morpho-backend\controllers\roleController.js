const role = require("../models/Role");
const resHandle = require("../utils/responseHandle");
class Controller {
  createRole = async (req, res) => {
    try {
      let newRole = await role.create(req.body).then((respose) => {
        resHandle.handleData(res, 200, "Role Created", true, respose);
      });
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };

  getAllRoles = async (req, res) => {
    try {
      let allRoles = await role.find({});
      resHandle.handleData(
        res,
        200,
        "Roles found Successfully",
        true,
        allRoles,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };

  getRolesToBeUsed = async (req, res) => {
    try {
      const client = await role.findOne({ role: "Client" });
      const designer = await role.findOne({ role: "Designer" });
      return res.status(200).json({
        message: "Roles Retrieved Successfully",
        success: true,
        data: { client, designer },
      });
    } catch (error) {
      return res.status(500).json({
        message: `Error ${error}`,
        success: false,
      });
    }
  };

  getRoleForSignup = async (req, res) => {
    try {
      let fetchedRole = await role.findOne({ role: req.body.role });
      if (!fetchedRole)
        return resHandle.handleError(res, 404, "Role Not Found");
      resHandle.handleData(
        res,
        200,
        "Role Retrieved Successfully",
        true,
        fetchedRole,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };

  deleteRole = async (req, res) => {
    try {
      const { id } = req.body;
      let roleToBeDeleted = await role.findByIdAndDelete(id);
      if (!roleToBeDeleted)
        return resHandle.handleError(res, 404, "Role Not Found");
      resHandle.handleData(res, 200, "Role Deleted Successfully", true);
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  softDeleteRole = async (req, res) => {
    try {
      const { _id } = req.body;
      let roleToBeFetched = await role.findByIdAndUpdate(
        { _id },
        { isDeleted: true },
      );
      let roleUpdated = await role.findById({ _id });
      if (!roleToBeFetched)
        return resHandle.handleError(res, 404, "Role is not Found");

      resHandle.handleData(
        res,
        200,
        "Soft Deleted Applied on this role",
        true,
        roleUpdated,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  updateRoleById = async (req, res) => {
    try {
      const { _id } = req.body;
      const bodyInfo = req.body;
      let roleToBeRetrieved = await role.findByIdAndUpdate(
        { _id },
        { ...bodyInfo },
      );
      let roleUpdated = await role.findById({ _id });
      if (!roleToBeRetrieved)
        return resHandle.handleError(res, 404, "Role Not Found");

      resHandle.handleData(
        res,
        200,
        "Role Updated Successfully",
        true,
        roleUpdated,
      );
    } catch (error) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
}

const controller = new Controller();
module.exports = controller;
