const User = require("../models/User");

/**
 * Increment a user's product count and check if they've reached their limit
 * 
 * @param {string} userId - The user's ID
 * @returns {Object} Object containing success status, updated count, and limit
 */
const incrementProductCount = async (userId) => {
  try {
    // Find the user and get their current product count and limit
    const user = await User.findById(userId).select("productCount productLimit");
    
    if (!user) {
      return {
        success: false,
        message: "User not found"
      };
    }
    
    // Check if the user has reached their product limit
    if (user.productCount >= user.productLimit) {
      return {
        success: false,
        message: "Product limit reached",
        productCount: user.productCount,
        productLimit: user.productLimit
      };
    }
    
    // Increment the product count
    user.productCount += 1;
    await user.save();
    
    return {
      success: true,
      message: "Product count incremented",
      productCount: user.productCount,
      productLimit: user.productLimit
    };
  } catch (error) {
    console.error("Error incrementing product count:", error);
    return {
      success: false,
      message: "Error incrementing product count",
      error: error.message
    };
  }
};

/**
 * Decrement a user's product count
 * 
 * @param {string} userId - The user's ID
 * @returns {Object} Object containing success status and updated count
 */
const decrementProductCount = async (userId) => {
  try {
    // Find the user and get their current product count
    const user = await User.findById(userId).select("productCount productLimit");
    
    if (!user) {
      return {
        success: false,
        message: "User not found"
      };
    }
    
    // Decrement the product count, but don't go below 0
    if (user.productCount > 0) {
      user.productCount -= 1;
      await user.save();
    }
    
    return {
      success: true,
      message: "Product count decremented",
      productCount: user.productCount,
      productLimit: user.productLimit
    };
  } catch (error) {
    console.error("Error decrementing product count:", error);
    return {
      success: false,
      message: "Error decrementing product count",
      error: error.message
    };
  }
};

module.exports = {
  incrementProductCount,
  decrementProductCount
};
