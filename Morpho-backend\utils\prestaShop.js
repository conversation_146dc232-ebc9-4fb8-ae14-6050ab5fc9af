const axios = require('axios');

const setPrestashopClient = (req, res, next) => {
  const { baseUrl, apiKey } = req.body;

  if (!baseUrl || !apiKey) {
    return res.status(400).json({ success: false, message: 'Missing PrestaShop credentials' });
  }

  // Attach PrestaShop client to the request object
  req.prestashopClient = async (endpoint) => {
    try {
      const response = await axios.get(`${baseUrl}${endpoint}`, {
        headers: {
          Authorization: `Basic ${Buffer.from(apiKey + ':').toString('base64')}`,
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  };

  next();
};

module.exports = setPrestashopClient;
