import React, { useRef, useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { GlassContainer } from '../ui/GlassContainer';
import { useInView } from '../../hooks/useInView';

const faqs = [
  {
    id: 'difference',
    question: 'How is <PERSON>rp<PERSON> different from other 3D model viewers and 3D websites?',
    answer: 'Morpho is the only 3D product viewer built exclusively for Shopify. While other 3D model viewers are generic tools with basic e-commerce plugins, our Shopify 3D product viewer was designed from day one to integrate seamlessly with Shopify\'s catalog structure, variant system, and theme architecture.'
  },
  {
    id: 'experience',
    question: 'Do I need 3D modeling experience to use Morpho\'s 3D product visualization?',
    answer: 'Absolutely not. Unlike complex 3D modeling websites, Morpho was built for Shopify merchants, not 3D experts. Our iOS app handles all the technical aspects of 3D product modeling automatically, or you can directly upload existing 3D models if you already have them.'
  },
  {
    id: 'speed',
    question: 'Will adding 3D products slow down my Shopify online store?',
    answer: 'No, unlike generic 3D websites that can dramatically increase page load times, Morpho automatically optimizes all 3D models specifically for e-commerce performance. Our testing shows 3D product models load in under 2 seconds with negligible impact on your Lighthouse scores.'
  },
  {
    id: 'variants',
    question: 'How does Morpho\'s 3D product viewer handle Shopify product variants?',
    answer: 'Unlike other 3D model viewers that require separate models for each variant, Morpho automatically maps your 3D models to your existing Shopify variant structure, allowing customers to switch between colors, sizes, or styles in the 3D product viewer without loading a new model.'
  },
  {
    id: 'ios',
    question: 'What if I don\'t have an iOS device for 3D product scanning?',
    answer: 'You can directly upload existing 3D product models to the platform. Alternatively, we offer a 3D product rendering service specifically for Shopify merchants. Simply ship your products to our studio, and we\'ll create professional 3D models optimized for your online store.'
  },
  {
    id: 'theme',
    question: 'Will Morpho\'s 3D product viewer work with my Shopify theme?',
    answer: 'Yes! Our 3D model viewer is compatible with all Shopify themes, including custom themes. The 3D product viewer automatically adapts to your theme\'s design and is responsive for all devices, ensuring your 3D products look great on any screen size.'
  }
];

export const FAQ: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  const [openItem, setOpenItem] = useState<string | null>(faqs[0].id);

  const toggleItem = (id: string) => {
    setOpenItem(openItem === id ? null : id);
  };

  return (
    <section ref={sectionRef} id="faq" className="py-24 relative">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className={`text-3xl md:text-4xl font-bold text-white mb-6 transition-all duration-700 ${
            isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            Frequently Asked Questions About Our Shopify 3D Product Viewer
          </h2>
        </div>

        <div className="max-w-3xl mx-auto">
          <GlassContainer className="divide-y divide-gray-800 border-0 border-l-0 border-r-0">
            {faqs.map((faq, index) => (
              <div 
                key={faq.id}
                className={`transition-all duration-700 ${
                  isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${150 + index * 100}ms` }}
              >
                <button
                  className="w-full py-6 flex justify-between items-center text-left focus:outline-none focus:ring-1 focus:ring-blue-400 focus:ring-opacity-50 rounded-md group"
                  onClick={() => toggleItem(faq.id)}
                  aria-expanded={openItem === faq.id}
                >
                  <h3 className="text-xl font-medium text-white group-hover:text-blue-300 transition-colors">{faq.question}</h3>
                  <ChevronDown 
                    className={`w-5 h-5 text-blue-400 transition-transform duration-300 ease-out ${
                      openItem === faq.id ? 'transform rotate-180' : ''
                    }`} 
                  />
                </button>
                <div 
                  className={`overflow-hidden transition-all duration-300 ease-in-out ${
                    openItem === faq.id ? 'max-h-96 pb-6 animate-fade-in' : 'max-h-0'
                  }`}
                >
                  <p className="text-gray-300">{faq.answer}</p>
                </div>
              </div>
            ))}
          </GlassContainer>
        </div>
      </div>
    </section>
  );
};