import React, { useRef } from "react";
import { Check } from "lucide-react";
import { GlassCard } from "../ui/GlassCard";
import { Button } from "../ui/Button";
import { useInView } from "../../hooks/useInView";

const plans = [
  {
    id: "basic",
    name: "Basic",
    price: 49,
    billing: "month",
    yearlyPrice: 499,
    description: "Perfect for small Shopify stores with a moderate catalog",
    features: [
      "Up to 25 products in 3D",
      "Core features",
      "Free integration with e-commerce platforms",
      "Basic analytics",
      "Standard support",
      "10% off model services",
      "Encrypted data",
      "Model compression",
    ],
    highlighted: false,
  },
  {
    id: "pro",
    name: "Pro",
    price: 179,
    billing: "month",
    yearlyPrice: 1789,
    description: "Ideal for growing Shopify brands with larger catalogs",
    features: [
      "Up to 150 products in 3D",
      "Full analytics",
      "Priority support",
      "Custom branding",
      "API access",
      "Unlimited model conversions",
      "20% off modeling services",
      "Encrypted data",
      "Model compression",
    ],
    highlighted: true,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: 400,
    billing: "month",
    customPricing: true,
    description: "Custom solution for large Shopify Plus merchants",
    features: [
      "Unlimited products (1000+)",
      "Dedicated success manager",
      "SLA",
      "API/SDK",
      "White-label",
      "Full analytics",
      "Custom encryption protocols",
      "Advanced compression",
      "Large model bundles or custom services",
    ],
    highlighted: false,
  },
];

export const Pricing: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  const [billingInterval, setBillingInterval] = React.useState<
    "month" | "year"
  >("month");

  // Helper function to calculate savings
  const calculateSavings = (monthlyPrice: number, yearlyPrice: number) => {
    const monthlyCostPerYear = monthlyPrice * 12;
    const savings = monthlyCostPerYear - yearlyPrice;
    return savings;
  };

  return (
    <section ref={sectionRef} id="pricing" className="py-24 relative">
      {/* Background glow */}
      <div className="absolute top-1/2 right-1/4 w-1/3 h-1/3 bg-[#9747FF]/10 rounded-full filter blur-[100px] opacity-60"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center mb-8">
          <h2
            className={`text-3xl md:text-4xl font-bold text-white mb-6 transition-all duration-700 ${
              isInView
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-10"
            }`}
          >
            Pricing Plans Designed for Shopify Merchants
          </h2>
          <p
            className={`text-xl text-gray-300 transition-all duration-700 delay-150 ${
              isInView
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-10"
            }`}
          >
            All plans include a 14-day free trial. No credit card required.
            Cancel anytime.
          </p>
        </div>

        {/* Billing Interval Toggle */}
        <div
          className={`flex justify-center mb-8 transition-all duration-700 delay-200 ${
            isInView ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          }`}
        >
          <div className="inline-flex items-center bg-gray-800/50 p-1 rounded-lg">
            <button
              onClick={() => setBillingInterval("month")}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingInterval === "month"
                  ? "bg-blue-400 text-[#0A0A10]"
                  : "text-gray-300 hover:text-white"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingInterval("year")}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingInterval === "year"
                  ? "bg-blue-400 text-[#0A0A10]"
                  : "text-gray-300 hover:text-white"
              }`}
            >
              Yearly
            </button>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div
              key={plan.id}
              className={`transition-all duration-700 ${
                isInView
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-10"
              }`}
              style={{ transitionDelay: `${200 + index * 150}ms` }}
            >
              <GlassCard
                className={`h-full relative ${
                  plan.highlighted
                    ? "border-2 border-blue-400"
                    : "border border-gray-800"
                }`}
              >
                {plan.highlighted && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-blue-400 text-[#0A0A10] font-bold py-1 px-4 rounded-full text-sm">
                    Most Popular
                  </div>
                )}

                <div className="flex flex-col h-full">
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {plan.name}
                  </h3>

                  {plan.customPricing && billingInterval === "month" ? (
                    <div className="text-3xl font-bold text-white mb-6">
                      ${plan.price}+/month
                    </div>
                  ) : plan.customPricing && billingInterval === "year" ? (
                    <div className="text-3xl font-bold text-white mb-6">
                      Custom pricing
                    </div>
                  ) : (
                    <div className="mb-6">
                      <div className="flex items-baseline">
                        <span className="text-3xl font-bold text-white">
                          $
                          {billingInterval === "month"
                            ? plan.price
                            : plan.yearlyPrice}
                        </span>
                        <span className="text-gray-400">
                          /{billingInterval}
                        </span>
                      </div>

                      {/* Show savings for yearly plans */}
                      {billingInterval === "year" && plan.yearlyPrice && (
                        <div className="flex items-center mt-2">
                          <span className="bg-green-500/20 text-green-400 text-xs font-medium px-2 py-1 rounded-full mr-2">
                            Save $
                            {calculateSavings(plan.price, plan.yearlyPrice)}
                          </span>
                          <span className="text-gray-400 text-xs">
                            vs. monthly billing
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  <p className="text-gray-300 mb-6">{plan.description}</p>

                  <ul className="space-y-3 mb-8 flex-grow">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <Check className="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    intent={plan.highlighted ? "primary" : "secondary"}
                    className="w-full mt-auto"
                    onClick={() =>
                      (window.location.href = "/dashboard/subscription")
                    }
                  >
                    {plan.id === "enterprise" ? "Contact Us" : "Get Started"}
                  </Button>
                </div>
              </GlassCard>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
