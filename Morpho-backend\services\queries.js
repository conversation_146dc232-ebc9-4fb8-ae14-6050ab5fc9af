const resHandle = require("../utils/responseHandle");

getAllData = async (req, res, modelName, model) => {
  let modelsToBeRetrieved = await model.find({});
  resHandle.handleData(
    res,
    200,
    `${modelName} Retrieved Successfully`,
    true,
    modelsToBeRetrieved,
  );
};
getDataByVariable = async (req, res, modelName, model, variable) => {
  const dataToBeRetrieved = await model.find({ variable: variable });
  if (!dataToBeRetrieved)
    resHandle.handleError(res, 404, `${modelName} Not Found`);
  resHandle.handleData(
    res,
    200,
    `${modelName} Retrieved Successfully`,
    true,
    dataToBeRetrieved,
  );
};
getModelById = async (req, res, modelName, model, id) => {
  let modelToBeRetrieved = await model.findById(id);
  if (!modelToBeRetrieved)
    return resHandle.handleError(res, 404, `${modelName} Not Found`);
  resHandle.handleData(
    res,
    200,
    `${modelName} Retrieved Successfully`,
    true,
    modelToBeRetrieved,
  );
};

createModel = async (req, res, modelName, model, helper) => {
  try {
    let modelToBeCreated = new model(req.body);
    if (helper && helper.name == "hashId") {
      const hashedId = helper(modelToBeCreated._id);
      modelToBeCreated.hashedId = hashedId;
    }
    await modelToBeCreated.save();
    resHandle.handleData(
      res,
      200,
      `${modelName} Created Successfully`,
      true,
      modelToBeCreated,
    );
  } catch (error) {
    resHandle.handleError(res, 500, `Error ${error}`);
  }
};

deleteModelById = async (req, res, modelName, modelSchema, id) => {
  let modelToBeDeleted = await modelSchema.findByIdAndDelete(id);
  if (!modelToBeDeleted) {
    return resHandle.handleError(res, 404, `${modelName} Not Found`);
  }
  resHandle.handleData(res, 200, `${modelName}  Deleted Successfully`, true);
};
updateModelById = async (req, res, modelName, modelSchema) => {
  const { _id } = req.body;
  let modelToBeUpdated = await modelSchema.findByIdAndUpdate(
    { _id: _id },
    { ...req.body },
    { new: true },
  );
  if (!modelToBeUpdated)
    resHandle.handleError(res, 404, `${modelName} Not Found`);
  resHandle.handleData(
    res,
    200,
    `${modelName} Updated Successfully`,
    true,
    modelToBeUpdated,
  );
};

module.exports = {
  getAllData,
  getModelById,
  createModel,
  deleteModelById,
  getDataByVariable,
  updateModelById,
};
