const router = require("express").Router();
const controller = require("../../controllers/modelCustomizationController");
const {
  validateModelCustomizationSchema,
} = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");
const { admin, designer } = require("../../constants");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
router
  .route("/create-model-customization")
  .post(
    validateModelCustomizationSchema,
    verifyRoles([admin, designer]),
    async<PERSON>rror<PERSON>and<PERSON>(controller.createModelCustomization)
  );
router.get(
  "/get-all-model-customizations",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getAllModelCustomization)
);
router.get(
  "/get-model-customization-by-id",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getModelCustomizationById)
);
router.get(
  "/get-model-customization-with-model",
  asyncError<PERSON><PERSON><PERSON>(controller.getModelCustomizationWithModel)
);
router.delete(
  "/delete-model-customization",
  verifyRoles([admin, designer]),
  asyncErrorHandler(controller.deleteModelCustomization)
);

module.exports = router;
