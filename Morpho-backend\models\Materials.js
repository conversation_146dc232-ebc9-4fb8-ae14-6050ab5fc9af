const mongoose = require("mongoose");
const material = require("./Material");

const materialsSchema = new mongoose.Schema({
  library: {
    type: mongoose.Types.ObjectId,
    ref: "Material Library",
  },
  user: {
    type: mongoose.Types.ObjectId,
    ref: "User",
  },
  material: {
    type: [material],
  },
});

const materialCollection = new mongoose.model("Materials", materialsSchema);

module.exports = materialCollection;
