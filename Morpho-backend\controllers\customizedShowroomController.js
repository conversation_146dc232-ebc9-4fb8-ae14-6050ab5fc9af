const customizedShowroomModel = require("../models/customizedShowroomModel");
const showroomCategoryModel = require("../models/showroomCategoriesModel");
const showroomModel = require("../models/showroomModel");
const {
  createShowroomRelated,
  getCategories,
} = require("../services/showroomService");
const { createHdr } = require("../services/hdrService");
const HDRModel = require("../models/HDR");
const { deepCopyObject } = require("../utils/objectManipulation");
const { uploadToS3NewVersion, uploadToS3 } = require("../config/s3Service");
class Controller {
  create = async (req, res) => {
    const bodyData = req.body;
    const files = req.files;
    const config = {
      sku: null,
      position: {
        x: null,
        y: null,
        z: null,
      },
      rotation: {
        x: null,
        y: null,
      },
      scale: null,
      url: null, // Initialize as null
      description: {
        bold: null,
        font: null,
        italic: null,
        text: null,
        textAlignment: null,
        toogle: null,
      },
      hdr: {
        id: null,
        intensity: null,
        rotationY: null,
      },
      logo: {
        borderShape: null,
        frameColor: { r: null, g: null, b: null },
        imageUrl: null,
        scale: null,
      },
      images: {
        toogle: null,
        imageUrls: [],
        borderColor: {
          r: null,
          g: null,
          b: null,
        },
      },
      materials: [],
      selectedTemplate: null,
      templateColor: null,
      templateTextColor: null,
      templateTextFont: null,
      user: req.user,
    };

    const hdrData = {
      name: bodyData.hdrName,
      url: files.hdrUrl ? files.hdrUrl[0].path : null,
      icon: files.hdrIcon ? files.hdrIcon[0].path : null,
      rotationY: bodyData.hdrRotationY || null,
      intensity: bodyData.hdrIntensity || null,
      category: bodyData.hdrCategory || null,
      library: bodyData.hdrLibrary || null,
    };

    const bucket = `showroom`;
    const folder = `CustomizedShowroom`;

    if (req.files.imageUrls) {
      const imageUploadPromises = req.files.imageUrls.map((file) =>
        uploadToS3NewVersion(file, "images", bucket)
      );
      const uploadResults = await Promise.all(imageUploadPromises);

      const imageUrls = uploadResults.map((result) => result.Location);
      bodyData.images.imageUrls = imageUrls;
    }

    if (req.files.logoUrl) {
      const data = await uploadToS3(req.files.logoUrl[0]);
      const url = data[0]?.Location;
      config.logo.imageUrl = url;
      bodyData.logo.imageUrl = url;
    }

    if (req.files.url) {
      const data = await uploadToS3(req.files.url[0]);
      const url = data[0]?.Location;
      config.url = url;
      bodyData.url = url;
    } else if (bodyData.url) {
      config.url = bodyData.url;
    } else {
      return res.status(400).json({ error: "URL is required" });
    }

    const newCustomizedShowroom = await createShowroomRelated(
      bodyData,
      files,
      config,
      customizedShowroomModel,
      bucket,
      folder
    );

    newCustomizedShowroom.logo.imageUrl = config.logo.imageUrl;
    newCustomizedShowroom.url = config.url;
    newCustomizedShowroom.sku = req.body.sku;
    newCustomizedShowroom.scale = req.body.scale;
    newCustomizedShowroom.selectedTemplate = req.body.selectedTemplate;
    newCustomizedShowroom.templateColor = req.body.templateColor;
    newCustomizedShowroom.templateTextColor = req.body.templateTextColor;
    newCustomizedShowroom.templateTextFont = req.body.templateTextFont;

    await newCustomizedShowroom.save();
    return res.status(200).json({ data: newCustomizedShowroom });
  };
  getByIdAndSku = async (req, res) => {
    const { id, sku } = req.query;
    const customizedShowroom = await customizedShowroomModel.findById(id);
    const showroom = await showroomModel.findOne({ sku });
    if (!customizedShowroomModel || !showroom)
      return res.status(404).json("Showroom Or Customized Showrom Not Found");
    return res.status(201).json({
      customizedShowroom,
      showroom,
    });
  };
  getById = async (req, res) => {
    const { id } = req.params;
    try {
      // Use `lean()` to convert the Mongoose document into a plain JavaScript object
      const customizedShowroom = await customizedShowroomModel
        .findById(id)
        .populate({
          path: "hdr.id",
          select: "name url -_id", // Only select 'name' and 'url' fields from hdr
        });
      if (!customizedShowroom) {
        return res.status(404).json("Customized Showroom Not Found");
      }
      customizedShowroom.hdr.id.intensity = customizedShowroom.intensity;
      customizedShowroom.hdr.id.rotationY = customizedShowroom.rotationY;
      return res.status(200).json({ data: customizedShowroom });
    } catch (error) {
      return res.status(500).json("Server Error");
    }
  };

  getAllByUser = async (req, res) => {
    const id = req.user;
    const customizedShowroom = await customizedShowroomModel.findById({
      user: id,
    });
    return res.status(200).json({ data: customizedShowroom });
  };
  updateById = async (req, res) => {
    try {
      const { id } = req.params;
      const bodyData = req.body;
      const files = req.files;
      const bucket = `showroom`;

      // Initialize inputs object
      let inputs = {
        position: {
          x: bodyData.position?.x || null,
          y: bodyData.position?.y || null,
          z: bodyData.position?.z || null,
        },
        rotation: {
          x: bodyData.rotation?.x || null,
          y: bodyData.rotation?.y || null,
        },
        scale: bodyData.scale || null,
        url: bodyData.url || null,
        description: {
          bold: bodyData.description?.bold || null,
          font: bodyData.description?.font || null,
          italic: bodyData.description?.italic || null,
          text: bodyData.description?.text || null,
          textAlignment: bodyData.description?.textAlignment || null,
          toogle: bodyData.description?.toogle || null,
        },
        hdr: null,
        logo: {
          borderShape: bodyData.logo?.borderShape || null,
          frameColor: {
            r: bodyData.logo?.frameColor?.r || null,
            g: bodyData.logo?.frameColor?.g || null,
            b: bodyData.logo?.frameColor?.b || null,
          },
          imageUrl: bodyData.logo?.logoUrl || null,
          scale: bodyData.logo?.scale || null,
        },
        images: {
          toogle: bodyData.images?.toogle || null,
          imageUrls: [],
          borderColor: {
            r: bodyData.images?.borderColor?.r || null,
            g: bodyData.images?.borderColor?.g || null,
            b: bodyData.images?.borderColor?.b || null,
          },
        },
        materials: bodyData.materials || [],
        user: req.user,
      };

      // Process HDR
      const hdr = await createHdr(bodyData, files, req);
      bodyData.hdr = hdr._id;
      inputs.hdr = hdr._id;

      // Handle image uploads
      if (files.imageUrls) {
        const imageUploadPromises = files.imageUrls.map((file) =>
          uploadToS3NewVersion(file, "images", bucket)
        );
        const uploadResults = await Promise.all(imageUploadPromises);
        inputs.images.imageUrls = uploadResults.map(
          (result) => result.Location
        );
      }

      // Handle the URL update
      if (typeof bodyData.url === "string") {
        inputs.url = bodyData.url;
      } else if (files?.url) {
        const data = await uploadToS3(files.url[0]);
        inputs.url = data[0]?.Location;
      }

      // Handle the logo update
      if (typeof bodyData.logo === "object") {
        inputs.logo = bodyData.logo;
      } else if (files?.logo) {
        const data = await uploadToS3(files.logo[0]);
        inputs.logo.imageUrl = data[0]?.Location;
      }

      // Handle the category creation if not found
      let categoryToBeFound = await showroomCategoryModel.findOne({
        name: bodyData.showroomCategory,
      });
      if (!categoryToBeFound) {
        categoryToBeFound = new showroomCategoryModel({
          name: bodyData.showroomCategory,
        });
        await categoryToBeFound.save();
      }
      inputs.category = categoryToBeFound._id;

      // Update the customized showroom
      const showroomToBeUpdated =
        await customizedShowroomModel.findByIdAndUpdate(
          id,
          { ...inputs },
          { new: true, runValidators: true }
        );

      if (!showroomToBeUpdated) {
        return res.status(404).json({ message: "Showroom Not Found" });
      }
      //test
      return res.status(200).json({ data: showroomToBeUpdated });
    } catch (error) {
      return res.status(500).json({ error: error.message });
    }
  };

  getMostUsedSkus = async (req, res) => {
    const topSkus = await customizedShowroomModel.aggregate([
      {
        $group: {
          _id: "$sku",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
      {
        $limit: 10,
      },
      {
        $project: {
          _id: 0,
          sku: "$_id",
          count: 1,
        },
      },
    ]);

    res.status(200).json(topSkus);
  };
  getAllCategoriesOfShowroom = async (req, res) => {
    const categories = await getCategories(customizedShowroomModel);
    return res.status(200).json({ data: categories });
  };
  deleteById = async (req, res) => {
    const { id } = req.params;
    const customizedShowroom = await customizedShowroomModel.findByIdAndDelete(
      id
    );
    if (!customizedShowroom)
      return res.status(404).json("Customized Showroom Not Found");
    return res.status(200).json("Customized Showroom Deleted");
  };
}

const controller = new Controller();
module.exports = controller;
