const mongoose = require("mongoose");

const projectSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    industry: {
      type: String,
    },
    client: {
      type: mongoose.Types.ObjectId,
      ref: "User",
    },
    type: {
      type: String,
    },
    portalUrl: {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    type_of_integration: {
      type: String,
    },
    artists: [
      {
        type: mongoose.Types.ObjectId,
        ref: "User",
      },
    ],
    template: {
      type: mongoose.Types.ObjectId,
      ref: "Template",
    },
    status: {
      type: String,
      enum: ["request", "in-progress", "rejected", "canceled", "done"],
      default: "request",
    },
    softDelete: {
      type: Boolean,
      default: false,
    },
    isShowroom: {
      type: Boolean,
      type: false,
    },
    uiUrl: {
      type: String,
      url: /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
  },
  { timestamps: true, collection: "projects" },
);
const projectModel = mongoose.model("Project", projectSchema);
module.exports = projectModel;
