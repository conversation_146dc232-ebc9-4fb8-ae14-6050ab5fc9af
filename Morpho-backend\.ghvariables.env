############## NODE CREDENTIALS #######################
NODE_VERSION=18.16.0
#######################################################

############## TASK DEFINITION PATH #######################
TASK_DEFINITION_PATH=.github/definitions/container-definition.yml
#######################################################

############## BACKEND SECRETS #######################
AWS_BACKEND_SSM_ROLE_PATH=/slaves/backend/role-arn
AWS_CLUSTER_NAME=Backend-Cluster
AWS_TARGET_GROUP_PATH=/morpho
AWS_LOAD_BALANCER_LISTENER_ARN=arn:aws:elasticloadbalancing:eu-central-1:334641153102:listener/app/load1/c84cad03470d6675/2ff717ebb9355687
AWS_LOAD_BALANCER_RULE_ARN=arn:aws:elasticloadbalancing:eu-central-1:334641153102:listener-rule/app/load1/c84cad03470d6675/2ff717ebb9355687/95aecee8266052d7
AWS_PROJECT_NAME=backendMorpho
AWS_TASKS_TO_RUN_NUMBERS=1
AWS_VPC_ID=vpc-0d72fdf299264b9b3
CONTAINER_PORT=5000
DOCKER_TAG=latest
DOCKER_IMAGE=backendmorphoimage
#######################################################

