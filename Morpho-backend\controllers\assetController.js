const { default: mongoose } = require("mongoose");
const assetSchema = require("../models/AssetLibrary");
const projectSchema = require("../models/Project");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
const {
  getAllData,
  getModelById,
  createModel,
  deleteModelById,
} = require("../services/dataService");
class Controller {
  createAsset = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Create asset using service
    return await createModel(req, res, "Asset", assetSchema);
  };
  getAllAssets = async (req, res) => {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await assetSchema.countDocuments();

    // Check if assets exist
    if (totalCount === 0) {
      throw new expressError(
        "No assets found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch assets with pagination
    const allAssets = await assetSchema.find({}).skip(skip).limit(limit);

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "Assets retrieved successfully",
      true,
      allAssets,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };
  getAssetById = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Asset ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid asset ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Get asset by ID using service
    return await getModelById(req, res, "Asset", assetSchema, id);
  };
  getAssetByProjectId = async (req, res) => {
    // Get project ID from request body
    const { project } = req.body;

    // Validate required fields
    if (!project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!project.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Fetch asset library with related data
    const assetLibrary = await assetSchema.aggregate([
      { $match: { project: mongoose.Types.ObjectId(project) } },
      {
        $lookup: {
          from: "projects",
          localField: "project",
          foreignField: "_id",
          as: "project",
        },
      },
      {
        $unwind: {
          path: "$project",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "GlbModel",
          localField: "_id",
          foreignField: "asset_library",
          as: "models",
        },
      },
      {
        $unwind: {
          path: "$models",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "categories",
          localField: "models.category",
          foreignField: "_id",
          as: "models.category",
        },
      },
      {
        $unwind: {
          path: "$models.category",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$_id",
          project: { $first: "$project" },
          models: { $push: "$models" },
        },
      },
    ]);

    // Check if assets exist
    if (!assetLibrary || assetLibrary.length === 0) {
      throw new expressError(
        "No assets found for this project",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Assets retrieved successfully",
      true,
      assetLibrary
    );
  };

  getAssetByIdWithProject = async (req, res) => {
    // Get asset ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Asset ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid asset ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find asset by ID and populate project
    const assetToBeFound = await assetSchema.findById(id).populate("project");

    // Check if asset exists
    if (!assetToBeFound) {
      throw new expressError(
        "Asset not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Asset with project details retrieved successfully",
      true,
      assetToBeFound
    );
  };
  deleteAsset = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Asset ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid asset ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete asset by ID using service
    return await deleteModelById(req, res, "Asset", assetSchema, id);
  };
  updateAsset = async (req, res) => {
    let { id, assetLibrary_name } = req.body;

    // Validate required fields
    if (!id || !assetLibrary_name) {
      throw new expressError(
        "Asset ID and name are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid asset ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Update asset
    let assetToBeUpdated = await assetSchema.findByIdAndUpdate(
      id,
      { assetLibrary_name: assetLibrary_name },
      { new: true } // Return updated document
    );

    // Check if asset exists
    if (!assetToBeUpdated) {
      throw new expressError(
        "Asset not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Asset Updated Successfully",
      true,
      assetToBeUpdated
    );
  };
}

let controller = new Controller();
module.exports = controller;
