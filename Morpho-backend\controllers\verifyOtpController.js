const userOtp = require("../models/userOtp");
const user = require("../models/User");
const transporter = require("../config/nodemailerConfig");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const {
  EMAIL,
  ACCESS_TOKEN_SECRET,
  REFRESH_TOKEN_SECRET,
} = require("../config/envVariables");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");

class Controller {
  // Original method - kept for backward compatibility
  sendOTPVerification = async ({ _id, email }, res) => {
    // Validate required fields
    if (!_id || !email) {
      throw new expressError(
        "User ID and email are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Generate OTP
    const otp = `${Math.floor(1000 + Math.random() * 9000)}`;

    // Create mail options
    const mailOptions = {
      from: EMAIL,
      to: email,
      subject: "Verify your Email",
      text: `Enter ${otp} in the app to verify your account`,
    };

    // Hash OTP
    const saltRounds = 10;
    const hashedOtp = await bcrypt.hash(otp, saltRounds);

    // Create OTP verification record
    const newOTPVerification = new userOtp({
      userId: _id,
      otp: hashedOtp,
      createdAt: Date.now(),
      expiresAt: Date.now() + 3600000,
    });

    // Save OTP verification record
    await newOTPVerification.save();

    // Send email
    await transporter.sendMail(mailOptions);

    // Return success response
    return res.json({
      status: "Pending",
      message: "Verification OTP email sent",
      data: {
        userId: _id,
        email,
      },
    });
  };

  // New asynchronous method that doesn't wait for response
  sendOTPVerificationAsync = async ({ _id, email }) => {
    // Validate required fields
    if (!_id || !email) {
      console.error("Missing required fields: user ID or email");
      return false;
    }

    try {
      // Generate OTP
      const otp = `${Math.floor(1000 + Math.random() * 9000)}`;

      // Create mail options
      const mailOptions = {
        from: EMAIL,
        to: email,
        subject: "Verify your Email",
        text: `Enter ${otp} in the app to verify your account`,
      };

      // Use a lower salt round for faster hashing
      const saltRounds = 8;
      const hashedOtp = await bcrypt.hash(otp, saltRounds);

      // Create and save OTP verification record
      const newOTPVerification = new userOtp({
        userId: _id,
        otp: hashedOtp,
        createdAt: Date.now(),
        expiresAt: Date.now() + 3600000,
      });

      await newOTPVerification.save();

      // Send email without awaiting the result
      transporter.sendMail(mailOptions, (error, info) => {
        if (error) {
          console.error("Error sending OTP email:", error);
        } else {
          console.log("OTP email sent:", info.response);
        }
      });

      return true;
    } catch (error) {
      console.error("Error in sendOTPVerificationAsync:", error);
      return false;
    }
  };
  verifyOTP = async (req, res) => {
    let { userId, otp } = req.body;

    // Validate required fields
    if (!userId || !otp) {
      throw new expressError(
        "User ID and OTP are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find OTP verification records
    const userOTPVerificationRecords = await userOtp.find({ userId });

    // Check if verification records exist
    if (userOTPVerificationRecords.length <= 0) {
      throw new expressError(
        "Account record doesn't exist or has been verified already",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Get OTP details
    const { expiresAt } = userOTPVerificationRecords[0];
    const hashedOTP = userOTPVerificationRecords[0].otp;

    // Check if OTP has expired
    if (expiresAt < Date.now()) {
      // Delete expired OTP records
      await userOtp.deleteMany({ userId });
      throw new expressError(
        "OTP has expired",
        400,
        expressError.CODES.EXPIRED_TOKEN
      );
    }

    // Verify OTP
    const validOTP = await bcrypt.compare(otp, hashedOTP);

    // Check if OTP is valid
    if (!validOTP) {
      throw new expressError(
        "The OTP entered is wrong",
        400,
        expressError.CODES.INVALID_CREDENTIALS
      );
    }

    // Update user verification status
    await user.updateOne({ _id: userId }, { verified: true });

    // Delete OTP records
    await userOtp.deleteMany({ userId });

    // Find the user and populate the role
    const userLogIn = await user.findOne({ _id: userId }).populate("role");

    // Check if user exists
    if (!userLogIn) {
      throw new expressError(
        "User not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Get the role ID
    const roleId = userLogIn.role ? userLogIn.role._id : null;

    // Create an access token
    const accessToken = jwt.sign(
      {
        userId: userLogIn._id,
        role: roleId,
        email: userLogIn.email,
      },
      ACCESS_TOKEN_SECRET,
      { expiresIn: "12h" }
    );

    // Create a refresh token
    const refreshToken = jwt.sign(
      {
        userId: userLogIn._id,
        role: roleId,
        email: userLogIn.email,
      },
      REFRESH_TOKEN_SECRET,
      { expiresIn: "40d" }
    );

    // Update user's refresh token
    userLogIn.refreshToken = refreshToken;
    await userLogIn.save();

    // Set cookie with refresh token
    res.cookie("jwt", refreshToken, {
      httpOnly: true,
      sameSite: "None",
      // secure: true, // Activate in production mode
      maxAge: 3888000000,
    });

    // Return success response with access token and user data
    return res.status(200).json({
      message: "User Logged In",
      success: true,
      data: userLogIn,
      accessToken: accessToken,
    });
  };
  resendOTP = async (req, res) => {
    let { userId, email } = req.body;

    // Validate required fields
    if (!userId || !email) {
      throw new expressError(
        "User ID and email are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Delete existing OTP records
    await userOtp.deleteMany({ userId });

    // Use the async method to send OTP
    const success = await this.sendOTPVerificationAsync({
      _id: userId,
      email,
    });

    // Check if OTP was sent successfully
    if (!success) {
      throw new expressError(
        "Failed to send OTP email",
        500,
        expressError.CODES.SERVER_ERROR
      );
    }

    // Return success response
    return res.json({
      status: "Pending",
      message: "Verification OTP email sent",
      data: {
        userId,
        email,
      },
    });
  };
}
const controller = new Controller();

module.exports = controller;
