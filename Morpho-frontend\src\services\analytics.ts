import { storage } from "../utils/storage";
import { apiCache } from "../utils/cache";
import type { AnalyticsData } from "../types/analytics";

const API_URL = "httsp://api.modularcx.link/morpho/analytics";

class AnalyticsService {
  private getDeviceType(): string {
    const ua = navigator.userAgent;
    if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
      return "tablet";
    }
    if (
      /Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(
        ua
      )
    ) {
      return "mobile";
    }
    return "desktop";
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem("analytics_session_id");
    if (!sessionId) {
      sessionId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem("analytics_session_id", sessionId);
    }
    return sessionId;
  }

  async getModelAnalytics(modelId: string): Promise<{
    views: number;
    downloads: number;
    conversions: number;
    avgTimeSpent: string;
    viewsByDevice: {
      desktop: number;
      mobile: number;
      tablet: number;
    };
    dailyViews: number[];
  }> {
    const token = storage.getToken();
    if (!token) {
      throw new Error("No authentication token found");
    }

    const cacheKey = `model_analytics_${modelId}`;
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    const response = await fetch(`${API_URL}/analytics/model/${modelId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch model analytics");
    }

    const data = await response.json();
    apiCache.set(cacheKey, data);
    return data;
  }
}

export const analyticsService = new AnalyticsService();
