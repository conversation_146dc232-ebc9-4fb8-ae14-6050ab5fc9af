const commentModel = require("../models/CommentModel");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
class Controller {
  createComment = async (req, res) => {
    // Validate required fields
    if (!req.body.product_id) {
      throw new expressError(
        "Product ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!req.body.comment) {
      throw new expressError(
        "Comment text is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Create new comment
    const newComment = new commentModel({
      ...req.body,
      user: req.user,
    });

    // Save comment to database
    await newComment.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Comment created successfully",
      true,
      newComment
    );
  };
  getAllComments = async (req, res) => {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await commentModel.countDocuments();

    // Check if comments exist
    if (totalCount === 0) {
      throw new expressError(
        "No comments found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch comments with pagination and populate references
    const allComments = await commentModel
      .find({})
      .skip(skip)
      .limit(limit)
      .populate(["user", "product_id"]);

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "Comments retrieved successfully",
      true,
      allComments,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };
  getCommentById = async (req, res) => {
    // Get comment ID from request body
    const { _id } = req.body;

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Comment ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!_id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid comment ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find comment by ID and populate references
    const commentToBeRetrieved = await commentModel
      .findById(_id)
      .populate(["user", "product_id"]);

    // Check if comment exists
    if (!commentToBeRetrieved) {
      throw new expressError(
        "Comment not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Comment retrieved successfully",
      true,
      commentToBeRetrieved
    );
  };
  deleteCommentById = async (req, res) => {
    // Get comment ID from request body
    const { _id } = req.body;

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Comment ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!_id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid comment ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete comment
    const commentToBeDeleted = await commentModel.findByIdAndDelete(_id);

    // Check if comment exists
    if (!commentToBeDeleted) {
      throw new expressError(
        "Comment not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(res, 200, "Comment deleted successfully", true);
  };
}

const controller = new Controller();
module.exports = controller;
