const { createPaymentIntent } = require("../services/stripeService");
const { STRIPE_PUBLISHABLE_KEY } = require("../config/envVariables");

exports.config = (req, res) => {
  return res.json({ publishableKey: STRIPE_PUBLISHABLE_KEY });
};

exports.createPayment = async (req, res, next) => {
  const { currency } = req.body;
  const paymentIntent = await createPaymentIntent(currency, 100);
  return res.send({ clientSecret: paymentIntent.client_secret });
};
