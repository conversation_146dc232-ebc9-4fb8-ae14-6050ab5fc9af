export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupCredentials {
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  password: string;
  // Role field for assigning user role
  role?: string;
  // Optional fields
  phoneNumber?: string;
  country?: string;
  companyName?: string;
  industry?: string;
  companyWebsite?: string;
}

export interface LoginResponse {
  message: string;
  success: boolean;
  data: {
    _id: string;
    first_name: string;
    last_name: string;
    username: string;
    email: string;
    role: string;
    verified: boolean;
    refreshToken: string;
  };
  accessToken: string;
}

export interface SignupResponse {
  message: string;
  success: boolean;
}
