const { default: mongoose } = require("mongoose");
const glbSchema = require("../models/GlbModel");
const projectSchema = require("../models/Project");
const { generateRandomHash } = require("../utils/generateApiKey");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
const roleSchema = require("../models/Role");
const { uploadToS3WithConversion, uploadToS3 } = require("../config/s3Service");
const projectModel = require("../models/Project");
const assetLibraryModel = require("../models/AssetLibrary");
const { v4: uuidv4 } = require("uuid");
const glbModelModel = require("../models/GlbModel");
class Controller {
  generateRandomString = (length) => {
    const characters =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let randomString = "";
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      randomString += characters.charAt(randomIndex);
    }
    return randomString;
  };
  createGlbModel = async (req, res) => {
    // Validate required fields
    if (!req.body.name) {
      throw new expressError(
        "Model name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!req.body.project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Create new GLB model with creator information
    const newModel = new glbSchema({
      ...req.body,
      createdBy: req.user, // Add the creator's ID
      key: generateRandomHash(process.env.SECRECT_API_KEY_GENERATOR, 16),
    });

    // Save model to database
    await newModel.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "3D model created successfully",
      true,
      newModel
    );
  };
  creteGlbModelUpdated = async (req, res) => {
    let data;
    if (req.file) {
      data = await uploadToS3(req.file);
    }
    let newModel = new glbSchema({
      ...req.body,
      key: generateRandomHash(process.env.SECRECT_API_KEY_GENERATOR, 16),
      url: data[0].Location,
      // 64675b0c4c280d1bcd1b4516
    });
    await newModel.save();
    return res.status(200).json(creteGlbModelUpdated);
  };
  createProductPhone = async (req, res, next) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate required fields
    const bodyData = req.body;
    if (!bodyData.name) {
      throw new expressError(
        "Product name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!bodyData.project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Prepare input data
    let inputs = {
      name: bodyData.name,
      description: bodyData.description,
      price: bodyData.price,
      sku: bodyData.sku,
      sizes: bodyData.sizes,
      imageUrl: bodyData.imageUrl,
      width: bodyData.width,
      assetLibrary: bodyData.assetLibrary,
      hdr: bodyData.hdr,
      customizations: bodyData.customizations,
      variants: bodyData.variants,
      project: bodyData.project,
    };

    // Handle file upload
    let data;
    if (req.file) {
      data = await uploadToS3WithConversion(req.file);
      const url = data.glbUrl;
      inputs.url = url;
    } else {
      throw new expressError(
        "3D model file is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Get USDZ URL
    const usdz = data.usdzUrl;

    // Create product
    const productToBeCreated = new glbSchema({
      ...inputs,
      key: generateRandomHash(process.env.SECRECT_API_KEY_GENERATOR, 16),
      usdzUrl: usdz,
    });

    // Save product
    await productToBeCreated.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Product created successfully",
      true,
      {
        productToBeCreated,
        usdz: usdz,
      }
    );
  };
  getAllGlbModels = async (req, res) => {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await glbSchema.countDocuments();

    // Fetch models with pagination
    const glbModelToBeRetrieved = await glbSchema
      .find({})
      .skip(skip)
      .limit(limit);

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "3D models retrieved successfully",
      true,
      glbModelToBeRetrieved,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };
  //Need to Be Fixed
  createProductForShowroom = async (req, res) => {
    const file = req.file;
    const originalName = file.originalname;
    const nameWithoutExtension = originalName.substring(
      originalName.lastIndexOf(".")
    );
    const data = await uploadToS3(file);
    const modelUrl = data[0].Location;
    let assetLibraryId;
    const projectToBeFoundForUser = await projectModel.findOne({
      client: req.user,
      isShowroom: true,
    });
    if (!projectToBeFoundForUser) {
      const newProject = new projectModel({
        name: "Showroom",
        description: "Showroom",
        client: req.user,
        isShowroom: true,
      });
      await newProject.save();
      const newAssetLibrary = new assetLibraryModel({
        serial_number: uuidv4(),
        project: newProject._id,
      });
      await newAssetLibrary.save();
      assetLibraryId = newAssetLibrary._id;
    }
    const modelToBeCreated = new glbSchema(req.body, {
      url: modelUrl,
      asset_library: assetLibraryId,
    });
    await modelToBeCreated.save();
  };
  getModelsByClient = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    let userRole = "Client"; // Default to Client role if not found

    // Only try to find the role if req.role is not empty
    if (req.role && req.role !== "") {
      try {
        const roleToBeRetrieved = await roleSchema
          .findById(req.role)
          .select("-_id role");

        if (roleToBeRetrieved) {
          userRole = roleToBeRetrieved.role;
        }
      } catch (roleError) {
        console.error("Error finding role:", roleError);
        // Continue with default Client role
      }
    } else {
      console.log("No role provided in request, using default Client role");
    }

    // Build aggregation pipeline
    let pipeline = [
      {
        $lookup: {
          from: "projects",
          let: { projectId: "$project" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$_id", "$$projectId"] }
              }
            },
            {
              $project: {
                _id: 1,
                name: 1,
                client: 1,
                artists: 1,
                status: 1,
                description: 1
              }
            }
          ],
          as: "project"
        }
      },
      {
        $unwind: {
          path: "$project",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "categories",
          localField: "category",
          foreignField: "_id",
          as: "category"
        }
      },
      {
        $unwind: {
          path: "$category",
          preserveNullAndEmptyArrays: true
        }
      }
    ];

    // Apply role-based filtering
    if (userRole === "Client") {
      pipeline.push({
        $match: { "project.client": mongoose.Types.ObjectId(req.user) }
      });
    } else if (userRole === "Designer") {
      pipeline.push({
        $match: {
          $or: [
            // Case 1: User is the client of the project
            { "project.client": mongoose.Types.ObjectId(req.user) },
            // Case 2: User is in the artists array of the project
            { "project.artists": mongoose.Types.ObjectId(req.user) }
          ]
        }
      });
    }

    // Add sorting
    pipeline.push({
      $sort: { createdAt: -1 }
    });

    // Execute the aggregation
    const glbModelToBeRetrieved = await glbSchema.aggregate(pipeline);

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D models retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  test = async (req, res) => {
    // Validate user ID
    if (!req.params.userId) {
      throw new expressError(
        "User ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!req.params.userId.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid user ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Convert user ID to ObjectId
    const userId = mongoose.Types.ObjectId(req.params.userId);

    // Build aggregation pipeline
    let pipeline = [
      {
        $lookup: {
          from: "projects",
          localField: "project",
          foreignField: "_id",
          as: "project",
        },
      },
      {
        $unwind: {
          path: "$project",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $match: {
          $or: [
            { "project.client": userId }, // User is the client
            // { "project.artists": userId }, // User is one of the artists
          ],
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ];

    // Execute aggregation
    const glbModels = await glbSchema.aggregate(pipeline);

    // Check if models exist
    if (glbModels.length === 0) {
      return resHandle.handleData(
        res,
        404,
        "No GLB Models found for the user",
        true,
        []
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "GLB Models Retrieved Successfully",
      true,
      glbModels
    );
  };

  getModelsByClientByClientId = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    let userRole = "Client"; // Default to Client role if not found

    // Only try to find the role if req.role is not empty
    if (req.role && req.role !== "") {
      try {
        const roleToBeRetrieved = await roleSchema
          .findById(req.role)
          .select("-_id role");

        if (roleToBeRetrieved) {
          userRole = roleToBeRetrieved.role;
        }
      } catch (roleError) {
        console.error("Error finding role:", roleError);
        // Continue with default Client role
      }
    } else {
      console.log("No role provided in request, using default Client role");
    }

    // Build aggregation pipeline
    let pipeline = [
      {
        $lookup: {
          from: "projects",
          localField: "project",
          foreignField: "_id",
          as: "project",
        },
      },
      {
        $unwind: {
          path: "$project",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "categories",
          localField: "category",
          foreignField: "_id",
          as: "category",
        },
      },
      {
        $unwind: {
          path: "$category",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ];

    // Apply role-based filtering
    if (userRole === "Client") {
      // Filter projects where the user is the client
      pipeline.push({
        $match: { "project.client": mongoose.Types.ObjectId(req.user) },
      });
    } else if (userRole === "Designer") {
      // Filter projects where the user is one of the artists
      pipeline.push({
        $match: { "project.artists": mongoose.Types.ObjectId(req.user) },
      });
    }

    // Execute aggregation
    let glbModelToBeRetrieved = await glbSchema.aggregate([...pipeline]);

    // Check if models exist
    if (!glbModelToBeRetrieved || glbModelToBeRetrieved.length === 0) {
      throw new expressError(
        "No 3D models found for this user",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D models retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  getBySkuAndProject = async (req, res) => {
    const { sku, project } = req.query;

    // Validate required fields
    if (!sku) {
      throw new expressError(
        "SKU is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate project ID format
    if (!project.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find model by SKU and project
    const glbToBeRetrieved = await glbSchema.findOne({
      sku: sku,
      project: project,
    });

    // Check if model exists
    if (!glbToBeRetrieved) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model retrieved successfully",
      true,
      glbToBeRetrieved
    );
  };
  getAllProductsByProjectId = async (req, res) => {
    // Get project ID from query parameters
    const { project } = req.query;

    // Validate required fields
    if (!project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!project.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await glbSchema.countDocuments({ project });

    // Check if models exist for this project
    if (totalCount === 0) {
      throw new expressError(
        "No 3D models found for this project",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch models with pagination and populate references
    const glbModelsToBeFetched = await glbSchema
      .find({ project })
      .skip(skip)
      .limit(limit)
      .populate("project")
      .populate("category")
      .populate("collectionId")
      .populate("asset_library")
      .populate("customizations.values.icon_id");

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "3D models retrieved successfully",
      true,
      glbModelsToBeFetched,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };
  getAllProductsAndProjects = async (req, res, next) => {
    // Validate user authentication
    const userId = req.user;
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Find projects for user
    const projects = await projectSchema.find({ client: userId });

    // Check if projects exist
    if (!projects || projects.length === 0) {
      throw new expressError(
        "No projects found for this user",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Collect data for each project
    const projectsWithData = [];
    for (let project of projects) {
      const glbModels = await glbSchema
        .find({ project: project._id })
        .populate("project");

      projectsWithData.push({
        project: project,
        glbModels: glbModels,
      });
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Projects and products retrieved successfully",
      true,
      projectsWithData
    );
  };

  getAllGlbModelsByProjectId = async (req, res) => {
    // Get project ID from request body
    const { project } = req.body;

    // Validate required fields
    if (!project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!project.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await glbSchema.countDocuments({ project });

    // Check if models exist for this project
    if (totalCount === 0) {
      throw new expressError(
        "No 3D models found for this project",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch models with pagination and populate references
    const glbModelsToBeFetched = await glbSchema
      .find({ project })
      .skip(skip)
      .limit(limit)
      .populate("project")
      .populate("category")
      .populate("collectionId")
      .populate("asset_library")
      .populate("customizations.values.icon_id");

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "3D models retrieved successfully",
      true,
      glbModelsToBeFetched,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };
  getGlbModelById = async (req, res) => {
    // Get model ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Fetch model with all related data using aggregation pipeline
      const glbModel = await glbSchema.aggregate([
        {
          $match: { _id: mongoose.Types.ObjectId(id) },
        },
        {
          $lookup: {
            from: "projects",
            localField: "project",
            foreignField: "_id",
            as: "project",
          },
        },
        {
          $unwind: {
            path: "$project",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "categories",
            localField: "category",
            foreignField: "_id",
            as: "category",
          },
        },
        {
          $unwind: {
            path: "$category",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "collections",
            localField: "collectionId",
            foreignField: "_id",
            as: "collectionId",
          },
        },
        {
          $unwind: {
            path: "$collectionId",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "assetlibraries",
            localField: "asset_library",
            foreignField: "_id",
            as: "asset_library",
          },
        },
        {
          $unwind: {
            path: "$asset_library",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "MediaModel",
            localField: "_id",
            foreignField: "model",
            as: "media",
          },
        },
        {
          $addFields: {
            media: {
              $filter: {
                input: "$media",
                as: "media",
                cond: {
                  $eq: ["$$media.isMedia", true],
                },
              },
            },
          },
        },
        {
          $unwind: {
            path: "$media",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $sort: {
            "media.createdAt": -1,
          },
        },
        {
          $group: {
            _id: "$_id",
            root: { $first: "$$ROOT" },
            media: { $push: "$media" },
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: ["$root", { media: "$media" }],
            },
          },
        },
        {
          $lookup: {
            from: "MediaModel",
            localField: "_id",
            foreignField: "model",
            as: "referenceFiles",
          },
        },
        {
          $addFields: {
            referenceFiles: {
              $filter: {
                input: "$referenceFiles",
                as: "referenceFiles",
                cond: {
                  $eq: ["$$referenceFiles.isMedia", false],
                },
              },
            },
          },
        },
        {
          $unwind: {
            path: "$referenceFiles",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $sort: {
            "referenceFiles.createdAt": -1,
          },
        },
        {
          $group: {
            _id: "$_id",
            root: { $first: "$$ROOT" },
            referenceFiles: { $push: "$referenceFiles" },
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: ["$root", { referenceFiles: "$referenceFiles" }],
            },
          },
        },
        {
          $lookup: {
            from: "CommentModel",
            localField: "_id",
            foreignField: "product_id",
            as: "comments",
          },
        },
        {
          $unwind: {
            path: "$comments",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "comments.user",
            foreignField: "_id",
            as: "comments.user",
          },
        },
        {
          $unwind: {
            path: "$comments.user",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: "$_id",
            root: { $first: "$$ROOT" },
            comments: { $push: "$comments" },
          },
        },
        {
          $addFields: {
            "root.comments": "$comments",
          },
        },
        {
          $replaceRoot: {
            newRoot: "$root",
          },
        },
        {
          $unwind: {
            path: "$customizations",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$customizations.values",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "MediaModel",
            localField: "customizations.values.icon_id",
            foreignField: "_id",
            as: "customizations.values.icon",
          },
        },
        {
          $unwind: {
            path: "$customizations.values.icon",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: {
              _id: "$_id",
              customizationId: "$customizations._id",
            },
            root: { $first: "$$ROOT" },
            values: { $push: "$customizations.values" },
          },
        },
        {
          $addFields: {
            "root.customizations.values": "$values",
          },
        },
        {
          $group: {
            _id: "$_id._id",
            root: { $first: "$root" },
            customizations: { $push: "$root.customizations" },
          },
        },
        {
          $addFields: {
            "root.customizations": "$customizations",
          },
        },
        {
          $replaceRoot: {
            newRoot: "$root",
          },
        },
      ]);

      // Check if model exists
      if (!glbModel || glbModel.length === 0) {
        throw new expressError(
          "3D model not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "3D model retrieved successfully",
        true,
        glbModel
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid model ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  getGlbModelByProjectId = async (req, res) => {
    const { project } = req.body;

    // Validate required fields
    if (!project) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!project.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid project ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find models by project ID
    let projectToBeRetrieved = await glbSchema
      .find({ project })
      .populate([
        "project",
        "category",
        "collectionId",
        "asset_library",
        "customizations.values.icon_id",
      ]);

    // Check if models exist
    if (!projectToBeRetrieved || projectToBeRetrieved.length === 0) {
      throw new expressError(
        "No 3D models found for this project",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D models retrieved successfully",
      true,
      projectToBeRetrieved
    );
  };
  getGlbModelWithProject = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find model by ID and populate project
    let glbModelToBeRetrieved = await glbSchema
      .findOne({ _id: id })
      .populate("project");

    // Check if model exists
    if (!glbModelToBeRetrieved) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  getGlbModelWithCategory = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find model by ID and populate category
    let glbModelToBeRetrieved = await glbSchema
      .findOne({ _id: id })
      .populate("category");

    // Check if model exists
    if (!glbModelToBeRetrieved) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  getGlbModelWithCollectionId = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find model by ID and populate collection
    let glbModelToBeRetrieved = await glbSchema
      .findOne({ _id: id })
      .populate("collectionId");

    // Check if model exists
    if (!glbModelToBeRetrieved) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  getGlbModelWithAsset = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find model by ID and populate asset library
    let glbModelToBeRetrieved = await glbSchema
      .findOne({ _id: id })
      .populate("asset_library");

    // Check if model exists
    if (!glbModelToBeRetrieved) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  getGlbModelWithAllInformations = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find model by ID and populate all references
    let glbModelToBeRetrieved = await glbSchema
      .findOne({ _id: id })
      .populate("project")
      .populate("category")
      .populate("collectionId")
      .populate("asset_library");

    // Check if model exists
    if (!glbModelToBeRetrieved) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  getGlbLinkWithHdrById = async (req, res) => {
    const id = req.params.id;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find model by ID
    const glbModelToBeRetrieved = await glbSchema.findById(id);

    // Check if model exists
    if (!glbModelToBeRetrieved) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model retrieved successfully",
      true,
      glbModelToBeRetrieved
    );
  };
  getModelsByUserIdParams = async (req, res) => {
    const { userId } = req.params;

    // Validate required fields
    if (!userId) {
      throw new expressError(
        "User ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!userId.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid user ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find projects for user
    const userProjects = await projectModel
      .find({
        client: mongoose.Types.ObjectId(userId),
      })
      .select("_id")
      .lean();

    // Check if projects exist
    if (!userProjects || userProjects.length === 0) {
      throw new expressError(
        "No projects found for this user",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Find models for projects
    const glbModels = await glbModelModel.find({
      project: { $in: userProjects.map((p) => p._id) },
    });

    // Check if models exist
    if (!glbModels || glbModels.length === 0) {
      throw new expressError(
        "No 3D models found for this user's projects",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D models retrieved successfully",
      true,
      glbModels
    );
  };
  updateGlbModelById = async (req, res) => {
    const { _id } = req.body;

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!_id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Update model
    let glbModelToBeUpdated = await glbSchema.findByIdAndUpdate(
      _id,
      { ...req.body },
      { runValidators: true }
    );

    // Check if model exists
    if (!glbModelToBeUpdated) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch updated model
    let glbModelUpdated = await glbSchema.findById(_id);

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model updated successfully",
      true,
      glbModelUpdated
    );
  };

  updateProductWithFiles = async (req, res) => {
    try {
      const userId = req.user;
      const { productId } = req.params;
      const bodyData = req.body;

      // Check if the product exists and belongs to the user
      const projectToBeFound = await projectModel
        .findOne({ client: userId })
        .lean();
      if (!projectToBeFound) {
        return res
          .status(404)
          .json({ success: false, message: "Project not found" });
      }

      // Find the product
      const product = await glbModelModel.findOne({
        _id: productId,
        project: projectToBeFound._id,
      });

      if (!product) {
        return res.status(404).json({
          success: false,
          message:
            "Product not found or you don't have permission to update it",
        });
      }

      // Prepare the update data
      const updateData = {};

      // Update basic fields if provided
      if (bodyData.name) updateData.name = bodyData.name;
      if (bodyData.description) updateData.description = bodyData.description;
      if (bodyData.price) updateData.price = parseFloat(bodyData.price);
      if (bodyData.sku) updateData.sku = bodyData.sku;
      if (bodyData.published !== undefined)
        updateData.published = bodyData.published;
      if (bodyData.sizes) updateData.sizes = bodyData.sizes;
      if (bodyData.width) updateData.width = bodyData.width;
      if (bodyData.category) updateData.category = bodyData.category;
      if (bodyData.collectionId)
        updateData.collectionId = bodyData.collectionId;
      if (bodyData.customizations)
        updateData.customizations = bodyData.customizations;
      if (bodyData.variants) updateData.variants = bodyData.variants;
      if (bodyData.hdr) updateData.hdr = bodyData.hdr;

      // Handle 3D model file upload (url field)
      if (req.files && req.files.url && req.files.url.length > 0) {
        const data = await uploadToS3(req.files.url[0]);
        updateData.url = data[0]?.Location;
      }

      // Handle image upload (imageUrl field)
      if (req.files && req.files.imageUrl && req.files.imageUrl.length > 0) {
        const imageData = await uploadToS3(req.files.imageUrl[0]);
        updateData.imageUrl = imageData[0]?.Location;
      }

      // Update the product
      const updatedProduct = await glbModelModel.findByIdAndUpdate(
        productId,
        { $set: updateData },
        { new: true }
      );

      return res.status(200).json({
        success: true,
        message: "Product updated successfully",
        data: updatedProduct,
      });
    } catch (error) {
      console.error("Error updating product:", error);
      return res.status(500).json({
        success: false,
        message: "An error occurred while updating the product",
        error: error.message,
      });
    }
  };
  deleteGlbModel = async (req, res) => {
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Model ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid model ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete model
    let glbModelToBeDeleted = await glbSchema.findByIdAndDelete(id);

    // Check if model exists
    if (!glbModelToBeDeleted) {
      throw new expressError(
        "3D model not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "3D model deleted successfully",
      true
    );
  };
  deleteProductFromParams = async (req, res) => {
    try {
      const { id } = req.params;

      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return resHandle.handleError(
          res,
          400,
          "Invalid product ID format",
          false,
          "INVALID_DATA_FORMAT"
        );
      }

      // Find the model first to check if it exists and belongs to the user
      const modelToCheck = await glbSchema.findById(id).populate("project");

      if (!modelToCheck) {
        return resHandle.handleError(
          res,
          404,
          "Product not found. It may have been already deleted.",
          false,
          "RESOURCE_NOT_FOUND"
        );
      }

      // Check if the user has permission to delete this model
      if (
        modelToCheck.project &&
        modelToCheck.project.client &&
        modelToCheck.project.client.toString() !== req.user
      ) {
        return resHandle.handleError(
          res,
          403,
          "You don't have permission to delete this product",
          false,
          "PERMISSION_DENIED"
        );
      }

      // Delete the model
      const glbModelToBeDeleted = await glbSchema.findByIdAndDelete(id);

      // This should never happen since we already checked, but just in case
      if (!glbModelToBeDeleted) {
        return resHandle.handleError(
          res,
          404,
          "Product not found",
          false,
          "RESOURCE_NOT_FOUND"
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Product deleted successfully",
        true
      );
    } catch (err) {
      console.error("Error deleting product:", err);
      return resHandle.handleError(
        res,
        500,
        "An error occurred while deleting the product",
        false,
        "SERVER_ERROR"
      );
    }
  };
  createGlbModelUpdated = async (req, res) => {
    try {
      // Validate required fields
      if (!req.body.name) {
        return resHandle.handleError(
          res,
          400,
          "Product name is required",
          false,
          "MISSING_REQUIRED_FIELD"
        );
      }

      if (!req.files || !req.files.file || !req.files.file.length) {
        return resHandle.handleError(
          res,
          400,
          "3D model file is required",
          false,
          "MISSING_REQUIRED_FIELD"
        );
      }

      const userId = req.user;

      // Find or create a default project for the user
      let projectToBeFound = await projectModel
        .findOne({ client: userId })
        .lean();

      if (!projectToBeFound) {
        try {
          const newProject = new projectModel({
            name: "Default Project Name",
            description: "This is a default project description.",
            industry: "General",
            client: userId,
            type: "Standard",
            type_of_integration: "Web Development",
            artists: [userId], // Add the designer as an artist
            template: null,
            status: "request",
            softDelete: false,
            isShowroom: false,
          });

          const savedProject = await newProject.save();
          projectToBeFound = savedProject.toObject();
        } catch (projectError) {
          console.error("Error creating default project:", projectError);
          return resHandle.handleError(
            res,
            500,
            "Failed to create default project",
            false,
            "DATABASE_ERROR"
          );
        }
      } else {
        // If project exists, ensure the user is in the artists array
        if (!projectToBeFound.artists || !projectToBeFound.artists.includes(userId)) {
          await projectModel.findByIdAndUpdate(
            projectToBeFound._id,
            { $addToSet: { artists: userId } }
          );
          projectToBeFound = await projectModel.findById(projectToBeFound._id).lean();
        }
      }

      const bodyData = req.body;
      let inputs = {
        name: bodyData.name,
        description: bodyData.description || "",
        sku: bodyData.sku || bodyData.description || `SKU-${Date.now()}`,
        price: bodyData.price || 0,
        project: projectToBeFound._id,
        createdBy: userId, // Add the creator's ID
      };

      // Handle 3D model file upload (required)
      try {
        const data = await uploadToS3(req.files.file[0]);
        if (!data || !data[0] || !data[0].Location) {
          return resHandle.handleError(
            res,
            500,
            "Failed to upload 3D model file",
            false,
            "FILE_UPLOAD_ERROR"
          );
        }
        inputs.url = data[0].Location;
      } catch (uploadError) {
        console.error("Error uploading 3D model:", uploadError);
        return resHandle.handleError(
          res,
          500,
          "Failed to upload 3D model: " +
            (uploadError.message || "Unknown error"),
          false,
          "FILE_UPLOAD_ERROR"
        );
      }

      // Handle image upload (optional)
      if (req.files.imageUrl && req.files.imageUrl.length > 0) {
        try {
          const imageData = await uploadToS3(req.files.imageUrl[0]);
          if (imageData && imageData[0] && imageData[0].Location) {
            inputs.imageUrl = imageData[0].Location;
          }
        } catch (imageUploadError) {
          // Don't fail the whole request if just the image upload fails
          console.error(
            "Error uploading image (continuing):",
            imageUploadError
          );
        }
      }

      // Create and save the new model
      try {
        let newGlb = new glbModelModel(inputs);
        await newGlb.save();

        return resHandle.handleData(
          res,
          201, // Created
          "Product created successfully",
          true,
          newGlb
        );
      } catch (saveError) {
        console.error("Error saving model:", saveError);

        // Handle validation errors
        if (saveError.name === "ValidationError") {
          const errors = Object.values(saveError.errors)
            .map((err) => err.message)
            .join(", ");
          return resHandle.handleError(
            res,
            400,
            `Validation failed: ${errors}`,
            false,
            "VALIDATION_ERROR"
          );
        }

        // Handle duplicate key errors
        if (saveError.code === 11000) {
          const field = Object.keys(saveError.keyValue)[0];
          return resHandle.handleError(
            res,
            400,
            `A product with this ${field} already exists`,
            false,
            "DUPLICATE_ENTRY"
          );
        }

        // Handle other errors
        return resHandle.handleError(
          res,
          500,
          "Failed to save product",
          false,
          "DATABASE_ERROR"
        );
      }
    } catch (error) {
      console.error("Unexpected error in createGlbModelUpdated:", error);
      return resHandle.handleError(
        res,
        500,
        "An unexpected error occurred while creating the product",
        false,
        "SERVER_ERROR"
      );
    }
  };
}

const controller = new Controller();
module.exports = controller;
