const userSchema = require("../models/User");
const roleSchema = require("../models/Role");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");

class Controller {
  getAllDesigners = async (req, res) => {
    // Find Designer role
    const roleToBeFetched = await roleSchema.findOne({ role: "Designer" });

    // Check if Designer role exists
    if (!roleToBeFetched) {
      throw new expressError(
        "Designer role not found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Find all users with Designer role
    const usersToBeFetched = await userSchema.find({
      role: roleToBeFetched._id,
    });

    // Check if any designers exist
    if (!usersToBeFetched || usersToBeFetched.length === 0) {
      throw new expressError(
        "No designers found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Designers retrieved successfully",
      true,
      usersToBeFetched
    );
  };
  getAllClients = async (req, res) => {
    // Find Client role
    const roleToBeFetched = await roleSchema.findOne({ role: "Client" });

    // Check if Client role exists
    if (!roleToBeFetched) {
      throw new expressError(
        "Client role not found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Find all users with Client role
    const usersToBeFetched = await userSchema.find({
      role: roleToBeFetched._id,
    });

    // Check if any clients exist
    if (!usersToBeFetched || usersToBeFetched.length === 0) {
      throw new expressError(
        "No clients found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Clients retrieved successfully",
      true,
      usersToBeFetched
    );
  };
}

const controller = new Controller();
module.exports = controller;
