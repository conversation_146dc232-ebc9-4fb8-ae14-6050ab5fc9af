const templateSchema = require("../models/Template");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");
class Controller {
  createTemplate = async (req, res) => {
    // Extract template data from request body
    const {
      template_name,
      project_id,
      template_description,
      template_previewUrl,
      template_imageUrl,
    } = req.body;

    // Validate required fields
    if (!template_name) {
      throw new expressError(
        "Template name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!project_id) {
      throw new expressError(
        "Project ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Create new template
    const newTemplate = new templateSchema({
      template_name,
      project_id,
      template_description,
      template_previewUrl,
      template_imageUrl,
    });

    // Save template to database
    await newTemplate.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Template created successfully",
      true,
      newTemplate
    );
  };
  getAllTemplate = async (req, res) => {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await templateSchema.countDocuments();

    // Check if templates exist
    if (totalCount === 0) {
      throw new expressError(
        "No templates found in the system",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch templates with pagination
    const allTemplates = await templateSchema.find({}).skip(skip).limit(limit);

    // Return paginated response
    return resHandle.handlePaginatedData(
      res,
      200,
      "Templates retrieved successfully",
      true,
      allTemplates,
      {
        total: totalCount,
        page: page,
        perPage: limit,
      }
    );
  };
  getTemplateById = async (req, res) => {
    // Get template ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Template ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid template ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find template by ID
    const templateToBeRetrieved = await templateSchema.findOne({ _id: id });

    // Check if template exists
    if (!templateToBeRetrieved) {
      throw new expressError(
        "Template not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Template retrieved successfully",
      true,
      templateToBeRetrieved
    );
  };
  getTemplateWithProject = async (req, res) => {
    // Get template ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Template ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid template ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find template by ID and populate project
    const templateToBeFound = await templateSchema
      .findOne({ _id: id })
      .populate("project_id");

    // Check if template exists
    if (!templateToBeFound) {
      throw new expressError(
        "Template not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Template with project details retrieved successfully",
      true,
      templateToBeFound
    );
  };
  deleteTemplateById = async (req, res) => {
    // Get template ID from request body
    const { id } = req.body;

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Template ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid template ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Delete template
    const templateToBeDeleted = await templateSchema.findByIdAndDelete(id);

    // Check if template exists
    if (!templateToBeDeleted) {
      throw new expressError(
        "Template not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Template deleted successfully",
      true
    );
  };
}

const controller = new Controller();
module.exports = controller;
