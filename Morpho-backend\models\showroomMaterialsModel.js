const mongoose = require("mongoose");

const showroomMaterialSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    albedoColor: {
      r: Number,
      g: Number,
      b: Number,
    },
    metallic: Number,
    roughness: Number,
    alpha: Number,
    transparencyMode: Number,
    albedoTextureLink: {
      type: String,
    },
    category: {
      type: mongoose.Types.ObjectId,
      ref: "ShowroomCategory",
    },
  },
  { timestamps: true, colletion: "showroomMaterials" },
);

const showroomMaterialModel = mongoose.model(
  "ShowroomMaterial",
  showroomMaterialSchema,
);
module.exports = showroomMaterialModel;
