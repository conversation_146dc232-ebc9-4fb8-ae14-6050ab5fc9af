const router = require("express").Router();
const controller = require("../../controllers/showroomController");
const { upload } = require("../../middleware/multer-middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.post(
  "/create",

  upload.fields([
    { name: "url", maxCount: 1 },
    { name: "debugUrl", maxCount: 1 },
    { name: "imagesUrl", maxCount: 4 },
    { name: "iconImageUrl", maxCount: 1 },
  ]),
  asyncError<PERSON>and<PERSON>(controller.createShowroom)
);
router.get("/get-by-sku/:sku", asyncError<PERSON>and<PERSON>(controller.getBySku));
router.get(
  "/get-by-sku-editor/:sku",
  asyncError<PERSON>andler(controller.getBySkuEditor)
);
router.get(
  "/get-all-categories",
  asyncError<PERSON><PERSON><PERSON>(controller.getCategoriesOfShowroom)
);
router.get("/get-all", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.getAll));
router.put(
  "/update/:id",
  upload.fields([
    { name: "url", maxCount: 1 },
    { name: "debugUrl", maxCount: 1 },
    { name: "imagesUrl", maxCount: 4 },
    { name: "iconImageUrl", maxCount: 1 },
  ]),
  asyncErrorHandler(controller.updateById)
);
router.delete("/delete/:id", asyncErrorHandler(controller.deleteById));
module.exports = router;
