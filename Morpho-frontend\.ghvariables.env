################ UNIVERSAL VALUES HERE ################
#######################################################

############## NODE CREDENTIALS #######################
NODE_VERSION=18.16.0
#######################################################

############## FRONTEND SECRETS #######################
AWS_FRONTEND_SSM_ROLE_PATH=/slaves/frontend/role-arn
AWS_S3_BUILD_BUCKET=frontend-built-websites
AWS_AMPLIFY_STAGING_APP_NAME=Staging-Web-Apps
AWS_AMPLIFY_PRODUCTION_APP_NAME=Frontend-Web-Apps
AWS_AMPLIFY_STAGING_RESOURCE_NAME=MorphoDemo
AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME=Morpho
AWS_AMPLIFY_PRODUCTION_DOMAIN=modularcx.io
AWS_AMPLIFY_PRODUCTION_SUBDOMAIN=morpho
#######################################################

