const webflow = require("../utils/webflow");
const GlbModel = require("../models/GlbModel");
const projectSchema = require("../models/Project");
const assetLibraryModel = require("../models/AssetLibrary");
const expressError = require("../errors/expressError");
const resHandle = require("../utils/responseHandle");

// Fetch all products from Webflow
exports.getAllProducts = async (req, res, next) => {
  // Validate webflow client
  if (!req.webflowClient) {
    throw new expressError(
      "Webflow client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Parse limit parameter
  const limit = parseInt(req.query.limit, 10) || 5;

  // Fetch items from Webflow
  const response = await req.webflowClient.get();
  const products = response.data.items;

  // Check if products exist
  if (!products || products.length === 0) {
    throw new expressError(
      "No items found in the collection",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Return success response
  return resHandle.handleData(
    res,
    200,
    "Products retrieved successfully",
    true,
    products.slice(0, limit)
  );
};

//doesnt work as intended cz Webflow's API doesn't provide direct endpoints for orders or order items
exports.getStatistics = async (req, res, next) => {
  // Validate webflow client
  if (!req.webflowClient) {
    throw new expressError(
      "Webflow client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Fetch all orders (or order-like data) from Webflow
  const ordersResponse = await req.webflowClient.get();
  const orders = ordersResponse.data.items;

  // Check if orders exist
  if (!orders || orders.length === 0) {
    throw new expressError(
      "No orders found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Aggregate data for statistics
  const productStats = {};
  let totalOrders = 0;

  orders.forEach((order) => {
    totalOrders++;

    // Make sure the fieldData has the required fields
    const fieldData = order.fieldData || {};

    const productId = fieldData["product-id-2"]; // Adjust this based on your Webflow field names
    const productName = fieldData["product-name"] || fieldData.name; // Adjust based on your Webflow field names
    const quantity = fieldData.quantity || 1; // Default quantity to 1 if missing
    const price = fieldData["price-2"] || 0; // Price of the product

    // Handle if any of the required fields are missing
    if (!productId || !productName || !price) {
      console.error("Missing data for order:", order);
      return; // Skip this order if data is incomplete
    }

    // Aggregate product statistics
    if (!productStats[productId]) {
      productStats[productId] = {
        productId,
        name: productName,
        totalSold: 0,
        revenue: 0,
      };
    }

    productStats[productId].totalSold += quantity;
    productStats[productId].revenue += quantity * price;
  });

  // Convert the productStats object to an array and sort by totalSold (most sold first)
  const topProducts = Object.values(productStats).sort(
    (a, b) => b.totalSold - a.totalSold
  );

  // Return success response
  return resHandle.handleData(
    res,
    200,
    "Statistics retrieved successfully",
    true,
    {
      totalOrders,
      topProducts,
    }
  );
};

exports.saveAllProducts = async (req, res, next) => {
  const userId = req.user;

  // Validate user authentication
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Validate webflow client
  if (!req.webflowClient) {
    throw new expressError(
      "Webflow client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Get the user's project
  const project = await projectSchema.findOne({ client: userId }).lean();
  if (!project) {
    throw new expressError(
      "Project not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Get the asset library
  const assetLibrary = await assetLibraryModel.findOne({
    project: project._id,
  });

  if (!assetLibrary) {
    throw new expressError(
      "Asset library not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  const assetLibraryId = assetLibrary._id;
  const projectId = project._id;

  // Fetch products from Webflow
  const response = await req.webflowClient.get();
  const products = response.data.items;

  // Check if the response contains products
  if (!Array.isArray(products)) {
    throw new expressError(
      "Expected products to be an array",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Arrays to track new and skipped products
  const skippedProducts = [];
  const savedProducts = [];

  for (const product of products) {
    // Extract values from `fieldData`
    const fieldData = product.fieldData || {};

    // Map the correct fields from Webflow to your database schema
    const productId = fieldData["product-id-2"];
    const productName = fieldData["name"] || "";
    const productPrice = parseFloat(fieldData["price-2"]) || 0;
    const productSku = fieldData["sku"] || "";
    const productImage = fieldData["image"]?.[0]?.url || "";

    // If the image is just a relative URL, we need to convert it to an absolute URL
    const imageUrl = productImage ? productImage : null;

    // Skip products without images
    if (!imageUrl) {
      skippedProducts.push({
        name: productName,
        id: productId,
        reason: "Missing image URL.",
      });
      continue;
    }

    // Skip existing products
    const existingProduct = await GlbModel.findOne({
      integrationKey: String(productId),
    });
    if (existingProduct) {
      skippedProducts.push({
        name: productName,
        id: productId,
        reason: "Product already exists.",
      });
      continue;
    }

    // Create and save new product
    const newProduct = new GlbModel({
      name: productName,
      description: fieldData["description"] || "No description available",
      imageUrl: imageUrl,
      price: productPrice,
      sku: productSku,
      integrationKey: String(productId),
      published: true,
      project: projectId,
      asset_library: assetLibraryId,
      // Add other fields as necessary
    });
    await newProduct.save();
    savedProducts.push(newProduct);
  }

  // Return success response
  return resHandle.handleData(
    res,
    200,
    `${savedProducts.length} product(s) saved successfully`,
    true,
    {
      savedProducts,
      skippedProducts,
    }
  );
};
