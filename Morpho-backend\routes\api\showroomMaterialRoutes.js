const router = require("express").Router();
const controller = require("../../controllers/showroomMaterialController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
const { upload } = require("../../middleware/multer-middleware");

router.post(
  "/create",
  upload.fields([{ name: "albedoTextureLink", maxCount: 1 }]),
  async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.createShowroomMaterial)
);
router.get("/get-all", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.getAllShowroomMaterials));
router.get("/get-by-id/:id", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.getById));
router.get(
  "/get-categories",
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getCategoriesOfMaterials)
);
router.put("/update-by-id/:id", async<PERSON>rror<PERSON><PERSON><PERSON>(controller.updateById));
router.delete("/delete-by-id/:id", async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.deleteById));

module.exports = router;
