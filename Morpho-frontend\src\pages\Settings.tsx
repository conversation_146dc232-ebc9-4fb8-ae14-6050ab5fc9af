import React from "react";
import { <PERSON>, <PERSON>, <PERSON>lette, Globe, CreditCard } from "lucide-react";
import { Link } from "react-router-dom";

const Settings = () => {
  const sections = [
    {
      title: "Account",
      icon: Lock,
      settings: [
        {
          name: "Two-Factor Authentication",
          description: "Add an extra layer of security to your account",
          enabled: true,
        },
        {
          name: "Password",
          description: "Change your password",
          action: "Change",
        },
      ],
    },
    {
      title: "Notifications",
      icon: Bell,
      settings: [
        {
          name: "Email Notifications",
          description: "Receive updates about your 3D models and requests",
          enabled: true,
        },
        {
          name: "Browser Notifications",
          description: "Get notified when changes are made to your projects",
          enabled: false,
        },
      ],
    },
    {
      title: "Appearance",
      icon: Palette,
      settings: [
        {
          name: "Theme",
          description: "Choose between light and dark mode",
          value: "Dark",
        },
      ],
    },
    {
      title: "Billing",
      icon: CreditCard,
      settings: [
        {
          name: "Current Plan",
          description: "Enterprise Plan - $400/month",
          action: "Manage",
          link: "/subscription",
        },
        {
          name: "Payment Method",
          description: "•••• •••• •••• 4242",
          action: "Update",
        },
      ],
    },
  ];

  return (
    <div>
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-light tracking-wide text-gray-100">
          Settings
        </h1>
      </div>

      <div className="mt-8 space-y-8">
        {sections.map((section) => {
          const Icon = section.icon;
          return (
            <div
              key={section.title}
              className="card rounded-xl overflow-hidden"
            >
              <div className="p-5 border-b border-gray-800">
                <div className="flex items-center gap-3">
                  <Icon
                    size={20}
                    strokeWidth={1.5}
                    className="text-indigo-400"
                  />
                  <h2 className="text-xl font-light tracking-wide text-gray-100">
                    {section.title}
                  </h2>
                </div>
              </div>
              <div className="divide-y divide-gray-800">
                {section.settings.map((setting) => (
                  <div
                    key={setting.name}
                    className="p-5 flex items-center justify-between"
                  >
                    <div>
                      <h3 className="text-gray-100 font-light">
                        {setting.name}
                      </h3>
                      <p className="text-sm text-gray-400 mt-1 font-light">
                        {setting.description}
                      </p>
                    </div>
                    {"enabled" in setting ? (
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={
                            "enabled" in setting ? setting.enabled : false
                          }
                          onChange={() => {}}
                        />
                        <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-500"></div>
                      </label>
                    ) : "link" in setting ? (
                      <Link
                        to={setting.link || "#"}
                        className="px-4 py-2 rounded-lg bg-dark-300 border border-gray-700/50 text-gray-300 hover:border-indigo-500/50 transition-colors text-sm font-light inline-block"
                      >
                        {setting.action}
                      </Link>
                    ) : (
                      <button className="px-4 py-2 rounded-lg bg-dark-300 border border-gray-700/50 text-gray-300 hover:border-indigo-500/50 transition-colors text-sm font-light">
                        {setting.action}
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Settings;
