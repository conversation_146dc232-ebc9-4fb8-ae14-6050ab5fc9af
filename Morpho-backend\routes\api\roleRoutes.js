const router = require("express").Router();
const controller = require("../../controllers/roleController");
const verifyRole = require("../../middleware/verifyRole");
const verifyJWT = require("../../middleware/verifyJWT");
const { designer, admin, client } = require("../../constants");
const { validateRoleSchema } = require("../../validation/middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
router.get(
  "/get-all-roles",
  verifyJWT,
  verifyRole([admin]),
  asyncError<PERSON>and<PERSON>(controller.getAllRoles)
);
router.get(
  "/get-role-informations",
  verifyJWT,
  verifyRole([admin]),
  asyncError<PERSON>andler(controller.getRoleForSignup)
);
router.get("/get-roles", asyncErrorHandler(controller.getRolesToBeUsed));
router
  .route("/create-role")
  .post(
    validateRoleSchema,
    verifyRole([admin]),
    async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.createRole)
  );

router.delete(
  "/delete-role",
  verifyJWT,
  verifyRole([admin]),
  asyncErrorHandler(controller.deleteRole)
);
router.put(
  "/soft-delete",
  verifyJWT,
  verifyRole([admin]),
  asyncErrorHandler(controller.softDeleteRole)
);
router.put(
  "/update-role",
  verifyJWT,
  verifyRole([admin]),
  asyncErrorHandler(controller.updateRoleById)
);
module.exports = router;
