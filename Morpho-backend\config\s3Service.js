const env = require("dotenv").config({ path: "./.env" });
const { S3 } = require("aws-sdk");
const FormData = require("form-data");

const {
  S3Client,
  ListObjectsV2Command,
  PutObjectCommand,
} = require("@aws-sdk/client-s3");

const { Upload } = require("@aws-sdk/lib-storage");
const s3Client = new S3Client({ region: "eu-central-1" });

exports.s3Upload = async (file, folder) => {
  const s3 = new S3();

  const bucketName = process.env.AWS_BUCKET_NAME;

  // Determine the folder path if provided
  const folderPath = folder ? `${folder}/` : "";
  const key = `showroom/${folderPath}${file.originalname}`;
  try {
    const params = {
      Bucket: bucketName,
      Key: key, // Include folder in the key if present
      Body: file.buffer,
      ContentType: file.mimetype, // Ensure content type is set
    };
    // Upload the file
    const result = await s3.upload(params).promise();
    return result;
  } catch (error) {
    console.error("Upload error:", error);
    throw error;
  }
};
exports.uploadToS3 = async (file) => {
  const formData = new FormData();
  formData.append("bucketName", "morphobucket");
  formData.append("folderName", "Publish");
  formData.append("file", file.buffer, { filename: file.originalname });
  const formDataBuffer = formData.getBuffer();
  const formDataHeader = formData.getHeaders();
  const requestOptions = {
    method: "POST",
    body: formDataBuffer,
    headers: { ...formDataHeader },
  };
  const response = await fetch(
    "https://api.modularcx.link/global-functions-api/s3/upload-to-s3",
    requestOptions
  );
  // Parsing the response JSON
  const responseData = await response.json();
  return responseData.data;
};

exports.uploadToS3WithConversion = async (file) => {
  const formdata = new FormData();
  formdata.append("usdz", file.buffer, { filename: file.originalname });
  const formDataBuffer = formdata.getBuffer();
  const formDataHeader = formdata.getHeaders();

  const requestOptions = {
    method: "POST",
    body: formDataBuffer,
    headers: { ...formDataHeader },
  };
  const response = await fetch(
    "https://api.modularcx.link/conversion/ktx/test",
    requestOptions
  );
  // Parsing the response JSON
  const responseData = await response.json();
  return responseData;
};
exports.uploadToS3NewVersion = async (file, folder, folderPathh) => {
  const bucketName = "morphobucket";

  // Ensure folderPathh does not end with a slash
  const cleanedFolderPathh = folderPathh.endsWith("/")
    ? folderPathh.slice(0, -1)
    : folderPathh;

  // Construct folderPath without repeating folder name
  const folderPath = `${cleanedFolderPathh}/${folder}`;
  const filePath = `${folderPath}/${file.originalname}`;

  // Check if the folder exists by listing objects with the folder prefix
  try {
    const listParams = {
      Bucket: bucketName,
      Prefix: folderPath,
      MaxKeys: 1,
    };
    const listCommand = new ListObjectsV2Command(listParams);
    const listResponse = await s3Client.send(listCommand);

    if (!listResponse.Contents || listResponse.Contents.length === 0) {
      // Create the folder by uploading an empty object
      const createFolderParams = {
        Bucket: bucketName,
        Key: folderPath + "/", // Folder path should end with a '/'
        Body: "",
      };
      const putFolderCommand = new PutObjectCommand(createFolderParams);
      await s3Client.send(putFolderCommand);
    }
  } catch (error) {
    console.error("Error checking or creating folder: ", error);
    throw error;
  }

  // Upload the file using Upload class from @aws-sdk/lib-storage
  try {
    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: bucketName,
        Key: filePath,
        Body: file.buffer, // Use a stream or buffer here
        ContentType: file.mimetype,
      },
    });

    const response = await upload.done();

    return {
      Location: `https://${bucketName}.s3.amazonaws.com/${filePath}`,
    };
  } catch (error) {
    console.error("Error uploading file: ", error);
    throw error;
  }
};
