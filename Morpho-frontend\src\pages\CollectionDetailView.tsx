import React, { useState } from "react";
import { ChevronLeft, Trash2, X, Trash } from "lucide-react";
import { Collection } from "../hooks/useCollection";
import ModelCard from "../components/ModelCard";
import { api } from "../services/api";
import { toast } from "react-hot-toast";

interface CollectionDetailViewProps {
  collection: Collection;
  onBack: () => void;
  onRefresh: () => void;
  viewMode: "grid" | "list";
}

const CollectionDetailView: React.FC<CollectionDetailViewProps> = ({
  collection,
  onBack,
  onRefresh,
  viewMode,
}) => {
  const [isRemoving, setIsRemoving] = useState(false);
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showDeleteCollectionConfirm, setShowDeleteCollectionConfirm] =
    useState(false);
  const [isDeletingCollection, setIsDeletingCollection] = useState(false);

  const handleRemoveFromCollection = async (modelId: string) => {
    setSelectedModelId(modelId);
    setShowConfirmation(true);
  };

  const confirmRemove = async () => {
    if (!selectedModelId) return;

    try {
      setIsRemoving(true);
      await api.removeModelFromCollection(collection._id, selectedModelId);
      toast.success("Model removed from collection");
      setShowConfirmation(false);
      setSelectedModelId(null);
      onRefresh(); // Refresh the collection data
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to remove model from collection",
      );
    } finally {
      setIsRemoving(false);
    }
  };

  // Add handler for delete collection

  // In CollectionDetailView.tsx
  // In CollectionDetailView.tsx
  const handleDeleteCollection = async () => {
    try {
      setIsDeletingCollection(true);

      // Remember the ID of the collection we're deleting
      const deletedCollectionId = collection._id;

      // Delete the collection
      await api.deleteCollection(deletedCollectionId);

      // Show success message
      toast.success("Collection deleted successfully");

      // Navigate back BEFORE any further API calls can happen
      onBack();

      // Optional: We can also pass the deleted ID back to the parent
      // if you want to implement special handling
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to delete collection",
      );
      setIsDeletingCollection(false);
      setShowDeleteCollectionConfirm(false);
    }
  };

  return (
    <div>
      <div className="mb-6 p-5 rounded-lg border border-gray-800 bg-dark-300">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-light text-gray-100 mb-2">
              {collection.name}
            </h2>
            <p className="text-gray-400 text-sm">{collection.description}</p>
            <p className="text-gray-500 text-xs mt-2">
              {Array.isArray(collection.products)
                ? collection.products.length
                : 0}{" "}
              {collection.products && collection.products.length === 1
                ? "model"
                : "models"}{" "}
              in this collection
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowDeleteCollectionConfirm(true)}
              className="btn btn-secondary flex items-center gap-2 text-red-400 hover:text-red-300"
            >
              <Trash size={16} strokeWidth={1.5} />
              <span>Delete Collection</span>
            </button>
            <button
              onClick={onBack}
              className="btn btn-secondary flex items-center gap-2 group"
            >
              <ChevronLeft size={18} strokeWidth={1.5} />
              <span>Back</span>
            </button>
          </div>
        </div>
      </div>

      {Array.isArray(collection.products) && collection.products.length > 0 ? (
        <div
          className={`mt-6 ${
            viewMode === "grid"
              ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6"
              : "space-y-4"
          }`}
        >
          {collection.products.map((model: any) => (
            <ModelCard
              key={model._id}
              model={model}
              viewMode={viewMode}
              onPublish={() => {}}
              onDuplicate={() => {}}
              onDelete={() => handleRemoveFromCollection(model._id)}
              deleteButtonLabel="Remove from Collection"
            />
          ))}
        </div>
      ) : (
        <div className="mt-8 text-center">
          <div className="p-8 rounded-lg bg-dark-300/50 border border-gray-800/50">
            <p className="text-gray-400 font-light">
              No 3D models found in this collection
            </p>
          </div>
        </div>
      )}

      {/* Confirmation Modal for Removing a Model */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-dark-300 rounded-xl w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-light text-gray-100">
                Remove from Collection
              </h3>
              <button
                onClick={() => setShowConfirmation(false)}
                className="p-1 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors"
              >
                <X size={20} strokeWidth={1.5} />
              </button>
            </div>
            <p className="text-gray-300 mb-6">
              Are you sure you want to remove this model from the collection?
              This action won't delete the model from your library.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowConfirmation(false)}
                className="btn btn-secondary"
                disabled={isRemoving}
              >
                Cancel
              </button>
              <button
                onClick={confirmRemove}
                className="btn bg-red-500 hover:bg-red-600 text-white flex items-center gap-2"
                disabled={isRemoving}
              >
                {isRemoving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                    <span>Removing...</span>
                  </>
                ) : (
                  <>
                    <Trash2 size={16} strokeWidth={1.5} />
                    <span>Remove</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Collection Confirmation Modal */}
      {showDeleteCollectionConfirm && (
        <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-dark-300 rounded-xl w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-light text-gray-100">
                Delete Collection
              </h3>
              <button
                onClick={() => setShowDeleteCollectionConfirm(false)}
                className="p-1 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors"
              >
                <X size={20} strokeWidth={1.5} />
              </button>
            </div>
            <p className="text-gray-300 mb-6">
              Are you sure you want to delete the collection "{collection.name}
              "? This action cannot be undone. The models in this collection
              will not be deleted from your library.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowDeleteCollectionConfirm(false)}
                className="btn btn-secondary"
                disabled={isDeletingCollection}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteCollection}
                className="btn bg-red-500 hover:bg-red-600 text-white flex items-center gap-2"
                disabled={isDeletingCollection}
              >
                {isDeletingCollection ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                    <span>Deleting...</span>
                  </>
                ) : (
                  <>
                    <Trash2 size={16} strokeWidth={1.5} />
                    <span>Delete</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CollectionDetailView;
