const materialsModel = require("../models/Materials");
const pushObjectToArray = require("../utils/materialUtility");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");

class Controller {
  createMaterials = async (req, res) => {
    // Create new material
    let newMaterial = await materialsModel.create(req.body);

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Material Created Successfully",
      true,
      newMaterial
    );
  };
  getAllMaterials = async (req, res) => {
    // Fetch all materials
    const materialsToBeFetched = await materialsModel.find();

    // Check if materials exist
    if (!materialsToBeFetched || materialsToBeFetched.length === 0) {
      throw new expressError(
        "No materials found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Materials Fetched Successfully",
      true,
      materialsToBeFetched
    );
  };
  getMaterialById = async (req, res) => {
    const { _id } = req.body;

    // Validate material ID
    if (!_id) {
      throw new expressError(
        "Material ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Fetch material by ID
    const materialToBeFetched = await materialsModel.findById(_id);

    // Check if material exists
    if (!materialToBeFetched) {
      throw new expressError(
        "Material not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Material Retrieved Successfully",
      true,
      materialToBeFetched
    );
  };
  getMaterialByUserId = async (req, res) => {
    const userId = req.user;
    const userRole = req.role;

    // Validate user authentication
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Fetch materials by user ID
    let materialsToBeFetched = await materialsModel.find({ user: userId });

    // Filter materials for client role
    if (userRole === "Client") {
      materialsToBeFetched = materialsToBeFetched.map((doc) => {
        // Filter the materials array to include only active materials
        const activeMaterials = doc.material.filter(
          (material) => material.active === true
        );
        // Update the 'material' property to contain only active materials
        doc.material = activeMaterials;
        return doc;
      });
    }

    // Check if materials exist
    if (!materialsToBeFetched || materialsToBeFetched.length === 0) {
      throw new expressError(
        "No materials found for this user",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Materials Retrieved Successfully",
      true,
      materialsToBeFetched
    );
  };
  getMaterialByActive = async (req, res) => {
    const { _id } = req.body;

    // Validate material ID
    if (!_id) {
      throw new expressError(
        "Material ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Fetch material by ID
    let materialToBeFetched = await materialsModel
      .findById(_id)
      .select("material")
      .lean();

    // Check if material exists
    if (!materialToBeFetched) {
      throw new expressError(
        "Material not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Filter active materials
    let filteredMaterial = materialToBeFetched.material.filter(
      (mat) => mat.active === true
    );

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Material Retrieved Successfully",
      true,
      filteredMaterial
    );
  };
  addMaterialsById = async (req, res) => {
    const { _id, name, color, size, version, active } = req.body;

    // Validate required fields
    if (!_id || !name) {
      throw new expressError(
        "Material ID and name are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find material by ID
    let materialToBeUpdated = await materialsModel.findById(_id);

    // Check if material exists
    if (!materialToBeUpdated) {
      throw new expressError(
        "Material not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Create object to push
    let objectToBePushed = {
      name: name,
      version: version,
      properties: { color: color, size: size },
    };

    // Update material
    const updatedMaterial = pushObjectToArray(
      materialToBeUpdated.material,
      objectToBePushed,
      active
    );

    // Save updated material
    await updatedMaterial.save();

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Material Updated Successfully",
      true,
      updatedMaterial
    );
  };
  updateMaterialNameById = async (req, res) => {
    const { _id, name } = req.body;

    // Validate required fields
    if (!_id || !name) {
      throw new expressError(
        "Material ID and name are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find material by ID
    let materialToBeUpdated = await materialsModel.findOne({
      "material._id": _id,
    });

    // Check if material exists
    if (!materialToBeUpdated) {
      throw new expressError(
        "Material not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Find material index
    const materialIndex = materialToBeUpdated.material.findIndex(
      (material) => material._id == _id
    );

    // Update material name
    materialToBeUpdated.material[materialIndex].name = name;

    // Save updated material
    await materialToBeUpdated.save();

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Material Updated Successfully",
      true,
      materialToBeUpdated
    );
  };
  deleteMaterialById = async (req, res) => {
    const { _id } = req.body;

    // Validate material ID
    if (!_id) {
      throw new expressError(
        "Material ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Delete material by ID
    const materialToBeDeleted = await materialsModel.findByIdAndDelete(_id);

    // Check if material exists
    if (!materialToBeDeleted) {
      throw new expressError(
        "Material not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Material Deleted Successfully",
      true,
      materialToBeDeleted
    );
  };
}

const controller = new Controller();
module.exports = controller;
