import React, { useState, useEffect, useRef } from 'react';
import { Mic, MicOff } from 'lucide-react';

interface VoiceControlProps {
  commands: Record<string, () => void>;
  onListen?: (isListening: boolean) => void;
  onResult?: (transcript: string) => void;
  activationPhrase?: string;
  className?: string;
  buttonSize?: number;
}

export const VoiceControl: React.FC<VoiceControlProps> = ({
  commands,
  onListen,
  onResult,
  activationPhrase = 'hey morpho',
  className = '',
  buttonSize = 24,
}) => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const recognitionRef = useRef<any>(null);
  const timeoutRef = useRef<number | null>(null);
  // Tracking if component is mounted to prevent state updates after unmount
  const isMounted = useRef(true);

  // Setup speech recognition only when button is clicked to save resources
  const initSpeechRecognition = () => {
    if (recognitionRef.current) return;
    
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      console.warn('Speech recognition is not supported in this browser.');
      return;
    }

    // @ts-ignore - Browser compatibility
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognitionRef.current = new SpeechRecognition();
    recognitionRef.current.continuous = true;
    recognitionRef.current.interimResults = true;
    recognitionRef.current.lang = 'en-US';

    recognitionRef.current.onstart = () => {
      if (!isMounted.current) return;
      setIsListening(true);
      if (onListen) onListen(true);
    };

    recognitionRef.current.onend = () => {
      if (!isMounted.current) return;
      setIsListening(false);
      if (onListen) onListen(false);
    };

    recognitionRef.current.onresult = (event: any) => {
      if (!isMounted.current) return;
      
      const result = event.results[event.results.length - 1];
      const resultTranscript = result[0].transcript.toLowerCase().trim();
      
      setTranscript(resultTranscript);
      if (onResult) onResult(resultTranscript);
      
      // Process commands
      if (result.isFinal) {
        processCommand(resultTranscript);
      }
    };

    recognitionRef.current.onerror = (event: any) => {
      if (!isMounted.current) return;
      console.error('Speech recognition error:', event.error);
      setIsListening(false);
      if (onListen) onListen(false);
    };
  };

  useEffect(() => {
    // Mark component as mounted
    isMounted.current = true;
    
    // Cleanup on unmount
    return () => {
      isMounted.current = false;
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Process voice commands
  const processCommand = (text: string) => {
    if (text.includes(activationPhrase.toLowerCase())) {
      // Activation phrase detected
      for (const [command, callback] of Object.entries(commands)) {
        if (text.includes(command.toLowerCase())) {
          callback();
          break;
        }
      }
    }
  };

  // Toggle listening state
  const toggleListening = () => {
    if (isListening) {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    } else {
      initSpeechRecognition();
      if (recognitionRef.current) {
        recognitionRef.current.start();
        
        // Auto-stop after 30 seconds
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        timeoutRef.current = window.setTimeout(() => {
          if (recognitionRef.current && isListening) {
            recognitionRef.current.stop();
          }
        }, 30000);
      }
    }
  };

  return (
    <div className={`voice-control ${className}`}>
      <button
        onClick={toggleListening}
        className={`p-2 rounded-full transition-all duration-300 ${
          isListening 
            ? 'bg-blue-500 text-white shadow-glow-md animate-pulse' 
            : 'bg-[#12121A] text-gray-400 hover:bg-[#1A1A25] hover:text-blue-400'
        }`}
        aria-label={isListening ? 'Stop listening' : 'Start voice control'}
        title={isListening ? 'Stop listening' : 'Start voice control'}
      >
        {isListening ? (
          <Mic size={buttonSize} />
        ) : (
          <MicOff size={buttonSize} />
        )}
      </button>
      
      {isListening && (
        <div className="text-xs text-gray-400 mt-2 animate-pulse">
          Listening...
        </div>
      )}
    </div>
  );
};