import React, { useState, useRef } from 'react';
import { Bo<PERSON>, MessageSquare, X, User, Send } from 'lucide-react';
import { GlassContainer } from './GlassContainer';
import { Button } from './Button';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

const initialMessages: Message[] = [
  {
    id: '1',
    text: 'Hi there! I\'m the Morpho AI Assistant. How can I help you with 3D product visualization on your Shopify store?',
    sender: 'bot',
    timestamp: new Date(),
  },
];

export const AIAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Predefined responses for demo purposes
  const responses: Record<string, string> = {
    default: "I'd be happy to tell you more about how Morpho can help your Shopify store. Is there a specific feature you'd like to learn about?",
    pricing: "Morpho offers flexible pricing plans starting at $79/month for up to 50 3D models. Our most popular Growth plan is $149/month for up to 200 models. Enterprise plans with unlimited models are also available.",
    demo: "I'd be happy to show you a demo! You can book a personalized walkthrough with one of our 3D specialists using the 'Book a Demo' button on our pricing page.",
    integration: "Morpho integrates seamlessly with your Shopify store in just a few clicks. Our app automatically maps to your existing product catalog and variants, with no coding required.",
    scanning: "You can create 3D models using our iOS app on any iPhone or iPad with a LiDAR sensor. Simply scan your product from multiple angles, and our AI will generate an optimized 3D model ready for your store.",
    performance: "Morpho is designed for e-commerce performance. Our 3D models are automatically optimized to load in under 2 seconds, with negligible impact on your store's speed or SEO scores."
  };

  const simulateBotResponse = (userMessage: string) => {
    setIsBotTyping(true);
    
    setTimeout(() => {
      // Determine which response to use based on keywords in user message
      let responseText = responses.default;
      
      const userMessageLower = userMessage.toLowerCase();
      if (userMessageLower.includes('price') || userMessageLower.includes('cost') || userMessageLower.includes('plan')) {
        responseText = responses.pricing;
      } else if (userMessageLower.includes('demo') || userMessageLower.includes('show me')) {
        responseText = responses.demo;
      } else if (userMessageLower.includes('integrate') || userMessageLower.includes('install')) {
        responseText = responses.integration;
      } else if (userMessageLower.includes('scan') || userMessageLower.includes('create')) {
        responseText = responses.scanning;
      } else if (userMessageLower.includes('speed') || userMessageLower.includes('performance')) {
        responseText = responses.performance;
      }
      
      const newBotMessage: Message = {
        id: Date.now().toString(),
        text: responseText,
        sender: 'bot',
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, newBotMessage]);
      setIsBotTyping(false);
      scrollToBottom();
    }, 1500);
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;
    
    const newUserMessage: Message = {
      id: Date.now().toString(),
      text: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, newUserMessage]);
    setInputValue('');
    scrollToBottom();
    
    simulateBotResponse(inputValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      scrollToBottom();
    }
  };

  return (
    <>
      {/* Chat button */}
      <button
        onClick={toggleChat}
        className="fixed bottom-4 right-4 z-50 w-14 h-14 rounded-full bg-blue-400 text-white flex items-center justify-center shadow-glow-md hover:shadow-glow-lg transition-all duration-300 hover:scale-105"
        aria-label="Chat with Morpho AI"
      >
        {isOpen ? <X size={24} /> : <MessageSquare size={24} />}
      </button>
      
      {/* Chat window */}
      {isOpen && (
        <GlassContainer className="fixed bottom-20 right-4 z-50 w-80 sm:w-96 h-[500px] max-h-[80vh] rounded-lg overflow-hidden border border-blue-400/30 shadow-glow-md flex flex-col">
          {/* Header */}
          <div className="bg-[#12121A]/80 p-4 border-b border-blue-400/20 flex items-center">
            <div className="w-8 h-8 rounded-full bg-blue-400 flex items-center justify-center mr-3">
              <Bot size={16} className="text-[#12121A]" />
            </div>
            <div>
              <h3 className="text-white font-semibold">Morpho AI Assistant</h3>
              <p className="text-xs text-gray-400">Ask about 3D product visualization</p>
            </div>
          </div>
          
          {/* Message area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map(message => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[80%] ${message.sender === 'user' ? 'bg-blue-400 text-white' : 'bg-[#1A1A25] text-white'} rounded-lg p-3`}>
                  <div className="flex items-center mb-1">
                    {message.sender === 'bot' ? (
                      <Bot size={14} className="mr-1 text-blue-400" />
                    ) : (
                      <User size={14} className="mr-1" />
                    )}
                    <span className="text-xs opacity-70">
                      {message.sender === 'bot' ? 'Morpho AI' : 'You'}
                    </span>
                  </div>
                  <p>{message.text}</p>
                </div>
              </div>
            ))}
            
            {isBotTyping && (
              <div className="flex justify-start">
                <div className="bg-[#1A1A25] text-white rounded-lg p-3">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 rounded-full bg-blue-400 animate-bounce"></div>
                    <div className="w-2 h-2 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
          
          {/* Input area */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask about 3D product visualization..."
                className="flex-1 bg-[#1A1A25] text-white rounded-lg border border-gray-700 p-2 focus:outline-none focus:border-blue-400 resize-none"
                rows={1}
              />
              <Button 
                intent="primary"
                className="ml-2 p-2 h-[38px] w-[38px] flex items-center justify-center"
                onClick={handleSendMessage}
              >
                <Send size={16} />
              </Button>
            </div>
            <div className="mt-2 text-xs text-gray-500 text-center">
              Powered by Morpho AI
            </div>
          </div>
        </GlassContainer>
      )}
    </>
  );
};