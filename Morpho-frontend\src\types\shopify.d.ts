declare global {
  interface Window {
    // Removed window.shopify and related App Bridge types
  }

  namespace JSX {
    interface IntrinsicElements {
      's-page': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        title?: string;
      };
      's-layout': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
      's-layout-section': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
      's-card': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
      's-button': React.DetailedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> & {
        variant?: 'primary' | 'secondary' | 'plain';
        loading?: boolean;
      };
      's-spinner': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        size?: 'small' | 'medium' | 'large';
      };
      's-data-table': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        columnContentTypes?: string[];
        headings?: string[];
        rows?: any[][];
        footerContent?: string;
      };
    }
  }
} 