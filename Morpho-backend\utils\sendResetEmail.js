const transporter = require("../config/nodemailerConfig");
const { EMAIL } = require("../config/envVariables");

const sendResetEmail = (email, token, resetLink) => {
  transporter.sendMail(
    {
      from: EMAIL,
      to: email,
      subject: "Reset Password",
      html: `<a href="${resetLink}">Reset Password</a>`,
    },
    (err, info) => {
      if (err) {
        return res.status(500).json({
          Error: `Error ${err}`,
          success: false,
        });
      } else {
        return res.status(200).json({
          message: "Email Sent Successfully",
          success: true,
          data: info.response,
        });
      }
    }
  );
};

module.exports = { sendResetEmail };
