const router = require("express").Router();
const controller = require("../../controllers/glbController");
const verifyJWT = require("../../middleware/verifyJWT");
const { validateGlbSchema } = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");
const { admin, designer, client } = require("../../constants");
const { upload } = require("../../middleware/multer-middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.route("/create-glb-model").post(
  // validateGlbSchema,
  verifyJWT,
  upload.single("file"),
  // verifyRoles([admin, designer]),
  asyncErrorHandler(controller.createGlbModel)
);

router.post(
  "/create-glb",
  verifyJWT,
  upload.fields([
    { name: "file", maxCount: 1 },
    { name: "imageUrl", maxCount: 1 },
  ]),
  asyncError<PERSON>and<PERSON>(controller.createGlbModelUpdated)
);

router.get(
  "/get-all-by-client",
  verifyJWT,
  asyncErrorHandler(controller.getAllProductsAndProjects)
);
router
  .route("/create-product-phone")
  .post(
    upload.single("usdz"),
    asyncErrorHandler(controller.createProductPhone)
  );
router
  .route("/create")
  .post(
    verifyJWT,
    upload.single("file"),
    asyncErrorHandler(controller.creteGlbModelUpdated)
  );
router.get(
  "/get-all-glb-models",
  verifyJWT,
  // verifyRoles([admin,d]),
  asyncErrorHandler(controller.getAllGlbModels)
);
router.get("/get-models-app/:userId", asyncErrorHandler(controller.test));
router.post(
  "/get-models-by-clientId",
  verifyJWT,
  asyncErrorHandler(controller.getModelsByClient)
);
router.get(
  "/get-models/:userId",
  asyncErrorHandler(controller.getModelsByUserIdParams)
);
router.get(
  "/get-models-by-clientid",
  verifyJWT,
  asyncErrorHandler(controller.getModelsByClientByClientId)
);
router.post(
  "/create-product-showroom",
  upload.single("url"),
  asyncErrorHandler(controller.createProductForShowroom)
);
router.post(
  "/create-product-phone",
  asyncErrorHandler(controller.createProductPhone)
);
router.post(
  "/get-all-glb-models-by-projectId",
  verifyJWT,
  asyncErrorHandler(controller.getAllGlbModelsByProjectId)
);
router.get(
  "/get-all-by-project",
  verifyJWT,
  asyncErrorHandler(controller.getAllProductsByProjectId)
);
router.post(
  "/get-glb-model-by-id",
  verifyJWT,
  asyncErrorHandler(controller.getGlbModelById)
);
router.post(
  "/get-product-by-id",
  asyncErrorHandler(controller.getGlbModelById)
);
router.get(
  "/get-glb-model-by-project",
  verifyJWT,
  asyncErrorHandler(controller.getGlbModelByProjectId)
);
router.get(
  "/get-by-sku-and-project",
  verifyJWT,
  asyncErrorHandler(controller.getBySkuAndProject)
);
router.get(
  "/get-glb-model-with-project",
  verifyJWT,
  asyncErrorHandler(controller.getGlbModelWithProject)
);
router.get(
  "/get-glb-model-with-category",
  verifyJWT,
  asyncErrorHandler(controller.getGlbModelWithCategory)
);
router.get(
  "/get-glb-model-with-collection",
  verifyJWT,
  asyncErrorHandler(controller.getGlbModelWithCollectionId)
);
router.get(
  "/get-glb-model-with-asset",
  verifyJWT,
  asyncErrorHandler(controller.getGlbModelWithAsset)
);
router.get(
  "/get-glb-model-with-information",
  verifyJWT,
  asyncErrorHandler(controller.getGlbModelWithAllInformations)
);
router.get(
  "/get-glb-model-by-id-with-hdr/:id",
  asyncErrorHandler(controller.getGlbLinkWithHdrById)
);
router.put(
  "/update-glb-model-by-id",
  verifyJWT,
  // verifyRoles([admin, designer, client]),
  asyncErrorHandler(controller.updateGlbModelById)
);

router.put(
  "/update-product/:productId",
  verifyJWT,
  upload.fields([
    { name: "url", maxCount: 1 },
    { name: "imageUrl", maxCount: 1 },
  ]),
  asyncErrorHandler(controller.updateProductWithFiles)
);
router.post(
  "/delete-glb-model",
  verifyJWT,
  // verifyRoles([admin, designer]),
  asyncErrorHandler(controller.deleteGlbModel)
);
//test
router.delete(
  "/delete-by-id/:id",
  verifyJWT,
  asyncErrorHandler(controller.deleteProductFromParams)
);
router.post(
  "/delete-buff-module",
  asyncErrorHandler(controller.deleteGlbModel)
);

module.exports = router;
