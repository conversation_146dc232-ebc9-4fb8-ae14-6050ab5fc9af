const mongoose = require("mongoose");

const customizedMaterialSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    iconUrl: {
      type: String,
      required: true,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    model: {
      type: mongoose.Types.ObjectId,
      ref: "GlbModel",
    },
  },
  { timestamps: true, collection: "customizedMaterials" }
);

const customizedMaterialModel = mongoose.model(
  "CustomizedMaterial",
  customizedMaterialSchema
);
module.exports = customizedMaterialModel;
