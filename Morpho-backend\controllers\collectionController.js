const collectionSchema = require("../models/Collection");
const glbModelSchema = require("../models/GlbModel"); // Assuming this is your model name
const resHandle = require("../utils/responseHandle");
const mongoose = require("mongoose");
const expressError = require("../errors/expressError");

class Controller {
  createCollection = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate required fields
    if (!req.body.name) {
      throw new expressError(
        "Collection name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Create new collection
    const newCollection = new collectionSchema({
      ...req.body,
      user: req.user,
    });

    // Save collection to database
    await newCollection.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Collection created successfully",
      true,
      newCollection
    );
  };

  // Get a collection by ID (without populated products)
  getCollectionById = async (req, res) => {
    // Get collection ID from request params
    const { id } = req.params;

    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate required fields
    if (!id) {
      throw new expressError(
        "Collection ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid collection ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Find collection by ID and user ID
    const collection = await collectionSchema.findOne({
      _id: id,
      user: req.user,
    });

    // Check if collection exists
    if (!collection) {
      throw new expressError(
        "Collection not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Collection retrieved successfully",
      true,
      collection
    );
  };

  // Get all collections for the current user
  getCollections = async (req, res) => {
    // Validate user authentication
    if (!req.user) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Fetch collections for the current user
    const collections = await collectionSchema.find({ user: req.user });

    // Return success response
    return resHandle.handleData(
      res,
      200,
      "Collections retrieved successfully",
      true,
      collections
    );
  };

  // Add products to a collection
  addProductsToCollection = async (req, res) => {
    const { collectionId, productIds } = req.body;
    const userId = req.user;

    // Validate user authentication
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate input
    if (
      !collectionId ||
      !Array.isArray(productIds) ||
      productIds.length === 0
    ) {
      throw new expressError(
        "Invalid input data. Collection ID and product IDs are required",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Check if collection exists and belongs to the user
    const collection = await collectionSchema.findOne({
      _id: collectionId,
      user: userId,
    });

    if (!collection) {
      throw new expressError(
        "Collection not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Validate product IDs
    const validProductIds = productIds.filter((id) =>
      mongoose.Types.ObjectId.isValid(id)
    );
    const validProducts = await glbModelSchema.find({
      _id: { $in: validProductIds },
    });

    if (validProducts.length === 0) {
      throw new expressError(
        "No valid products found",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Add products to collection (avoid duplicates)
    const existingProductIds = collection.products.map((id) => id.toString());
    const newProductIds = validProducts.map((product) =>
      product._id.toString()
    );

    // Filter out products that are already in the collection
    const productsToAdd = newProductIds.filter(
      (id) => !existingProductIds.includes(id)
    );

    if (productsToAdd.length === 0) {
      return resHandle.handleData(
        res,
        200,
        "No new products to add to collection",
        true,
        collection
      );
    }

    // Convert string IDs back to ObjectIDs
    const objectIdProductsToAdd = productsToAdd.map(
      (id) => new mongoose.Types.ObjectId(id)
    );

    // Add new products to the collection
    collection.products = [...collection.products, ...objectIdProductsToAdd];
    await collection.save();

    return resHandle.handleData(
      res,
      200,
      "Products added to collection successfully",
      true,
      collection
    );
  };

  // Get a collection with populated products
  getCollection = async (req, res) => {
    const collectionId = req.params.id;
    const userId = req.user;

    // Validate user authentication
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate collection ID
    if (!collectionId) {
      throw new expressError(
        "Collection ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find the collection by ID and user ID
    const collection = await collectionSchema
      .findOne({ _id: collectionId, user: userId })
      .populate({
        path: "products",
        model: "GlbModel",
        select: "name description price imageUrl url published updatedAt",
      });

    if (!collection) {
      throw new expressError(
        "Collection not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    return resHandle.handleData(
      res,
      200,
      "Collection Retrieved Successfully",
      true,
      collection
    );
  };

  // Add models to a collection from FormData
  addModelsToCollection = async (req, res) => {
    const userId = req.user;

    // Validate user authentication
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Get collection ID from form data
    const collectionId = req.body.selectedCollection;

    if (!collectionId) {
      throw new expressError(
        "Collection ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Check if collection exists and belongs to the user
    const collection = await collectionSchema.findOne({
      _id: collectionId,
      user: userId,
    });

    if (!collection) {
      throw new expressError(
        "Collection not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Get the uploaded models - assuming models are uploaded and IDs are returned or stored
    // This would depend on how you handle model uploads
    const modelFiles = req.files || [];

    if (modelFiles.length === 0) {
      throw new expressError(
        "No model files uploaded",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Process model files and add them to the collection
    // This part would need to be adjusted based on your file upload implementation

    return resHandle.handleData(
      res,
      200,
      "Models added to collection successfully",
      true,
      collection
    );
  };
  removeModelFromCollection = async (req, res) => {
    const { collectionId, productId } = req.body;
    const userId = req.user;

    // Validate user authentication
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate required fields
    if (!collectionId || !productId) {
      throw new expressError(
        "Collection ID and Product ID are required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Find collection
    const collection = await collectionSchema.findOne({
      _id: collectionId,
      user: userId,
    });

    // Check if collection exists
    if (!collection) {
      throw new expressError(
        "Collection not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Check if product exists in collection
    if (!collection.products.includes(productId)) {
      throw new expressError(
        "Product not found in the collection",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Remove product from collection
    collection.products = collection.products.filter(
      (id) => id.toString() !== productId
    );

    // Save updated collection
    await collection.save();

    return resHandle.handleData(
      res,
      200,
      "Product removed from collection successfully",
      true,
      collection
    );
  };
  deleteCollection = async (req, res) => {
    const { id } = req.params;
    const userId = req.user;

    // Validate user authentication
    if (!userId) {
      throw new expressError(
        "User authentication is required",
        401,
        expressError.CODES.AUTHENTICATION_REQUIRED
      );
    }

    // Validate collection ID
    if (!id) {
      throw new expressError(
        "Collection ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Check if collection exists and belongs to the user
    const collection = await collectionSchema.findOne({
      _id: id,
      user: userId,
    });

    if (!collection) {
      throw new expressError(
        "Collection not found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Delete the collection
    await collectionSchema.findByIdAndDelete(id);

    return resHandle.handleData(
      res,
      200,
      "Collection deleted successfully",
      true,
      null
    );
  };
}

const controller = new Controller();

module.exports = controller;
