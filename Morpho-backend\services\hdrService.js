// hdrService.js

const hdrSchema = require("../models/HDR"); // Update the path as needed
const { s3Upload } = require("../config/s3Service"); // Update the path as needed

const createHdr = async (bodyData, files, req) => {
  const inputs = {
    name: bodyData.name || bodyData.hdrName,
    url: bodyData.url,
    icon: bodyData.icon,
    rotationY: bodyData.rotationY,
    intensity: bodyData.intensity,
    category: bodyData.category || bodyData.hdrCategory,
    library: bodyData.library,
  };

  // Handle URL upload
  if (
    files.hdrUrl &&
    files.hdrUrl.length > 0 &&
    typeof bodyData.url !== "string"
  ) {
    const data = await s3Upload(files.hdrUrl[0], `HDRI/${bodyData.category}`);
    inputs.url = data?.Location;
  }

  // Handle icon upload
  if (
    files.hdrIcon &&
    files.hdrIcon.length > 0 &&
    typeof bodyData.icon !== "string"
  ) {
    const data = await s3Upload(files.hdrIcon[0], "iconurl");
    inputs.icon = data?.Location;
  }

  // Check if the HDR with the same URL already exists
  const existingHdr = await hdrSchema.findOne({
    url: inputs.url,
    name: inputs.name,
    icon: inputs.icon,
    rotationY: inputs.rotationY,
    intensity: inputs.intensity,
    category: inputs.category,
    library: inputs.library,
    user: req.user,
  });
  if (existingHdr) return existingHdr;

  // Create new HDR document
  const newHdr = new hdrSchema(inputs);
  await newHdr.save();

  return newHdr;
};

module.exports = {
  createHdr,
};
