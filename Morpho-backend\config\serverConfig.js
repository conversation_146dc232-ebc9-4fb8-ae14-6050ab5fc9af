const express = require("express");
const http = require("http");
const { PORT } = require("./envVariables");
const databaseConnection = require("./mongooseConfig");
const expressApp = require("./expressConfig");

module.exports = async () => {
  const app = express();
  const server = http.createServer(app);

  await databaseConnection();
  await expressApp(app);

  server
    .listen(PORT, () => {
      console.log(`Server Listening on ${PORT}`);
    })
    .on("error", (error) => {
      console.log(`Error While Starting Application: ${error}`);
    });
};
