import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useParams, Link, useLocation } from "react-router-dom";
import { toast } from "react-hot-toast";
import { useSubscription } from "../hooks/useSubscription";
import {
  ArrowLeft,
  Edit3,
  Save,
  X,
  Share2,
  Download,
  History,
  Box,
  FileCode,
  Smartphone,
  Package,
  Layers,
  Ruler,
  AlertCircle,
  ChevronDown,
  Minimize2,
  Loader2,
} from "lucide-react";
import ProductTabs from "../components/ProductTabs";
import ModelViewer from "../components/ModelViewer";
import ProductUpdateForm from "../components/ProductUpdateForm";
import { api } from "../services/api";
import type { Model3D as Model3DType } from "../types/models";

const ProductDetail = () => {
  const { productId } = useParams();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const shouldEdit = searchParams.get("edit") === "true";

  // Get user subscription status
  const { subscription, loading: subscriptionLoading } = useSubscription();

  const [model, setModel] = useState<Model3DType | null>(null);
  const [isEditing, setIsEditing] = useState(shouldEdit);
  const [activeTab, setActiveTab] = useState<
    "info" | "preview" | "publish" | "analytics"
  >("preview");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewerError, setViewerError] = useState<string | null>(null);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [selectedVariantUrl, setSelectedVariantUrl] = useState<string | null>(
    null
  );
  const [isCompressing, setIsCompressing] = useState(false);
  const [modelSizeInfo, setModelSizeInfo] = useState<{
    original: number | null;
    compressed: number | null;
  }>({ original: null, compressed: null });
  const [selectedVariantId, setSelectedVariantId] = useState<string | null>(
    null
  );
  const [variantDropdownOpen, setVariantDropdownOpen] = useState(false);

  // Check if user has a paid plan (Basic, Pro, or Enterprise)
  const hasPaidPlan = useMemo(() => {
    if (!subscription) return false;
    return (
      subscription.status === "active" &&
      ["basic", "pro", "enterprise"].includes(subscription.plan || "")
    );
  }, [subscription]);

  const fetchModel = useCallback(async () => {
    try {
      const response = await api.getModels();
      let foundModel = response.data.find((m) => m._id === productId);
      if (!foundModel) {
        console.error(
          "Product not found in models list, trying to fetch directly"
        );
        try {
          // Try to fetch the model directly
          const singleModelResponse = await api.getModel(productId);
          if (singleModelResponse.success && singleModelResponse.data) {
            foundModel = singleModelResponse.data;
          } else {
            throw new Error("Product not found");
          }
        } catch (err) {
          console.error("Failed to fetch single model:", err);
          throw new Error("Product not found");
        }
      }
      setModel(foundModel);

      // Set initial variant URL if productOptions exist
      if (foundModel.productOptions && foundModel.productOptions.length > 0) {
        setSelectedVariantUrl(foundModel.productOptions[0].url);
        setSelectedVariantId(
          foundModel.productOptions[0].shopifyVariantId || "Variant 1"
        );
      } else {
        setSelectedVariantUrl(null);
        setSelectedVariantId(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch model");
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    let mounted = true;

    if (productId) {
      fetchModel();
    } else {
      console.error("ProductDetail: No product ID provided");
      setError("No product ID provided");
    }

    return () => {
      mounted = false;
    };
  }, [productId, fetchModel]);

  const handleModelUpdate = useCallback((updatedModel: Model3DType) => {
    setModel((prev) => {
      if (!prev) {
        return updatedModel;
      }
      return { ...prev, ...updatedModel };
    });
  }, []);

  const handleSave = useCallback(() => {
    try {
      // This is now handled by the ProductUpdateForm
      // The form will make the API call and handle the response
      setIsEditing(false);
    } catch (err) {
      console.error("Failed to save:", err);
    }
  }, []);

  const handleCancel = useCallback(() => {
    setIsEditing(false);
    // Reset any unsaved changes
    fetchModel();
  }, [fetchModel]);

  const handleCompressModel = useCallback(async () => {
    if (!model || !model._id) return;

    // Check if user has a paid subscription
    if (!hasPaidPlan) {
      toast.error(
        "Model compression requires a paid subscription (Basic, Pro, or Enterprise)",
        { duration: 5000 }
      );
      return;
    }

    // Get the model URL to compress
    const modelUrl = selectedVariantUrl || model.url;
    if (!modelUrl) {
      toast.error("No model URL found to compress");
      return;
    }

    // Set compressing state to show loading indicator
    setIsCompressing(true);
    setModelSizeInfo({ original: null, compressed: null });

    try {
      // Step 1: Fetch the original model to get its size
      const originalModelResponse = await fetch(modelUrl);
      if (!originalModelResponse.ok) {
        throw new Error(
          `Failed to fetch original model: ${originalModelResponse.status}`
        );
      }

      const originalModelBlob = await originalModelResponse.blob();
      const originalSize = originalModelBlob.size;

      // Update size info with original size
      setModelSizeInfo((prev) => ({ ...prev, original: originalSize }));

      // Step 2: Compress the model
      const compressedModelBlob = await api.compressModel(modelUrl);
      const compressedSize = compressedModelBlob.size;

      // Update size info with compressed size
      setModelSizeInfo((prev) => ({ ...prev, compressed: compressedSize }));

      // Calculate size reduction percentage
      const reductionPercent = (
        ((originalSize - compressedSize) / originalSize) *
        100
      ).toFixed(1);

      // Step 3: Update the model with the compressed file
      const updatedModel = await api.updateModelWithCompressedFile(
        model._id,
        compressedModelBlob
      );

      // Step 4: Update the local state with the new model data
      setModel(updatedModel);

      // If we're viewing a variant, update the selected variant URL
      if (selectedVariantUrl && updatedModel.productOptions) {
        const updatedVariant = updatedModel.productOptions.find(
          (option) => option.shopifyVariantId === selectedVariantId
        );
        if (updatedVariant) {
          setSelectedVariantUrl(updatedVariant.url);
        }
      }

      // Show success message with size reduction info
      toast.success(
        `Model compressed successfully! Size reduced by ${reductionPercent}%`
      );
    } catch (error) {
      console.error("Compression error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to compress model"
      );
    } finally {
      setIsCompressing(false);
    }
  }, [model, selectedVariantUrl, selectedVariantId, hasPaidPlan]);

  const handleVariantSelect = (url: string, variantId: string | null) => {
    setSelectedVariantUrl(url);
    setSelectedVariantId(variantId);
    setVariantDropdownOpen(false);
    // Reset the model loaded state to show the loading spinner
    setModelLoaded(false);
  };

  // Helper function to determine model format
  const getModelFormat = (url?: string) => {
    if (!url) return "Unknown";
    if (url.endsWith(".glb")) return "GLB";
    if (url.endsWith(".gltf")) return "GLTF";
    return "Unknown";
  };

  // Helper function to format file sizes
  const formatFileSize = (bytes: number | null): string => {
    if (bytes === null) return "N/A";
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Create a modified model object with the selected variant URL
  // This ensures that the tabs system and any other components get the current URL
  const currentModel = useMemo(() => {
    if (!model) return null;

    if (selectedVariantUrl) {
      // Create a new model object with the selected variant URL
      return {
        ...model,
        url: selectedVariantUrl, // Override the URL with the selected variant URL
        currentVariantId: selectedVariantId,
      };
    }

    return model;
  }, [model, selectedVariantUrl, selectedVariantId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-6rem)]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-300" />
      </div>
    );
  }

  if (error || !currentModel) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-6rem)]">
        <div className="p-6 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center gap-2">
          <AlertCircle size={20} strokeWidth={1.5} />
          <span>{error || "Product not found"}</span>
        </div>
      </div>
    );
  }

  // Use a safe accessor for model URL
  const modelUrl = currentModel.url || "";
  const modelName = currentModel.name || "Unnamed Model";
  const modelSku = currentModel.sku || "No SKU";
  const modelPrice = currentModel.price || 0;
  const modelCreatedAt = currentModel.createdAt
    ? new Date(currentModel.createdAt).toLocaleDateString()
    : "Unknown";
  const modelUpdatedAt = currentModel.updatedAt
    ? new Date(currentModel.updatedAt).toLocaleDateString()
    : "Unknown";

  // Check if product has variants from Shopify
  const hasVariants = model.productOptions && model.productOptions.length > 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link
            to="/dashboard/library"
            className="p-2 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors"
          >
            <ArrowLeft size={20} strokeWidth={1.5} />
          </Link>
          <div>
            <h1 className="text-2xl font-light tracking-wide text-gray-100">
              {modelName}
              {selectedVariantId && (
                <span className="ml-2 text-lg text-gray-400">
                  ({selectedVariantId})
                </span>
              )}
            </h1>
            <p className="text-sm text-gray-400 mt-1">
              SKU: {modelSku} • Created {modelCreatedAt}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 mr-4">
            <button className="p-2 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors">
              <Share2 size={18} strokeWidth={1.5} />
            </button>
            <button className="p-2 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors">
              <Download size={18} strokeWidth={1.5} />
            </button>
            <button className="p-2 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors">
              <History size={18} strokeWidth={1.5} />
            </button>
            <button
              onClick={handleCompressModel}
              disabled={isCompressing || !hasPaidPlan}
              className={`p-2 rounded-lg ${
                !hasPaidPlan && !isCompressing
                  ? "text-gray-600 cursor-not-allowed"
                  : "hover:bg-dark-200/50 text-gray-400"
              } transition-colors relative`}
              title={
                !hasPaidPlan
                  ? "Model compression requires a paid subscription (Basic, Pro, or Enterprise)"
                  : "Compress 3D Model"
              }
            >
              {isCompressing ? (
                <Loader2 size={18} strokeWidth={1.5} className="animate-spin" />
              ) : (
                <Minimize2 size={18} strokeWidth={1.5} />
              )}
              {!hasPaidPlan && (
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full"></span>
              )}
            </button>
          </div>
          {isEditing ? (
            <button
              onClick={handleCancel}
              className="btn btn-secondary flex items-center gap-2"
            >
              <X size={18} strokeWidth={1.5} />
              <span>Cancel</span>
            </button>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="btn btn-secondary flex items-center gap-2"
            >
              <Edit3 size={18} strokeWidth={1.5} />
              <span>Edit</span>
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="space-y-8">
        {/* Model Preview */}
        <div className="card rounded-xl p-6 bg-dark-300/90 backdrop-blur-lg">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-light text-gray-200">
              3D Model Preview
            </h2>
            <div className="flex items-center gap-2">
              {/* Compress model button */}
              <button
                onClick={handleCompressModel}
                disabled={isCompressing || !hasPaidPlan}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-lg ${
                  !hasPaidPlan && !isCompressing
                    ? "bg-dark-300 text-gray-500 cursor-not-allowed"
                    : "bg-dark-200 hover:bg-dark-100 text-gray-300"
                } text-sm transition-colors`}
                title={
                  !hasPaidPlan
                    ? "Model compression requires a paid subscription (Basic, Pro, or Enterprise)"
                    : "Optimize your 3D model for better performance"
                }
              >
                {isCompressing ? (
                  <>
                    <Loader2
                      size={16}
                      strokeWidth={1.5}
                      className="animate-spin"
                    />
                    <span>Compressing...</span>
                  </>
                ) : (
                  <>
                    <Minimize2 size={16} strokeWidth={1.5} />
                    <span>
                      {!hasPaidPlan ? "Premium Feature" : "Compress Model"}
                    </span>
                  </>
                )}
              </button>

              {/* Product variant selector - only show if product has variants */}
              {hasVariants && (
                <div className="relative">
                  <button
                    className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-dark-200 hover:bg-dark-100 text-gray-300 text-sm transition-colors"
                    onClick={() => setVariantDropdownOpen(!variantDropdownOpen)}
                  >
                    <span>Select Variant</span>
                    <ChevronDown size={16} strokeWidth={1.5} />
                  </button>

                  {variantDropdownOpen && (
                    <div className="absolute right-0 mt-1 z-10 w-56 rounded-md shadow-lg bg-dark-200 ring-1 ring-black ring-opacity-5">
                      <div className="py-1">
                        {model.productOptions.map((option, index) => (
                          <button
                            key={option.shopifyVariantId || index}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-dark-100"
                            onClick={() =>
                              handleVariantSelect(
                                option.url,
                                option.shopifyVariantId ||
                                  `Variant ${index + 1}`
                              )
                            }
                          >
                            {option.shopifyVariantId || `Variant ${index + 1}`}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {!currentModel.published && (
                <span className="px-2 py-1 rounded-full bg-yellow-500/10 text-yellow-400 text-xs">
                  Draft
                </span>
              )}
            </div>
          </div>
          <div className="aspect-[16/9] relative">
            {!modelLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-dark-200">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-brand-300 border-t-transparent" />
              </div>
            )}
            {modelUrl ? (
              <ModelViewer
                key={modelUrl} // Important: Change key when URL changes to force re-render
                url={modelUrl}
                modelId={currentModel._id}
                title={modelName}
                poster={currentModel.imageUrl}
                settings={{
                  autoRotate: false,
                  ar: true,
                  fullscreen: true,
                  zoom: true,
                  background: {
                    type: "color",
                    value: "#FFFFFF", // White background
                    opacity: 1,
                  },
                  controls: {
                    position: "bottom",
                    layout: "default",
                    visible: true,
                  },
                  lighting: {
                    environment: "neutral",
                    intensity: 1,
                    shadows: true,
                  },
                }}
                source="detail"
                className="w-full h-full"
                onError={(error) => {
                  setViewerError(error);
                  setModelLoaded(true);
                }}
                onLoad={() => {
                  setViewerError(null);
                  setModelLoaded(true);
                }}
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center bg-dark-200">
                <div className="p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/20 text-yellow-400 text-sm flex items-center gap-2">
                  <AlertCircle size={18} strokeWidth={1.5} />
                  <span>No model URL available</span>
                </div>
              </div>
            )}
            {viewerError && (
              <div className="absolute inset-0 flex items-center justify-center bg-dark-200/90 backdrop-blur-sm">
                <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm flex items-center gap-2">
                  <AlertCircle size={18} strokeWidth={1.5} />
                  <span>{viewerError}</span>
                </div>
              </div>
            )}
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4 text-sm text-gray-400">
            <div className="text-center">
              <p className="font-light">Left click + drag</p>
              <p className="text-gray-500 text-xs mt-1">Rotate model</p>
            </div>
            <div className="text-center">
              <p className="font-light">Right click + drag</p>
              <p className="text-gray-500 text-xs mt-1">Pan camera</p>
            </div>
            <div className="text-center">
              <p className="font-light">Scroll wheel</p>
              <p className="text-gray-500 text-xs mt-1">Zoom in/out</p>
            </div>
          </div>
        </div>

        {/* Model Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="card rounded-xl p-6">
              <h3 className="text-lg font-light text-gray-200 mb-4">
                Model Information
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-400">SKU</p>
                    <p className="text-gray-200 mt-1">{modelSku}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Price</p>
                    <p className="text-gray-200 mt-1">
                      ${modelPrice.toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-400">Created</p>
                    <p className="text-gray-200 mt-1">{modelCreatedAt}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Last Updated</p>
                    <p className="text-gray-200 mt-1">{modelUpdatedAt}</p>
                  </div>
                </div>
              </div>

              {/* Display variants information if available */}
              {hasVariants && (
                <div className="mt-6 pt-4 border-t border-gray-800">
                  <h4 className="text-sm font-medium text-gray-300 mb-3">
                    Shopify Variants
                  </h4>
                  <div className="grid gap-2">
                    {model.productOptions.map((option, index) => (
                      <div
                        key={option.shopifyVariantId || index}
                        className={`p-3 rounded-lg border ${
                          selectedVariantUrl === option.url
                            ? "border-brand-300 bg-brand-500/10"
                            : "border-gray-700 hover:border-gray-600"
                        } cursor-pointer transition-colors`}
                        onClick={() =>
                          handleVariantSelect(
                            option.url,
                            option.shopifyVariantId || `Variant ${index + 1}`
                          )
                        }
                      >
                        <p className="text-sm text-gray-300">
                          {option.shopifyVariantId || `Variant ${index + 1}`}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Quick Stats */}
          <div className="space-y-6">
            <div className="card rounded-xl p-6">
              <h3 className="text-lg font-light text-gray-200 mb-4">
                Model Specifications
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-gray-400">
                    <FileCode size={16} strokeWidth={1.5} />
                    <span className="text-sm">Format</span>
                  </div>
                  <span className="text-sm text-gray-300">
                    {getModelFormat(modelUrl)}
                  </span>
                </div>

                {/* File Size Information */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Minimize2 size={16} strokeWidth={1.5} />
                    <span className="text-sm">File Size</span>
                  </div>
                  <div className="text-right">
                    {modelSizeInfo.compressed ? (
                      <div>
                        <span className="text-sm text-green-400">
                          {formatFileSize(modelSizeInfo.compressed)}
                        </span>
                        {modelSizeInfo.original && (
                          <div className="text-xs text-gray-500">
                            Original: {formatFileSize(modelSizeInfo.original)}
                            <br />
                            Saved:{" "}
                            {(
                              ((modelSizeInfo.original -
                                modelSizeInfo.compressed) /
                                modelSizeInfo.original) *
                              100
                            ).toFixed(1)}
                            %
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-sm text-gray-300">
                        {isCompressing ? "Calculating..." : "Not compressed"}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Package size={16} strokeWidth={1.5} />
                    <span className="text-sm">Polygons</span>
                  </div>
                  <span className="text-sm text-gray-300">Optimized</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Layers size={16} strokeWidth={1.5} />
                    <span className="text-sm">Textures</span>
                  </div>
                  <span className="text-sm text-gray-300">PBR Materials</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Ruler size={16} strokeWidth={1.5} />
                    <span className="text-sm">Scale</span>
                  </div>
                  <span className="text-sm text-gray-300">1:1</span>
                </div>
              </div>
            </div>

            <div className="card rounded-xl p-6">
              <h3 className="text-lg font-light text-gray-200 mb-4">
                Compatibility
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Box size={16} strokeWidth={1.5} />
                    <span className="text-sm">AR Ready</span>
                  </div>
                  <span className="text-sm text-green-400">Yes</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Smartphone size={16} strokeWidth={1.5} />
                    <span className="text-sm">Mobile Optimized</span>
                  </div>
                  <span className="text-sm text-green-400">Yes</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Show ProductUpdateForm when in edit mode, otherwise show tabs */}
        {isEditing ? (
          <div className="card rounded-xl p-6 bg-dark-300/90 backdrop-blur-lg">
            <h2 className="text-lg font-light text-gray-200 mb-6">
              Edit Product
            </h2>
            <ProductUpdateForm
              productId={productId || ""}
              onSuccess={() => {
                setIsEditing(false);
                fetchModel(); // Refresh the model data
              }}
              onCancel={handleCancel}
            />
          </div>
        ) : (
          /* Tabs and Details */
          <ProductTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            model={currentModel} // Pass the modified model with the current URL
            isEditing={false} // Always false since we're using ProductUpdateForm instead
            onModelUpdate={handleModelUpdate}
          />
        )}
      </div>
    </div>
  );
};

export default ProductDetail;
