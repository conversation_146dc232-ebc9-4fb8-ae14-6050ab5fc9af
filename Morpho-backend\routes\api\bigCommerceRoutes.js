const router = require("express").Router();
const controller = require("../../controllers/bigCommerceController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
const {
  setBigCommerceClientv3,
  setBigCommerceClientv2,
} = require("../../utils/bigCommerce");

router.get(
  "/products",
  setBigCommerceClientv3,
  asyncError<PERSON><PERSON><PERSON>(controller.getAllProducts)
);
router.get(
  "/statistics",
  setBigCommerceClientv2,
  asyncError<PERSON><PERSON><PERSON>(controller.getStatistics)
);
router.post(
  "/save-products",
  setBigCommerceClientv3,
  asyncError<PERSON><PERSON><PERSON>(controller.saveAllProducts)
);

module.exports = router;
