// Updated interfaces to match your backend schema
export interface ViewerSettings {
  autoRotate: boolean;
  ar: boolean;
  fullscreen: boolean;
  zoom: boolean;
}

export interface BackgroundConfig {
  type: 'color' | 'environment' | 'transparent';
  value: string;
  opacity?: number;
}

export interface ControlPanelConfig {
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  alignment: 'left' | 'center' | 'right';
  visible: boolean;
  layout: 'default' | 'minimal' | 'custom';
}

export interface HdrConfig {
  url: string;
  level: number;
}

export interface GenerateLinkParams {
  glb: string;
  glbUrls: string[];
  hdr?: HdrConfig[];
  autoRotate: boolean;
  AR: boolean;
  fullScreen: boolean;
  zoom: boolean;
  background: BackgroundConfig;
  hashedId: string;
  icon360: boolean;
  controlPanel: ControlPanelConfig;
}

export interface ViewerTemplate {
  id: string;
  name: string;
  description: string;
  settings: ViewerSettings;
  background: BackgroundConfig;
  controlPanel: ControlPanelConfig;
  previewImage?: string;
}