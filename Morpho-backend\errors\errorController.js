const { NODE_ENV } = require("../config/envVariables.js");
const expressError = require("./expressError.js");

/**
 * Handles errors in development environment with detailed information
 * @param {Object} res - Express response object
 * @param {Error} error - Error object
 */
const devErrors = (res, error) => {
  res.status(error.statusCode).json({
    success: false,
    status: error.statusCode,
    message: error.message,
    stackTrace: error.stack,
    error: error,
  });
};

/**
 * Handles errors in production environment with appropriate user-facing messages
 * @param {Object} res - Express response object
 * @param {Error} error - Error object
 */
const prodErrors = (res, error) => {
  // Operational errors are expected errors that we can handle gracefully
  if (error.isOperational) {
    res.status(error.statusCode).json({
      success: false,
      status: error.statusCode,
      message: error.message,
      errorCode: error.errorCode || null,
    });
  } else {
    // For unexpected errors, don't leak error details to client
    console.error("ERROR 💥", error);
    res.status(500).json({
      success: false,
      status: "error",
      message: "Something went wrong on our end. Please try again later.",
      errorCode: "SERVER_ERROR",
    });
  }
};

/**
 * Handles MongoDB CastError (invalid ObjectId, etc.)
 * @param {Error} error - MongoDB CastError
 * @returns {Error} Formatted error
 */
const castErrorHandler = (error) => {
  let msg;
  if (error.path === "_id") {
    msg = `Invalid ID format. Please check the ID and try again.`;
  } else {
    msg = `Invalid value for ${error.path}: ${error.value}`;
  }
  const formattedError = new expressError(msg, 400);
  formattedError.errorCode = "INVALID_DATA_FORMAT";
  return formattedError;
};

/**
 * Handles MongoDB duplicate key errors
 * @param {Error} error - MongoDB duplicate key error
 * @returns {Error} Formatted error
 */
const duplicateKeyErrorHandler = (error) => {
  // Extract the duplicate field name and value
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];

  let msg;
  if (field === "email") {
    msg = `An account with this email (${value}) already exists. Please use a different email or try logging in.`;
  } else if (field === "username") {
    msg = `Username "${value}" is already taken. Please choose a different username.`;
  } else {
    msg = `Duplicate value for ${field}: ${value}. This value must be unique.`;
  }

  const formattedError = new expressError(msg, 400);
  formattedError.errorCode = "DUPLICATE_ENTRY";
  return formattedError;
};

/**
 * Handles Mongoose validation errors
 * @param {Error} error - Mongoose validation error
 * @returns {Error} Formatted error
 */
const validationErrorHandler = (error) => {
  const errors = Object.values(error.errors).map((value) => {
    // Make validation messages more user-friendly
    let message = value.message;
    if (message.includes("required")) {
      message = message.replace("Path", "Field");
    }
    return message;
  });

  const errorMessage = errors.join(". ");
  const msg = `Validation failed: ${errorMessage}`;

  const formattedError = new expressError(msg, 400);
  formattedError.errorCode = "VALIDATION_ERROR";
  return formattedError;
};

/**
 * Handles JWT authentication errors
 * @param {Error} error - JWT error
 * @returns {Error} Formatted error
 */
const jwtErrorHandler = (error) => {
  let msg;
  let statusCode = 401;
  let errorCode = "AUTHENTICATION_ERROR";

  if (error.name === "JsonWebTokenError") {
    msg = "Invalid authentication token. Please log in again.";
  } else if (error.name === "TokenExpiredError") {
    msg = "Your session has expired. Please log in again.";
    errorCode = "SESSION_EXPIRED";
  } else {
    msg = "Authentication failed. Please log in again.";
  }

  const formattedError = new expressError(msg, statusCode);
  formattedError.errorCode = errorCode;
  return formattedError;
};

/**
 * Handles file upload errors
 * @param {Error} error - File upload error
 * @returns {Error} Formatted error
 */
const fileUploadErrorHandler = (error) => {
  let msg;
  let errorCode = "FILE_UPLOAD_ERROR";

  if (error.code === "LIMIT_FILE_SIZE") {
    msg = "File is too large. Maximum file size is 100MB.";
    errorCode = "FILE_TOO_LARGE";
  } else if (error.code === "LIMIT_UNEXPECTED_FILE") {
    msg = "Unexpected file field. Please check the form fields.";
  } else if (error.code === "LIMIT_FILE_COUNT") {
    msg = "Too many files uploaded. Please try again with fewer files.";
    errorCode = "TOO_MANY_FILES";
  } else {
    msg = "File upload failed. Please try again.";
  }

  const formattedError = new expressError(msg, 400);
  formattedError.errorCode = errorCode;
  return formattedError;
};

/**
 * Handles network/connection errors
 * @param {Error} error - Network error
 * @returns {Error} Formatted error
 */
const networkErrorHandler = (error) => {
  const msg =
    "Connection error. Please check your internet connection and try again.";
  const formattedError = new expressError(msg, 503);
  formattedError.errorCode = "CONNECTION_ERROR";
  return formattedError;
};

/**
 * Main error handler middleware
 */
module.exports = (error, req, res, next) => {
  error.statusCode = error.statusCode || 500;
  error.status = error.status || "error";

  // Log all errors in both environments
  console.error(`[${new Date().toISOString()}] Error:`, {
    path: req.path,
    method: req.method,
    statusCode: error.statusCode,
    message: error.message,
    stack: NODE_ENV === "development" ? error.stack : undefined,
  });

  if (NODE_ENV === "development") {
    devErrors(res, error);
  } else {
    // Handle specific error types in production
    if (error.name === "CastError") error = castErrorHandler(error);
    if (error.code === 11000) error = duplicateKeyErrorHandler(error);
    if (error.name === "ValidationError") error = validationErrorHandler(error);
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      error = jwtErrorHandler(error);
    }
    if (error.name === "MulterError") error = fileUploadErrorHandler(error);
    if (error.code === "ECONNREFUSED" || error.code === "ENOTFOUND") {
      error = networkErrorHandler(error);
    }

    prodErrors(res, error);
  }
};
