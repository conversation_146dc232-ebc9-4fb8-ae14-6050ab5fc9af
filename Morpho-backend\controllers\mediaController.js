const mediaModel = require("../models/MediaModel");
const { s3Upload } = require("../config/s3Service");
const { S3 } = require("aws-sdk");
const resHandle = require("../utils/responseHandle");
const expressError = require("../errors/expressError");

class Controller {
  getMediaInformations = (results) => {
    // Validate input
    if (!results || !Array.isArray(results) || results.length === 0) {
      return [];
    }

    // Extract media information
    return results.map((result) => {
      return {
        name: result.Key.split("/")[1],
        url: result.Location,
      };
    });
  };
  uploadFiles = async (req, res) => {
    // Validate request files
    if (!req.files || req.files.length === 0) {
      throw new expressError(
        "No files provided for upload",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Upload files to S3
    const results = await s3Upload(req.files);

    // Extract media information
    const informations = this.getMediaInformations(results);

    // Validate upload results
    if (informations.length === 0) {
      throw new expressError(
        "Failed to upload files",
        500,
        expressError.CODES.UPLOAD_FAILED
      );
    }

    // Create new media record
    const newMedia = new mediaModel({
      name: informations[0].name,
      url: informations[0].url,
    });

    // Save media to database
    await newMedia.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Media uploaded successfully",
      true,
      results
    );
  };
  createMedia = async (req, res) => {
    // Validate request body
    if (!req.body || Object.keys(req.body).length === 0) {
      throw new expressError(
        "Media data is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate required fields
    if (!req.body.name) {
      throw new expressError(
        "Media name is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    if (!req.body.url) {
      throw new expressError(
        "Media URL is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Create new media record
    const newMedia = new mediaModel({
      ...req.body,
    });

    // Save media to database
    await newMedia.save();

    // Return success response
    return resHandle.handleData(
      res,
      201,
      "Media created successfully",
      true,
      newMedia
    );
  };
  getAllMedias = async (req, res) => {
    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Validate pagination parameters
    if (page < 1) {
      throw new expressError(
        "Page number must be greater than 0",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    if (limit < 1) {
      throw new expressError(
        "Limit must be greater than 0",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    // Get total count
    const totalCount = await mediaModel.countDocuments();

    // Check if media exists
    if (totalCount === 0) {
      throw new expressError(
        "No media found",
        404,
        expressError.CODES.RESOURCE_NOT_FOUND
      );
    }

    // Fetch media with pagination
    const allMediasToBeRetrieved = await mediaModel
      .find({})
      .skip(skip)
      .limit(limit);

    // Return paginated response
    return res.status(200).json({
      message: "Media retrieved successfully",
      success: true,
      data: allMediasToBeRetrieved,
      total: totalCount,
      page: page,
      perPage: limit,
    });
  };
  getMediaById = async (req, res) => {
    // Get media ID from request body
    const { _id } = req.body;

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Media ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!_id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid media ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Find media by ID
      const media = await mediaModel.findById({ _id });

      // Check if media exists
      if (!media) {
        throw new expressError(
          "Media not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Return success response
      return resHandle.handleData(
        res,
        200,
        "Media retrieved successfully",
        true,
        media
      );
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid media ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
  deleteMediaById = async (req, res) => {
    // Get media ID from request body
    const { _id } = req.body;

    // Validate required fields
    if (!_id) {
      throw new expressError(
        "Media ID is required",
        400,
        expressError.CODES.MISSING_REQUIRED_FIELD
      );
    }

    // Validate ID format
    if (!_id.match(/^[0-9a-fA-F]{24}$/)) {
      throw new expressError(
        "Invalid media ID format",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
    }

    try {
      // Delete media from database
      const deletedMedia = await mediaModel.findByIdAndDelete({ _id });

      // Check if media exists
      if (!deletedMedia) {
        throw new expressError(
          "Media not found",
          404,
          expressError.CODES.RESOURCE_NOT_FOUND
        );
      }

      // Delete media from S3
      const name = deletedMedia.name;
      const s3 = new S3();
      const folderPath = "media/";
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: folderPath + name,
      };

      // Delete object from S3
      s3.deleteObject(params, (error, data) => {
        if (error) {
          console.error("S3 delete error:", error);
        } else {
          console.log("Object deleted successfully.");
        }
      });

      // Return success response
      return resHandle.handleData(res, 200, "Media deleted successfully", true);
    } catch (err) {
      // Handle MongoDB-specific errors
      if (err.name === "BSONTypeError") {
        throw new expressError(
          "Invalid media ID format",
          400,
          expressError.CODES.INVALID_DATA_FORMAT
        );
      }

      // Re-throw other errors
      throw err;
    }
  };
}

const controller = new Controller();
module.exports = controller;
