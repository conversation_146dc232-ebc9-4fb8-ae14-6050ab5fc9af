import React, { useState, useCallback, useEffect } from "react";
import {
  Search,
  Store as StoreIcon,
  Upload,
  Grid,
  List,
  ArrowUpDown,
  Tag,
  ChevronDown,
  FileUp,
  FolderPlus,
  X,
  Folder,
  ChevronLeft,
  ChevronRight,
  Package,
  Trash,
  ArrowUpRight,
} from "lucide-react";
import { useModels } from "../hooks/useModels";
import { useCollections } from "../hooks/useCollection";
import ModelCard from "../components/ModelCard";
import ProductImportModal from "../components/ProductImportModal";
import SingleUploadModal from "../components/modals/SingleUploadModal";
import CollectionUploadModal from "../components/modals/CollectionUploadModal";
import AddToCollectionModal from "../components/modals/AddToCollectionModal";
import ShopifySyncModal from "../components/modals/ShopifySyncModal";
import CollectionDetailView from "../pages/CollectionDetailView";
import type { ProductImportResult } from "../types/ecommerce";
import { debounce } from "../utils/debounce";
import { toast } from "react-hot-toast";
import { api } from "../services/api";
import { useNavigate } from "react-router-dom";

// View mode types
type ViewMode = "grid" | "list";
type LibraryView = "models" | "collections";

const Library = () => {
  const navigate = useNavigate();
  const {
    models,
    loading: modelsLoading,
    error: modelsError,
    refresh: refreshModels,
    clearError: clearModelsError,
  } = useModels();
  const {
    collections,
    selectedCollection,
    loading: collectionsLoading,
    detailsLoading,
    error: collectionsError,
    fetchCollections,
    getCollectionDetails,
    clearSelectedCollection,
    createCollection,
    addProductsToCollection,
    clearError: clearCollectionsError,
  } = useCollections();

  // Add a state to switch between models and collections view
  const [libraryView, setLibraryView] = useState<LibraryView>("models");

  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [filterCategory, setFilterCategory] = useState("all");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<"name" | "date" | "price" | "count">(
    "date"
  );
  const [showImportModal, setShowImportModal] = useState(false);
  const [showUploadDropdown, setShowUploadDropdown] = useState(false);
  const [showSingleUploadModal, setShowSingleUploadModal] = useState(false);
  const [showCollectionUploadModal, setShowCollectionUploadModal] =
    useState(false);
  const [showShopifySyncModal, setShowShopifySyncModal] = useState(false);
  const [isNewCollection, setIsNewCollection] = useState(false);
  const [deletedCollectionIds, setDeletedCollectionIds] = useState<string[]>(
    []
  );

  // Add state for the AddToCollectionModal
  const [showAddToCollectionModal, setShowAddToCollectionModal] =
    useState(false);
  const [selectedModel, setSelectedModel] = useState(null);

  // Add state for collection deletion
  const [isDeletingCollection, setIsDeletingCollection] = useState(false);
  const [collectionToDelete, setCollectionToDelete] = useState<string | null>(
    null
  );
  const [showDeleteCollectionConfirm, setShowDeleteCollectionConfirm] =
    useState(false);

  // Fetch collections on component mount
  useEffect(() => {
    fetchCollections();
  }, [fetchCollections]);

  const handleSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 300),
    []
  );

  const handleBackFromCollection = useCallback(() => {
    // Clear the selected collection first
    clearSelectedCollection();

    // Then refresh the collections after a short delay
    setTimeout(() => {
      fetchCollections();
    }, 300);
  }, [clearSelectedCollection, fetchCollections]);

  // Handler for switching library view
  const handleViewChange = (view: LibraryView) => {
    setLibraryView(view);
    clearSelectedCollection(); // Clear selected collection when switching views
  };

  // Handler for collection click
  const handleCollectionClick = (collectionId: string) => {
    getCollectionDetails(collectionId);
  };

  // Handler for collection deletion
  const handleDeleteCollection = (
    collectionId: string,
    e: React.MouseEvent
  ) => {
    e.stopPropagation();
    e.preventDefault();
    setCollectionToDelete(collectionId);
    setShowDeleteCollectionConfirm(true);
  };

  // Handler to confirm collection deletion
  const confirmDeleteCollection = async () => {
    if (!collectionToDelete) return;

    try {
      setIsDeletingCollection(true);
      await api.deleteCollection(collectionToDelete);
      toast.success("Collection deleted successfully");
      setCollectionToDelete(null);
      setShowDeleteCollectionConfirm(false);
      fetchCollections(); // Refresh collections list
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to delete collection"
      );
    } finally {
      setIsDeletingCollection(false);
    }
  };

  // Handler for successful model upload
  const handleModelUploadSuccess = useCallback(() => {
    refreshModels();
  }, [refreshModels]);

  // Handler for collection operations success
  const handleCollectionSuccess = useCallback(() => {
    toast.success(
      isNewCollection
        ? "Collection created successfully"
        : "Models added to collection"
    );
    fetchCollections();
    setShowCollectionUploadModal(false);
  }, [isNewCollection, fetchCollections]);

  // Handler for model deletion
  const handleDeleteModel = useCallback(
    (id: string) => {
      refreshModels();
    },
    [refreshModels]
  );

  // Handler for model duplication
  const handleDuplicateModel = useCallback((id: string) => {}, []);

  // Handler for model publishing/unpublishing
  const handlePublishModel = useCallback((id: string) => {}, []);

  // Handler for adding a model to a collection
  const handleAddToCollection = useCallback((model) => {
    setSelectedModel(model);
    setShowAddToCollectionModal(true);
  }, []);

  // Handler for successful addition to collection
  const handleAddToCollectionSuccess = useCallback(() => {
    toast.success("Model added to collection successfully");
    fetchCollections(); // Refresh collections to show updated count
  }, [fetchCollections]);

  // Handler for model click
  const handleModelClick = useCallback(
    (model) => {
      navigate(`/dashboard/library/${model._id}`);
    },
    [navigate]
  );

  // Comprehensive collection refresh function
  const handleCollectionRefresh = useCallback(async () => {
    // First, always refresh the collections list to update counts everywhere
    await fetchCollections();

    // Then, if we have a selected collection, also refresh its details
    if (selectedCollection && selectedCollection._id) {
      try {
        await getCollectionDetails(selectedCollection._id);
      } catch (error) {
        clearSelectedCollection();
      }
    }
  }, [selectedCollection, getCollectionDetails, fetchCollections]);

  // Get loading and error states
  const loading = modelsLoading || collectionsLoading || detailsLoading;
  const error = modelsError || collectionsError;

  // Clear error function
  const clearError = useCallback(() => {
    clearModelsError();
    clearCollectionsError();
  }, [clearModelsError, clearCollectionsError]);
  const clearSelectedCollectionAndRefresh = useCallback(() => {
    // First set selectedCollection to null
    clearSelectedCollection();

    // Then refresh the collections list
    fetchCollections();
  }, [clearSelectedCollection, fetchCollections]);
  // Filter and sort models
  const filteredModels = models
    .filter(
      (model) =>
        model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (model.sku &&
          model.sku.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (model.description &&
          model.description.toLowerCase().includes(searchQuery.toLowerCase()))
    )
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "price":
          return a.price - b.price;
        default:
          return (
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
      }
    });

  // Filter and sort collections
  const filteredCollections = collections
    .filter(
      (collection) =>
        collection.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        collection.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "count":
          return (b.products?.length || 0) - (a.products?.length || 0);
        default:
          return (
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
      }
    });

  return (
    <div>
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-light tracking-wide text-gray-100">
            3D Library
          </h1>
          <p className="text-gray-400 mt-2 font-light">
            {selectedCollection
              ? `Viewing models in "${selectedCollection.name}"`
              : "Manage and organize your 3D product catalog"}
          </p>
        </div>
        <div className="flex items-center gap-3 w-full sm:w-auto">
          <button
            onClick={() => navigate("/dashboard/integrations")}
            className="btn btn-secondary flex-1 sm:flex-initial flex items-center gap-2 group
           hover:bg-dark-200/80 backdrop-blur-sm border border-gray-700/50
           hover:border-brand-500/30 transition-all duration-300"
          >
            <StoreIcon
              size={18}
              strokeWidth={1.5}
              className="group-hover:text-brand-300 transition-colors"
            />
            <span>Import from Store</span>
          </button>
          <button
            onClick={() => setShowShopifySyncModal(true)}
            className="btn btn-secondary flex-1 sm:flex-initial flex items-center gap-2 group
           hover:bg-dark-200/80 backdrop-blur-sm border border-gray-700/50
           hover:border-brand-500/30 transition-all duration-300"
          >
            <ArrowUpRight
              size={18}
              strokeWidth={1.5}
              className="group-hover:text-brand-300 transition-colors"
            />
            <span>Sync to Shopify</span>
          </button>
          <div className="relative">
            <button
              onClick={() => setShowUploadDropdown(!showUploadDropdown)}
              className="btn btn-primary flex-1 sm:flex-initial flex items-center gap-2 group relative pr-8
                       bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700
                       shadow-lg shadow-brand-500/20 hover:shadow-brand-500/30 hover:scale-[1.02]
                       active:scale-[0.98] transition-all duration-300"
            >
              <Upload size={18} strokeWidth={1.5} />
              <span>Upload Models</span>
              <ChevronDown
                size={16}
                strokeWidth={1.5}
                className={`absolute right-3 top-1/2 -translate-y-1/2 transition-all duration-300 ease-out
                         transform ${showUploadDropdown ? "rotate-180" : ""}`}
              />
            </button>

            {showUploadDropdown && (
              <div
                className="absolute right-0 mt-2 w-72 rounded-xl bg-dark-300/95 backdrop-blur-lg
                         border border-gray-800/50 shadow-2xl shadow-brand-500/5 overflow-hidden
                         transform origin-top-right transition-all duration-300 ease-out z-50
                         animate-slideIn ring-1 ring-brand-500/10"
              >
                <button
                  onClick={() => {
                    setShowUploadDropdown(false);
                    setShowSingleUploadModal(true);
                  }}
                  className="w-full px-5 py-4 text-left text-gray-300 hover:bg-brand-500/10
                         hover:text-brand-300 transition-all duration-300 flex items-center gap-4
                         group relative overflow-hidden hover:backdrop-blur-lg"
                >
                  <div
                    className="w-10 h-10 rounded-xl bg-dark-200 flex items-center justify-center
                            group-hover:bg-brand-500/10 group-hover:scale-110 transition-all duration-300"
                  >
                    <FileUp
                      size={16}
                      strokeWidth={1.5}
                      className="text-gray-400 group-hover:text-brand-300"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-light">Upload Single Model</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Add an individual 3D model
                    </p>
                  </div>
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-brand-500/0 via-brand-500/5 to-brand-500/0
                            translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"
                  />
                </button>
                <button
                  onClick={() => {
                    setShowUploadDropdown(false);
                    setIsNewCollection(false);
                    setShowCollectionUploadModal(true);
                  }}
                  className="w-full px-5 py-4 text-left text-gray-300 hover:bg-brand-500/10 hover:text-brand-300
                         transition-all duration-300 flex items-center gap-4 border-t border-gray-800/30 group relative overflow-hidden"
                >
                  <div
                    className="w-10 h-10 rounded-xl bg-dark-200 flex items-center justify-center
                            group-hover:bg-brand-500/10 group-hover:scale-110 transition-all duration-300"
                  >
                    <FolderPlus
                      size={16}
                      strokeWidth={1.5}
                      className="text-gray-400 group-hover:text-brand-300"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-light">Add to Collection</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Upload to existing collection
                    </p>
                  </div>
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-brand-500/0 via-brand-500/5 to-brand-500/0
                            translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"
                  />
                </button>
                <button
                  onClick={() => {
                    setShowUploadDropdown(false);
                    setIsNewCollection(true);
                    setShowCollectionUploadModal(true);
                  }}
                  className="w-full px-5 py-4 text-left text-gray-300 hover:bg-brand-500/10 hover:text-brand-300
                         transition-all duration-300 flex items-center gap-4 border-t border-gray-800/30 group relative overflow-hidden"
                >
                  <div
                    className="w-10 h-10 rounded-xl bg-dark-200 flex items-center justify-center
                            group-hover:bg-brand-500/10 group-hover:scale-110 transition-all duration-300"
                  >
                    <FolderPlus
                      size={16}
                      strokeWidth={1.5}
                      className="text-gray-400 group-hover:text-brand-300"
                    />
                  </div>
                  <div>
                    <p className="text-sm font-light">Create New Collection</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Start a new model collection
                    </p>
                  </div>
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-brand-500/0 via-brand-500/5 to-brand-500/0
                            translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"
                  />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Library View Tabs */}
      {!selectedCollection && (
        <div className="flex border-b border-gray-800 mb-6">
          <button
            onClick={() => handleViewChange("models")}
            className={`px-4 py-3 font-light text-sm relative ${
              libraryView === "models"
                ? "text-brand-300 border-b-2 border-brand-500"
                : "text-gray-400 hover:text-gray-300"
            }`}
          >
            All Models
          </button>
          <button
            onClick={() => handleViewChange("collections")}
            className={`px-4 py-3 font-light text-sm relative flex items-center gap-1 ${
              libraryView === "collections"
                ? "text-brand-300 border-b-2 border-brand-500"
                : "text-gray-400 hover:text-gray-300"
            }`}
          >
            <Folder size={16} strokeWidth={1.5} />
            <span>Collections</span>
            <span className="ml-1 px-1.5 py-0.5 text-xs rounded-full bg-dark-200/80 text-gray-400">
              {collections.length}
            </span>
          </button>
        </div>
      )}

      {showSingleUploadModal && (
        <SingleUploadModal
          onClose={() => setShowSingleUploadModal(false)}
          onUpload={() => {}} // This is not used as we handle the upload directly in the modal
          onSuccess={handleModelUploadSuccess} // Pass the success handler to refresh models
        />
      )}

      {showCollectionUploadModal && (
        <CollectionUploadModal
          onClose={() => setShowCollectionUploadModal(false)}
          onSuccess={handleCollectionSuccess}
          collections={collections.map((collection) => ({
            id: collection._id,
            name: collection.name,
          }))}
          isNewCollection={isNewCollection}
        />
      )}

      <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4 mb-6">
        <div className="flex-1 flex flex-col sm:flex-row items-stretch sm:items-center gap-4 w-full">
          {selectedCollection && (
            <button
              onClick={() => clearSelectedCollection()}
              className="btn btn-secondary flex items-center gap-2 group"
            >
              <ChevronLeft size={18} strokeWidth={1.5} />
              <span>
                Back to{" "}
                {libraryView === "models" ? "All Models" : "Collections"}
              </span>
            </button>
          )}

          <div className="relative flex-1">
            <Search
              size={18}
              strokeWidth={1.5}
              className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              onChange={(e) => handleSearch(e.target.value)}
              placeholder={
                selectedCollection
                  ? "Search models in collection..."
                  : libraryView === "collections"
                  ? "Search collections..."
                  : "Search models..."
              }
              className="search-input"
            />
          </div>

          {libraryView === "models" && !selectedCollection && (
            <>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="filter-select sm:w-48"
              >
                <option value="all">All Categories</option>
                <option value="furniture">Furniture</option>
                <option value="electronics">Electronics</option>
                <option value="fashion">Fashion</option>
                <option value="home">Home & Decor</option>
              </select>
              <div className="flex items-center gap-2">
                <Tag size={18} strokeWidth={1.5} className="text-gray-400" />
                <div className="flex gap-2">
                  {["New", "Featured", "AR Ready"].map((tag) => (
                    <button
                      key={tag}
                      onClick={() =>
                        setSelectedTags((prev) =>
                          prev.includes(tag)
                            ? prev.filter((t) => t !== tag)
                            : [...prev, tag]
                        )
                      }
                      className={`tag-button ${
                        selectedTags.includes(tag)
                          ? "tag-button-active"
                          : "tag-button-inactive"
                      }`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-gray-400">
            <ArrowUpDown size={16} strokeWidth={1.5} />
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="bg-transparent border-none text-sm focus:outline-none"
            >
              <option value="date">Last Updated</option>
              <option value="name">Name</option>
              {libraryView === "models" ? (
                <option value="price">Price</option>
              ) : (
                <option value="count">Model Count</option>
              )}
            </select>
          </div>
          {(libraryView === "models" || selectedCollection) && (
            <div className="flex items-center gap-2 border-l border-gray-700 pl-4">
              <button
                onClick={() => setViewMode("grid")}
                className={`p-2 rounded-lg ${
                  viewMode === "grid"
                    ? "text-brand-300 bg-brand-500/10"
                    : "text-gray-400"
                }`}
              >
                <Grid size={18} strokeWidth={1.5} />
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`p-2 rounded-lg ${
                  viewMode === "list"
                    ? "text-brand-300 bg-brand-500/10"
                    : "text-gray-400"
                }`}
              >
                <List size={18} strokeWidth={1.5} />
              </button>
            </div>
          )}
        </div>
      </div>

      {loading ? (
        <div className="mt-8 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-300"></div>
        </div>
      ) : error ? (
        <div className="mt-8 p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400">
          {error}
        </div>
      ) : selectedCollection ? (
        <CollectionDetailView
          collection={selectedCollection}
          onBack={handleBackFromCollection}
          onRefresh={fetchCollections}
          viewMode={viewMode}
        />
      ) : libraryView === "collections" ? (
        /* Collections Grid View */
        collections.length === 0 ? (
          <div className="mt-8 text-center">
            <div className="p-8 rounded-lg bg-dark-300/50 border border-gray-800/50">
              <p className="text-gray-400 font-light">No collections found</p>
              <button
                className="mt-4 btn btn-primary"
                onClick={() => {
                  setIsNewCollection(true);
                  setShowCollectionUploadModal(true);
                }}
              >
                Create your first collection
              </button>
            </div>
          </div>
        ) : (
          <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6">
            {filteredCollections.map((collection) => (
              <div
                key={collection._id}
                onClick={() => handleCollectionClick(collection._id)}
                className="bg-dark-300 rounded-xl overflow-hidden border border-gray-800/50 hover:border-brand-500/30
                         transition-all duration-300 cursor-pointer hover:shadow-lg hover:shadow-brand-500/5 group"
              >
                <div className="h-36 bg-dark-200 flex items-center justify-center relative">
                  <Folder
                    size={48}
                    className="text-gray-500 group-hover:text-brand-300 transition-colors"
                  />
                  <div
                    className="absolute bottom-3 right-3 bg-dark-300/90 backdrop-blur-sm rounded-full px-3 py-1
                               flex items-center gap-1 text-xs text-gray-400 border border-gray-700/50"
                  >
                    <Package size={12} strokeWidth={1.5} />
                    <span>
                      {Array.isArray(collection.products)
                        ? collection.products.filter(
                            (item) =>
                              item && typeof item === "object" && item._id
                          ).length
                        : 0}
                    </span>
                  </div>

                  {/* Add delete button in the top right corner */}
                  <div
                    className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <button
                      onClick={(e) => handleDeleteCollection(collection._id, e)}
                      className="p-2 rounded-full bg-red-500/30 backdrop-blur-sm hover:bg-red-500/50 transition-colors"
                      title="Delete Collection"
                    >
                      <Trash
                        size={16}
                        strokeWidth={1.5}
                        className="text-white"
                      />
                    </button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-gray-200 font-light text-lg group-hover:text-brand-300 transition-colors">
                    {collection.name}
                  </h3>
                  <p className="text-gray-500 text-sm mt-1 line-clamp-2">
                    {collection.description}
                  </p>
                  <div className="flex items-center justify-between mt-4">
                    <span className="text-xs text-gray-500">
                      {new Date(collection.updatedAt).toLocaleDateString()}
                    </span>
                    <div className="flex items-center gap-1 text-brand-400 group-hover:text-brand-300 transition-colors">
                      <span className="text-xs">View Models</span>
                      <ChevronRight size={14} strokeWidth={1.5} />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )
      ) : /* All Models Grid View */
      filteredModels.length === 0 ? (
        <div className="mt-8 text-center">
          <div className="p-8 rounded-lg bg-dark-300/50 border border-gray-800/50">
            <p className="text-gray-400 font-light">No 3D models found</p>
            <button
              className="mt-4 btn btn-primary"
              onClick={() => setShowSingleUploadModal(true)}
            >
              Upload your first model
            </button>
          </div>
        </div>
      ) : (
        <div
          className={`mt-8 ${
            viewMode === "grid"
              ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6"
              : "space-y-4"
          }`}
        >
          {filteredModels.map((model) => (
            <ModelCard
              key={model._id}
              model={model}
              viewMode={viewMode}
              onPublish={() => handlePublishModel(model._id)}
              onDuplicate={() => handleDuplicateModel(model._id)}
              onDelete={() => handleDeleteModel(model._id)}
              onAddToCollection={() => handleAddToCollection(model)}
              onClick={() => handleModelClick(model)}
            />
          ))}
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 flex items-center justify-between">
          <span>{error}</span>
          <button
            onClick={clearError}
            className="p-1 hover:bg-red-500/20 rounded-lg transition-colors"
          >
            <X size={16} strokeWidth={1.5} />
          </button>
        </div>
      )}

      {/* Add to Collection Modal */}
      {showAddToCollectionModal && selectedModel && (
        <AddToCollectionModal
          model={selectedModel}
          onClose={() => setShowAddToCollectionModal(false)}
          onSuccess={handleAddToCollectionSuccess}
        />
      )}

      {showImportModal && (
        <ProductImportModal
          onClose={() => setShowImportModal(false)}
          onImportComplete={(result: ProductImportResult) => {
            if (result.success) {
              refreshModels();
            }
            setShowImportModal(false);
          }}
        />
      )}

      {/* Delete Collection Confirmation Modal */}
      {showDeleteCollectionConfirm && (
        <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-dark-300 rounded-xl w-full max-w-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-light text-gray-100">
                Delete Collection
              </h3>
              <button
                onClick={() => setShowDeleteCollectionConfirm(false)}
                className="p-1 rounded-lg hover:bg-dark-200/50 text-gray-400 transition-colors"
              >
                <X size={20} strokeWidth={1.5} />
              </button>
            </div>
            <p className="text-gray-300 mb-6">
              Are you sure you want to delete this collection? This action
              cannot be undone. The models in this collection will not be
              deleted from your library.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowDeleteCollectionConfirm(false)}
                className="btn btn-secondary"
                disabled={isDeletingCollection}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteCollection}
                className="btn bg-red-500 hover:bg-red-600 text-white flex items-center gap-2"
                disabled={isDeletingCollection}
              >
                {isDeletingCollection ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                    <span>Deleting...</span>
                  </>
                ) : (
                  <>
                    <Trash size={16} strokeWidth={1.5} />
                    <span>Delete</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Shopify Sync Modal */}
      <ShopifySyncModal
        isOpen={showShopifySyncModal}
        onClose={() => setShowShopifySyncModal(false)}
        onSuccess={(result) => {
          toast.success(
            `Successfully synced ${result.data.success.length} products to Shopify`
          );
          refreshModels(); // Refresh models to show updated integration status
        }}
      />
    </div>
  );
};

export default Library;
