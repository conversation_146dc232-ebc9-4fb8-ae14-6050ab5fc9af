import React from 'react';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  hoverEffect?: 'glow' | 'lift' | 'none';
}

export const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className = '',
  style = {},
  hoverEffect = 'none', // Default to none for better performance
  ...props
}) => {
  // Removed hover state tracking for performance

  // Base styles with minimal effects
  const baseStyles = "bg-[#12121A]/40 backdrop-blur-md border border-white/5 rounded-xl p-6 transition-all duration-300";

  return (
    <div
      className={`${baseStyles} ${className}`}
      style={{
        // Simple box shadow without effects
        boxShadow: '0 4px 30px rgba(0, 0, 0, 0.1)',
        ...style
      }}
      {...props}
    >
      {children}
    </div>
  );
};