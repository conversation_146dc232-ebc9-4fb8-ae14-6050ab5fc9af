const mongoose = require("mongoose");

const commentModelSchema = new mongoose.Schema(
  {
    content: {
      type: String,
    },
    user: {
      type: mongoose.Types.ObjectId,
      ref: "User",
    },
    product_id: {
      type: mongoose.Types.ObjectId,
      ref: "GlbModel",
    },
  },

  { timestamps: true, collection: "comments" }
);

const glbModel = mongoose.model(
  "CommentModel",
  commentModelSchema,
  "CommentModel"
);
module.exports = glbModel;
