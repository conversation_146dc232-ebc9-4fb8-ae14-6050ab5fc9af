const router = require("express").Router();
const controller = require("../../controllers/magentoController");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
const setMagentoClient = require("../../utils/magento");

router.get(
  "/products",
  setMagentoClient,
  asyncError<PERSON><PERSON>ler(controller.getAllProducts)
);
router.get(
  "/statistics",
  setMagentoClient,
  asyncErrorHandler(controller.getStatistics)
);
router.post(
  "/save-products",
  setMagentoClient,
  asyncErrorHandler(controller.saveAllProducts)
);

module.exports = router;
