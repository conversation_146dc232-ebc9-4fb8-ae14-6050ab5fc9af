const Shopify = require('shopify-api-node');

const setShopifyClient = (req, res, next) => {
  const { shopName, apiKey, password } = req.body;

  if (!shopName || !apiKey || !password) {
    return res.status(400).json({ success: false, message: 'Missing Shopify credentials' });
  }

  // Attach Shopify client to the request object
  req.shopifyClient = new Shopify({
    shopName,
    apiKey,
    password, // Admin API access token
  });

  next();
};

module.exports = setShopifyClient;
