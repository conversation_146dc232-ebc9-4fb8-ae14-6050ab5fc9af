import React, { lazy, Suspense } from 'react';
import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { <PERSON> } from '../../components/sections/Hero';
import { Problem } from '../../components/sections/Problem';
import { Solution } from '../../components/sections/Solution';
import { Features } from '../../components/sections/Features';
import { ParticleField } from '../../components/ui/ParticleField';

// Lazy load non-critical components
const Testimonials = lazy(() => import('../../components/sections/Testimonials').then(module => ({ default: module.Testimonials })));
const Pricing = lazy(() => import('../../components/sections/Pricing').then(module => ({ default: module.Pricing })));
const FAQ = lazy(() => import('../../components/sections/FAQ').then(module => ({ default: module.FAQ })));
const About = lazy(() => import('../../components/sections/About').then(module => ({ default: module.About })));
const CTA = lazy(() => import('../../components/sections/CTA').then(module => ({ default: module.CTA })));
const AIAssistant = lazy(() => import('../../components/ui/AIAssistant').then(module => ({ default: module.AIAssistant })));

function Landing() {
  return (
    <div className="min-h-screen bg-[#0A0A10] text-white overflow-hidden">
      {/* Background with particles */}
      <div className="fixed inset-0 z-0 bg-gradient-to-br from-[#080810] to-[#101020]">
        <div className="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-[#0066CC]/5 rounded-full filter blur-[150px]"></div>
        <div className="absolute bottom-1/4 right-1/4 w-1/2 h-1/2 bg-[#9747FF]/5 rounded-full filter blur-[150px]"></div>
        
        {/* Particle field with optimized settings */}
        <ParticleField 
          particleCount={30}
          particleSize={2.5}
          speed={0.2}
          blur={0}
          interactive={true}
          depth={true}
        />
      </div>
      
      <Header />
      <main>
        <Hero />
        <Problem />
        <Solution />
        <Features />
        
        {/* Lazy load lower sections */}
        <Suspense fallback={<div className="py-24 text-center">Loading...</div>}>
          <Testimonials />
        </Suspense>
        <Suspense fallback={<div className="py-24 text-center">Loading...</div>}>
          <Pricing />
        </Suspense>
        <Suspense fallback={<div className="py-24 text-center">Loading...</div>}>
          <FAQ />
        </Suspense>
        <Suspense fallback={<div className="py-24 text-center">Loading...</div>}>
          <About />
        </Suspense>
        <Suspense fallback={<div className="py-24 text-center">Loading...</div>}>
          <CTA />
        </Suspense>
      </main>
      <Footer />
      
      {/* AI Assistant (lazy loaded) */}
      <Suspense fallback={null}>
        <AIAssistant />
      </Suspense>
    </div>
  );
}

export default Landing;