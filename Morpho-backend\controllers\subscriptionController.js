const {
  STRIPE_SECRET_KEY,
  STRIPE_PUBLISHABLE_KEY,
  STRIPE_WEBHOOK_SECRET,
  BASE_URL,
} = require("../config/envVariables");
const stripe = require("stripe")(STRIPE_SECRET_KEY);
const User = require("../models/User");
const expressError = require("../errors/expressError");

// Helper function to determine plan from subscription price
function determinePlanFromPrice(subscription) {
  // Get the price amount from the subscription
  const priceAmount = subscription?.items?.data[0]?.price?.unit_amount || 0;

  // Determine plan based on price amount (in cents)
  if (priceAmount <= 100) {
    // Free or test
    return "free";
  } else if (priceAmount <= 5000) {
    // $50 or less, likely basic plan ($49)
    return "basic";
  } else if (priceAmount <= 18000) {
    // $180 or less, likely pro plan ($179)
    return "pro";
  } else {
    // More than $180, likely enterprise plan ($400+)
    return "enterprise";
  }
}

// Helper function to determine product limit from plan
function determineProductLimitFromPlan(plan) {
  switch (plan) {
    case "free":
      return 1;
    case "basic":
      return 25;
    case "pro":
      return 150;
    case "enterprise":
      return Infinity;
    default:
      return 25; // Default to basic plan limit
  }
}

// Helper function to update a user's subscription plan directly
async function updateUserSubscriptionPlan(userId, plan) {
  // Validate required parameters
  if (!userId || !plan) {
    console.error("Missing userId or plan in updateUserSubscriptionPlan");
    return null;
  }

  // Determine product limit based on plan
  const productLimit = determineProductLimitFromPlan(plan);

  // Update data
  const updateData = {
    subscriptionStatus: "active",
    subscriptionPlan: plan,
    productLimit: productLimit,
  };

  // Update the user and return the updated document
  const updatedUser = await User.findByIdAndUpdate(userId, updateData, {
    new: true,
  });

  return updatedUser;
}

// Function to ensure webhook is registered with Stripe (only for production)
async function ensureWebhookRegistered() {
  // Skip webhook registration in development environment
  const isDevelopment =
    process.env.NODE_ENV === "development" || !process.env.NODE_ENV;

  if (isDevelopment) {
    console.log("Skipping webhook registration in development environment.");
    console.log(
      "Please use the Stripe CLI to forward webhook events to your local server:"
    );
    console.log(
      "stripe listen --forward-to http://localhost:5000/morpho/subscription/webhook"
    );
    return null;
  }

  // Get all webhooks
  const webhooks = await stripe.webhookEndpoints.list();
  const webhookUrl = `${BASE_URL}/morpho/subscription/webhook`;

  // Check if our webhook URL is already registered
  const existingWebhook = webhooks.data.find(
    (webhook) => webhook.url === webhookUrl
  );

  if (existingWebhook) {
    return existingWebhook;
  }

  // If not registered, create a new webhook
  const webhook = await stripe.webhookEndpoints.create({
    url: webhookUrl,
    enabled_events: [
      "checkout.session.completed",
      "customer.subscription.created",
      "customer.subscription.updated",
      "customer.subscription.deleted",
      "invoice.payment_succeeded",
      "invoice.payment_failed",
    ],
  });

  return webhook;
}

// Try to register webhook on startup
ensureWebhookRegistered().catch((err) =>
  console.error("Failed to register webhook:", err)
);

// Get subscription plans
exports.getSubscriptionPlans = async (req, res) => {
  const userId = req.user;

  // Validate user authentication
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Get user email for prefilling Stripe checkout
  const user = await User.findById(userId).select("email");
  if (!user) {
    throw new expressError(
      "User not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Generate a unique idempotency key for this request
  const timestamp = Date.now();
  const idempotencyKey = `${userId}_${timestamp}`;

  // Get the base URL for success redirect
  const baseUrl = process.env.CLIENT_URL || "http://localhost:3000";
  // Use the same timestamp for all URLs to keep them consistent
  const successUrl = `${baseUrl}/payment/success?plan=basic&t=${timestamp}`;

  // Append user email, client reference ID, success URL, and idempotency key to the payment links
  // Use test payment links for now, replace with production links when going live
  const basicPlanLink = `https://buy.stripe.com/test_8wMcOq20X46vbSw289?prefilled_email=${encodeURIComponent(
    user.email
  )}&client_reference_id=${userId}&idempotency_key=${idempotencyKey}_basic&success_url=${encodeURIComponent(
    successUrl
  )}`;

  // Basic Annual Plan
  const basicAnnualSuccessUrl = `${baseUrl}/payment/success?plan=basic-annual&t=${timestamp}`;
  const basicAnnualPlanLink = `https://buy.stripe.com/test_aEU3dQ4951Yn2hW4gk?prefilled_email=${encodeURIComponent(
    user.email
  )}&client_reference_id=${userId}&idempotency_key=${idempotencyKey}_basic_annual&success_url=${encodeURIComponent(
    basicAnnualSuccessUrl
  )}`;

  // Pro Monthly Plan
  const proSuccessUrl = `${baseUrl}/payment/success?plan=pro&t=${timestamp}`;
  const proPlanLink = `https://buy.stripe.com/test_cN23dQeNJ0Uj8Gk002?prefilled_email=${encodeURIComponent(
    user.email
  )}&client_reference_id=${userId}&idempotency_key=${idempotencyKey}_pro&success_url=${encodeURIComponent(
    proSuccessUrl
  )}`;

  // Pro Annual Plan
  const proAnnualSuccessUrl = `${baseUrl}/payment/success?plan=pro-annual&t=${timestamp}`;
  const proAnnualPlanLink = `https://buy.stripe.com/test_fZebKmeNJ6eDcWA8wB?prefilled_email=${encodeURIComponent(
    user.email
  )}&client_reference_id=${userId}&idempotency_key=${idempotencyKey}_pro_annual&success_url=${encodeURIComponent(
    proAnnualSuccessUrl
  )}`;

  // Enterprise Plan
  const enterpriseSuccessUrl = `${baseUrl}/payment/success?plan=enterprise&t=${timestamp}`;
  const enterprisePlanLink = `https://buy.stripe.com/test_9AQ3dQ6hd8mLcWAcMP?prefilled_email=${encodeURIComponent(
    user.email
  )}&client_reference_id=${userId}&idempotency_key=${idempotencyKey}_enterprise&success_url=${encodeURIComponent(
    enterpriseSuccessUrl
  )}`;

  // Define the subscription plans with the Stripe payment links
  const plans = [
    {
      id: "free",
      name: "Starter",
      description: "Get started with 3D product visualization",
      price: 0,
      interval: "month",
      productLimit: 1,
      features: [
        "1 product in 3D",
        "Core 3D/AR viewer",
        "iOS scanning app",
        "Basic stats",
        "Community support",
      ],
      paymentLink: null, // Free plan doesn't need a payment link
    },
    {
      id: "basic",
      name: "Basic",
      description: "Perfect for small Shopify stores with a moderate catalog",
      price: 49,
      interval: "month",
      productLimit: 25,
      features: [
        "Up to 25 products in 3D",
        "Core features",
        "Free integration with e-commerce platforms",
        "Basic analytics",
        "Standard support",
        "10% off model services",
        "Encrypted data",
        "Model compression",
      ],
      paymentLink: basicPlanLink,
    },
    {
      id: "basic-annual",
      name: "Basic (Annual)",
      description: "Perfect for small Shopify stores with a moderate catalog",
      price: 499,
      interval: "year",
      productLimit: 25,
      features: [
        "Up to 25 products in 3D",
        "Core features",
        "Free integration with e-commerce platforms",
        "Basic analytics",
        "Standard support",
        "10% off model services",
        "Encrypted data",
        "Model compression",
        "Save $89 with annual billing",
      ],
      paymentLink: basicAnnualPlanLink,
    },
    {
      id: "pro",
      name: "Pro",
      description: "Ideal for growing Shopify brands with larger catalogs",
      price: 179,
      interval: "month",
      productLimit: 150,
      features: [
        "Up to 150 products in 3D",
        "Full analytics",
        "Priority support",
        "Custom branding",
        "API access",
        "Unlimited model conversions",
        "20% off modeling services",
        "Encrypted data",
        "Model compression",
      ],
      paymentLink: proPlanLink,
    },
    {
      id: "pro-annual",
      name: "Pro (Annual)",
      description: "Ideal for growing Shopify brands with larger catalogs",
      price: 1789,
      interval: "year",
      productLimit: 150,
      features: [
        "Up to 150 products in 3D",
        "Full analytics",
        "Priority support",
        "Custom branding",
        "API access",
        "Unlimited model conversions",
        "20% off modeling services",
        "Encrypted data",
        "Model compression",
        "Save $359 with annual billing",
      ],
      paymentLink: proAnnualPlanLink,
    },
    {
      id: "enterprise",
      name: "Enterprise",
      description: "Custom solution for large Shopify Plus merchants",
      price: null,
      customPricing: true,
      interval: "month",
      productLimit: Infinity, // Unlimited products (1000+)
      features: [
        "Unlimited products (1000+)",
        "Dedicated success manager",
        "SLA",
        "API/SDK",
        "White-label",
        "Full analytics",
        "Custom encryption protocols",
        "Advanced compression",
        "Large model bundles or custom services",
      ],
      paymentLink: enterprisePlanLink,
    },
  ];

  return res.status(200).json({
    success: true,
    data: plans,
  });
};

// Get Stripe publishable key
exports.getPublishableKey = (req, res) => {
  return res.status(200).json({
    success: true,
    publishableKey: STRIPE_PUBLISHABLE_KEY,
  });
};

// Webhook handler for Stripe events
exports.handleWebhook = async (req, res) => {
  // Log the first part of the body for debugging
  if (typeof req.body === "object") {
  } else if (typeof req.body === "string") {
  } else if (Buffer.isBuffer(req.body)) {
  }

  const sig = req.headers["stripe-signature"];
  if (!sig) {
    console.error("No Stripe signature found in headers");
    return res.status(400).send("No Stripe signature found");
  }

  // Try different webhook secrets
  // When using Stripe CLI, it outputs a webhook secret that starts with 'whsec_'
  const webhookSecret =
    STRIPE_WEBHOOK_SECRET ||
    process.env.STRIPE_WEBHOOK_SECRET ||
    process.env.STRIPE_WEBHOOK_SECRECT ||
    process.env.WEBHOOK_SECRET; // Sometimes Stripe CLI uses this variable name

  if (!webhookSecret) {
    console.error("No webhook secret found in environment variables");
    return res.status(500).send("Webhook secret not configured");
  }

  let event;

  try {
    // Use rawBody if available, otherwise fall back to body
    let payload;
    if (req.rawBody) {
      payload = req.rawBody;
    } else if (Buffer.isBuffer(req.body)) {
      payload = req.body;
    } else if (typeof req.body === "string") {
      payload = req.body;
    } else {
      payload = JSON.stringify(req.body);
    }

    // Verify the webhook signature
    event = stripe.webhooks.constructEvent(payload, sig, webhookSecret);
  } catch (err) {
    console.error(`Webhook Error: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  try {
    switch (event.type) {
      case "checkout.session.completed":
        const session = event.data.object;

        // Extract customer email and client reference ID (user ID)
        const customerEmail = session.customer_details?.email;
        const userId = session.client_reference_id;
        const subscriptionId = session.subscription;
        const customerId = session.customer;

        if (!customerEmail && !userId) {
          console.error("No customer email or user ID found in session");
          break;
        }

        // If we have a subscription ID, try to get more details from Stripe
        let stripeSubscription = null;
        if (subscriptionId) {
          try {
            // Generate an idempotency key for this API call
            const idempotencyKey = `retrieve_subscription_${subscriptionId}_${Date.now()}`;

            stripeSubscription = await stripe.subscriptions.retrieve(
              subscriptionId,
              { idempotencyKey } // Add idempotency key to the request
            );
          } catch (err) {
            console.error("Error retrieving subscription from Stripe:", err);
          }
        } else {
          console.warn(
            "No subscription ID found in session, might be a one-time payment"
          );

          // If we have a customer ID but no subscription ID, try to find subscriptions for this customer
          if (customerId) {
            try {
              // Generate an idempotency key for this API call
              const idempotencyKey = `list_subscriptions_${customerId}_${Date.now()}`;

              const subscriptions = await stripe.subscriptions.list(
                {
                  customer: customerId,
                  limit: 1,
                  status: "active",
                },
                { idempotencyKey } // Add idempotency key to the request
              );

              if (subscriptions.data.length > 0) {
                stripeSubscription = subscriptions.data[0];
              }
            } catch (err) {
              console.error("Error listing subscriptions for customer:", err);
            }
          }
        }

        // Determine the plan type from the price ID or amount
        let subscriptionPlan = "basic"; // Default to basic
        let productLimit = 25; // Default to basic plan limit

        // Try to extract plan from metadata or URL parameters
        // Check if the success_url contains a plan parameter
        if (session.success_url) {
          try {
            const url = new URL(session.success_url);
            const planParam = url.searchParams.get("plan");
            if (planParam) {
              subscriptionPlan = planParam;
              productLimit = determineProductLimitFromPlan(planParam);
            }
          } catch (err) {
            console.error("Error parsing success_url:", err);
          }
        }

        // If we couldn't determine the plan from the URL, fall back to amount
        if (!subscriptionPlan || subscriptionPlan === "basic") {
          // Determine plan based on amount (in cents)
          const amountTotal = session.amount_total || 0;

          if (amountTotal <= 100) {
            // Free plan or test
            subscriptionPlan = "free";
            productLimit = 1;
          } else if (amountTotal <= 10000) {
            // $100 or less, likely basic plan ($49)
            subscriptionPlan = "basic";
            productLimit = 25;
          } else if (amountTotal <= 20000) {
            // $200 or less, likely pro plan ($179)
            subscriptionPlan = "pro";
            productLimit = 150;
          } else {
            // More than $200, likely enterprise plan ($400+)
            subscriptionPlan = "enterprise";
            productLimit = Infinity;
          }
        }

        // Update user by ID if available, otherwise fall back to email
        const query = userId ? { _id: userId } : { email: customerEmail };

        try {
          // First check if user exists
          const existingUser = await User.findOne(query);
          if (!existingUser) {
            console.error(`User not found with query:`, query);
            break;
          }

          // Prepare update data
          const updateData = {
            subscriptionStatus: "active",
            subscriptionPlan: subscriptionPlan,
            productLimit: productLimit,
          };

          // If productCount doesn't exist, initialize it to 0
          if (existingUser.productCount === undefined) {
            updateData.productCount = 0;
          }

          // Add subscription ID if available
          if (subscriptionId || (stripeSubscription && stripeSubscription.id)) {
            updateData.subscriptionId = subscriptionId || stripeSubscription.id;
          }

          // Update the user
          const updateResult = await User.findOneAndUpdate(query, updateData, {
            new: true,
          });

          if (updateResult) {
            // Double-check that the update was successful
            const verifyUser = await User.findOne(query);

            // Subscription update successful
          } else {
            console.error(`Failed to update user with query:`, query);
          }
        } catch (error) {
          console.error("Error updating user subscription:", error);
        }
        break;

      case "customer.subscription.updated":
        const subscription = event.data.object;

        // Map Stripe subscription status to our status
        let subscriptionStatus = subscription.status;

        // Handle special cases
        if (subscription.cancel_at_period_end) {
          subscriptionStatus = "canceling";
        }

        try {
          // First check if user exists with this subscription ID
          const existingSubscriber = await User.findOne({
            subscriptionId: subscription.id,
          });

          if (!existingSubscriber) {
            console.error(
              `No user found with subscription ID: ${subscription.id}`
            );

            // Try to find by customer ID if available
            if (subscription.customer) {
              // Get customer email from Stripe
              // Generate an idempotency key for this API call
              const idempotencyKey = `retrieve_customer_${
                subscription.customer
              }_${Date.now()}`;

              const customer = await stripe.customers.retrieve(
                subscription.customer,
                { idempotencyKey } // Add idempotency key to the request
              );

              if (customer && customer.email) {
                // Try to find user by email
                const userByEmail = await User.findOne({
                  email: customer.email,
                });

                if (userByEmail) {
                  // Update the user with the subscription ID and status
                  const updateByEmailResult = await User.findOneAndUpdate(
                    { email: customer.email },
                    {
                      subscriptionId: subscription.id,
                      subscriptionStatus: subscriptionStatus,
                      // Determine plan and product limit based on price
                      subscriptionPlan: determinePlanFromPrice(subscription),
                      productLimit: determineProductLimitFromPlan(
                        determinePlanFromPrice(subscription)
                      ),
                    },
                    { new: true }
                  );

                  if (updateByEmailResult) {
                    // Double-check that the update was successful
                    const verifyUser = await User.findOne({
                      email: customer.email,
                    });
                  }
                }
              }
            }
            break;
          }

          // Determine the plan from the subscription
          const plan = determinePlanFromPrice(subscription);
          const productLimit = determineProductLimitFromPlan(plan);

          console.log(
            `Updating subscription with ID ${subscription.id} to plan ${plan} with limit ${productLimit}`
          );

          // Update the user in the database with plan and product limit
          const subUpdateResult = await User.findOneAndUpdate(
            { subscriptionId: subscription.id },
            {
              subscriptionStatus: subscriptionStatus,
              subscriptionPlan: plan,
              productLimit: productLimit,
            },
            { new: true }
          );

          if (subUpdateResult) {
            // Double-check that the update was successful
            const verifyUser = await User.findOne({
              subscriptionId: subscription.id,
            });

            // Subscription update successful
          }
        } catch (error) {
          console.error("Error updating subscription status:", error);
        }
        break;

      case "customer.subscription.deleted":
        const canceledSubscription = event.data.object;

        // Update the user in the database
        const cancelResult = await User.findOneAndUpdate(
          { subscriptionId: canceledSubscription.id },
          {
            subscriptionStatus: "canceled",
            subscriptionId: null,
            subscriptionPlan: "free",
            productLimit: 1, // Reset to free tier limit
          },
          { new: true }
        );

        // Subscription canceled successfully

        break;

      case "invoice.payment_succeeded":
        const successInvoice = event.data.object;

        // Only process subscription invoices
        if (successInvoice.subscription) {
          try {
            // Get the subscription details
            const subscriptionId = successInvoice.subscription;
            console.log(
              `Processing payment success for subscription: ${subscriptionId}`
            );

            // Retrieve the subscription from Stripe
            const stripeSubscription = await stripe.subscriptions.retrieve(
              subscriptionId
            );

            // Determine the plan from the subscription
            const plan = determinePlanFromPrice(stripeSubscription);
            const productLimit = determineProductLimitFromPlan(plan);

            console.log(
              `Payment succeeded for subscription ${subscriptionId}, updating to plan ${plan} with limit ${productLimit}`
            );

            // Update the user with the plan and product limit
            const updateResult = await User.findOneAndUpdate(
              { subscriptionId: subscriptionId },
              {
                subscriptionStatus: "active",
                subscriptionPlan: plan,
                productLimit: productLimit,
              },
              { new: true }
            );
          } catch (err) {
            console.error(
              `Error processing payment success for subscription: ${successInvoice.subscription}`,
              err
            );
          }
        }
        break;

      case "invoice.payment_failed":
        const failedInvoice = event.data.object;

        // Only process subscription invoices
        if (failedInvoice.subscription) {
          const updatedUser = await User.findOneAndUpdate(
            { subscriptionId: failedInvoice.subscription },
            { subscriptionStatus: "past_due" },
            { new: true }
          );

          // Payment failed and subscription updated
        }
        break;

      default:
    }
  } catch (error) {
    console.error(`Error processing webhook event ${event.type}:`, error);
    // Still return 200 to acknowledge receipt of the event
    // This prevents Stripe from retrying the webhook
  }

  // Return a 200 response to acknowledge receipt of the event
  res.status(200).json({ received: true });
};

// Get current user subscription status
exports.getUserSubscription = async (req, res) => {
  const userId = req.user;

  // Validate user authentication
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Find user by ID
  const user = await User.findById(userId)
    .select(
      "subscriptionStatus subscriptionPlan subscriptionId productLimit productCount"
    )
    .lean();

  // Check if user exists
  if (!user) {
    throw new expressError(
      "User not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Handle 'none' status by converting it to 'inactive'
  let status = user.subscriptionStatus || "inactive";
  if (status === "none") {
    status = "inactive";

    // Update the user in the database
    await User.findByIdAndUpdate(userId, { subscriptionStatus: "inactive" });
  }

  // Set default product limit based on plan if not already set
  let productLimit = user.productLimit;
  if (productLimit === undefined) {
    productLimit = determineProductLimitFromPlan(
      user.subscriptionPlan || "free"
    );

    // Update the user with the correct product limit
    await User.findByIdAndUpdate(userId, { productLimit });
  }

  // Prepare response data
  const responseData = {
    success: true,
    data: {
      status: status,
      plan: user.subscriptionPlan || "free",
      subscriptionId: user.subscriptionId || null,
      productLimit: productLimit,
      productCount: user.productCount || 0,
    },
  };

  return res.status(200).json(responseData);
};

// Simulate webhook event (for testing)
exports.simulateWebhook = async (req, res) => {
  const { userId, plan } = req.body;

  // Validate required fields
  if (!userId || !plan) {
    throw new expressError(
      "User ID and plan are required",
      400,
      expressError.CODES.MISSING_REQUIRED_FIELD
    );
  }

  // Validate plan
  if (!["free", "basic", "pro", "enterprise"].includes(plan)) {
    throw new expressError(
      "Invalid plan. Must be one of: free, basic, pro, enterprise",
      400,
      expressError.CODES.INVALID_DATA_FORMAT
    );
  }

  // Determine product limit based on plan
  let productLimit = determineProductLimitFromPlan(plan);

  // Update the user
  const updateData = {
    subscriptionStatus: "active",
    subscriptionPlan: plan,
    productLimit: productLimit,
    // Generate a fake subscription ID for testing
    subscriptionId: `sub_test_${Date.now()}`,
  };

  // Find and update user
  const user = await User.findByIdAndUpdate(userId, updateData, {
    new: true,
  });

  // Check if user exists
  if (!user) {
    throw new expressError(
      "User not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Return success response
  return res.status(200).json({
    success: true,
    message: `Subscription updated to ${plan} plan via simulated webhook`,
    data: {
      status: user.subscriptionStatus,
      plan: user.subscriptionPlan,
      productLimit: user.productLimit,
      subscriptionId: user.subscriptionId,
    },
  });
};

// Direct update subscription plan
exports.directUpdatePlan = async (req, res) => {
  const userId = req.user;
  const { plan } = req.body;

  // Validate user authentication
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Validate required fields
  if (!plan) {
    throw new expressError(
      "Plan is required",
      400,
      expressError.CODES.MISSING_REQUIRED_FIELD
    );
  }

  // Validate plan
  if (!["free", "basic", "pro", "enterprise"].includes(plan)) {
    throw new expressError(
      "Invalid plan. Must be one of: free, basic, pro, enterprise",
      400,
      expressError.CODES.INVALID_DATA_FORMAT
    );
  }

  // Use the helper function to update the user's subscription plan
  const updatedUser = await updateUserSubscriptionPlan(userId, plan);

  // Check if update was successful
  if (!updatedUser) {
    throw new expressError(
      "Failed to update subscription plan",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Return success response
  return res.status(200).json({
    success: true,
    message: `Subscription updated to ${plan} plan`,
    data: {
      status: updatedUser.subscriptionStatus,
      plan: updatedUser.subscriptionPlan,
      subscriptionId: updatedUser.subscriptionId || null,
      productLimit: updatedUser.productLimit,
      productCount: updatedUser.productCount || 0,
    },
  });
};

// Manually trigger a webhook event for a specific subscription
exports.manualWebhook = async (req, res) => {
  try {
    const { subscriptionId, event_type } = req.body;

    if (!subscriptionId) {
      return res.status(400).json({
        success: false,
        message: "Subscription ID is required",
      });
    }

    // Default to payment_succeeded if no event type is provided
    const eventType = event_type || "invoice.payment_succeeded";

    // Retrieve the subscription from Stripe
    try {
      const stripeSubscription = await stripe.subscriptions.retrieve(
        subscriptionId
      );

      // Determine the plan from the subscription
      const plan = determinePlanFromPrice(stripeSubscription);
      const productLimit = determineProductLimitFromPlan(plan);

      // Find the user with this subscription ID
      const user = await User.findOne({ subscriptionId: subscriptionId });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: `No user found with subscription ID: ${subscriptionId}`,
        });
      }

      // Update the user with the plan and product limit
      const updateResult = await User.findOneAndUpdate(
        { subscriptionId: subscriptionId },
        {
          subscriptionStatus: "active",
          subscriptionPlan: plan,
          productLimit: productLimit,
        },
        { new: true }
      );

      if (updateResult) {
        return res.status(200).json({
          success: true,
          message: `Successfully processed manual webhook for subscription: ${subscriptionId}`,
          data: {
            status: updateResult.subscriptionStatus,
            plan: updateResult.subscriptionPlan,
            subscriptionId: updateResult.subscriptionId,
            productLimit: updateResult.productLimit,
            productCount: updateResult.productCount || 0,
          },
        });
      } else {
        return res.status(500).json({
          success: false,
          message: `Failed to update user with subscription ID: ${subscriptionId}`,
        });
      }
    } catch (err) {
      console.error(
        `Error retrieving subscription from Stripe: ${subscriptionId}`,
        err
      );
      return res.status(500).json({
        success: false,
        message: "Error retrieving subscription from Stripe",
        error: err.message,
      });
    }
  } catch (error) {
    console.error("Error processing manual webhook:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while processing the manual webhook",
      error: error.message,
    });
  }
};

// Update subscription (for testing)
exports.updateSubscription = async (req, res) => {
  const userId = req.user;
  const { plan } = req.body;

  // Validate user authentication
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Validate required fields
  if (!plan) {
    throw new expressError(
      "Plan is required",
      400,
      expressError.CODES.MISSING_REQUIRED_FIELD
    );
  }

  // Determine product limit based on plan
  let productLimit;
  switch (plan) {
    case "free":
      productLimit = 1;
      break;
    case "basic":
      productLimit = 25;
      break;
    case "pro":
      productLimit = 150;
      break;
    case "enterprise":
      productLimit = Infinity;
      break;
    default:
      throw new expressError(
        "Invalid plan. Must be one of: free, basic, pro, enterprise",
        400,
        expressError.CODES.INVALID_DATA_FORMAT
      );
  }

  // Update the user
  const updateData = {
    subscriptionStatus: "active",
    subscriptionPlan: plan,
    productLimit: productLimit,
  };

  // Find and update user
  const user = await User.findByIdAndUpdate(userId, updateData, {
    new: true,
  });

  // Check if user exists
  if (!user) {
    throw new expressError(
      "User not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Return success response
  return res.status(200).json({
    success: true,
    message: `Subscription updated to ${plan} plan`,
    data: {
      status: user.subscriptionStatus,
      plan: user.subscriptionPlan,
      productLimit: user.productLimit,
    },
  });
};

// Cancel subscription
exports.cancelSubscription = async (req, res) => {
  const userId = req.user;

  // Validate user authentication
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Find user by ID
  const user = await User.findById(userId);

  // Check if user exists
  if (!user) {
    throw new expressError(
      "User not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Check if user has an active subscription
  if (!user.subscriptionId) {
    throw new expressError(
      "No active subscription found",
      400,
      expressError.CODES.INVALID_REQUEST
    );
  }

  // Generate an idempotency key for this cancellation request
  const idempotencyKey = `cancel_subscription_${
    user.subscriptionId
  }_${Date.now()}`;

  // Cancel the subscription in Stripe
  const canceledSubscription = await stripe.subscriptions.update(
    user.subscriptionId,
    { cancel_at_period_end: true },
    { idempotencyKey } // Add idempotency key to the request
  );

  // Get the current period end date
  const periodEnd = new Date(canceledSubscription.current_period_end * 1000);
  const formattedDate = periodEnd.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Update user in database
  user.subscriptionStatus = "canceling";
  await user.save();

  // Return success response
  return res.status(200).json({
    success: true,
    message: `Your subscription will be canceled on ${formattedDate}. You can continue using your subscription until then.`,
    data: {
      status: "canceling",
      periodEnd: formattedDate,
    },
  });
};
