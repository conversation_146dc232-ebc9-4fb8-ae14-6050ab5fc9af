const mongoose = require("mongoose");

const productionSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: "Project",
    },
    imageUrl: {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
  },
  { timestamps: true, collection: "productions" }
);

const productionModel = mongoose.model("Production", productionSchema);
module.exports = productionModel;
