const axios = require('axios');

const setWebflowClient = (req, res, next) => {
  const { token, collectionId } = req.body;

  if (!token || !collectionId) {
    return res.status(400).json({ success: false, message: 'Missing Webflow credentials' });
  }

  // Attach Webflow client to the request object
  req.webflowClient = axios.create({
    baseURL: `https://api.webflow.com/v2/collections/${collectionId}/items`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  next();
};

module.exports = setWebflowClient;
