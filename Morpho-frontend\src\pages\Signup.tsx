import React, { useState } from "react";
import { Box, ArrowLeft } from "lucide-react";
import { useAuth } from "../hooks/useAuth";
import { Navigate, Link, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import type { SignupCredentials } from "../types/auth";

const Signup = () => {
  const { signup, loading, error, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<SignupCredentials>({
    first_name: "",
    last_name: "",
    username: "",
    email: "",
    password: "",
    // Explicitly set the Client role ID
    role: "6360d5055c6333b9c8385432",
    phoneNumber: "",
    country: "",
    companyName: "",
    industry: "",
    companyWebsite: "",
  });
  const [confirmPassword, setConfirmPassword] = useState("");

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords match
    if (formData.password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    try {
      const result = await signup(formData);

      // If signup is successful and we have the user data
      if (
        result.success &&
        result.data &&
        result.data.userId &&
        result.data.email
      ) {
        // Show success message
        toast.success(
          "Signup successful! Please check your email for the verification code"
        );

        // Redirect to OTP verification page with user data
        navigate("/verify-otp", {
          state: {
            userId: result.data.userId,
            email: result.data.email,
          },
        });
      }
    } catch (err) {
      // Error is handled by useAuth hook and displayed by the component
      console.error("Signup error:", err);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-400 px-4 py-8">
      <div className="card rounded-xl w-full max-w-2xl p-8">
        <div className="flex items-center justify-center mb-8">
          <Box className="w-10 h-10 text-brand-300" strokeWidth={1.5} />
          <span className="ml-2 text-2xl font-light tracking-wider bg-gradient-to-r from-brand-300 to-brand-100 text-transparent bg-clip-text">
            morpho
          </span>
        </div>

        <h1 className="text-2xl font-light text-center mb-6">
          Create an Account
        </h1>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Personal Information */}
            <div>
              <label
                htmlFor="first_name"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                First Name *
              </label>
              <input
                id="first_name"
                name="first_name"
                type="text"
                value={formData.first_name}
                onChange={handleChange}
                className="input"
                placeholder="Enter your first name"
                required
              />
            </div>

            <div>
              <label
                htmlFor="last_name"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Last Name *
              </label>
              <input
                id="last_name"
                name="last_name"
                type="text"
                value={formData.last_name}
                onChange={handleChange}
                className="input"
                placeholder="Enter your last name"
                required
              />
            </div>

            <div>
              <label
                htmlFor="username"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Username *
              </label>
              <input
                id="username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleChange}
                className="input"
                placeholder="Choose a username"
                required
              />
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Email *
              </label>
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                className="input"
                placeholder="Enter your email"
                required
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Password *
              </label>
              <input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                className="input"
                placeholder="Create a password"
                required
                minLength={8}
              />
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Confirm Password *
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="input"
                placeholder="Confirm your password"
                required
              />
            </div>

            {/* Optional Information */}
            <div>
              <label
                htmlFor="phoneNumber"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Phone Number
              </label>
              <input
                id="phoneNumber"
                name="phoneNumber"
                type="tel"
                value={formData.phoneNumber}
                onChange={handleChange}
                className="input"
                placeholder="Enter your phone number"
              />
            </div>

            <div>
              <label
                htmlFor="country"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Country
              </label>
              <input
                id="country"
                name="country"
                type="text"
                value={formData.country}
                onChange={handleChange}
                className="input"
                placeholder="Enter your country"
              />
            </div>

            <div>
              <label
                htmlFor="companyName"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Company Name
              </label>
              <input
                id="companyName"
                name="companyName"
                type="text"
                value={formData.companyName}
                onChange={handleChange}
                className="input"
                placeholder="Enter your company name"
              />
            </div>

            <div>
              <label
                htmlFor="industry"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Industry
              </label>
              <input
                id="industry"
                name="industry"
                type="text"
                value={formData.industry}
                onChange={handleChange}
                className="input"
                placeholder="Enter your industry"
              />
            </div>

            <div className="md:col-span-2">
              <label
                htmlFor="companyWebsite"
                className="block text-sm font-light text-gray-400 mb-2"
              >
                Company Website
              </label>
              <input
                id="companyWebsite"
                name="companyWebsite"
                type="url"
                value={formData.companyWebsite}
                onChange={handleChange}
                className="input"
                placeholder="https://example.com"
              />
            </div>
          </div>

          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <Link
              to="/login"
              className="text-brand-300 hover:text-brand-200 text-sm transition-colors"
            >
              Already have an account? Sign in
            </Link>

            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary w-full md:w-auto flex items-center justify-center"
            >
              {loading ? (
                <span className="inline-block w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
              ) : (
                "Create Account"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Signup;
