import React, { useState, useEffect } from "react";
import {
  X,
  Store,
  Search,
  AlertCircle,
  CheckCircle,
  Loader2,
} from "lucide-react";
import { useModels } from "../../hooks/useModels";
import { ecommerceService } from "../../services/ecommerce";
import { storage } from "../../utils/storage";
// Extend the Model3D type to include integrationKey
import { Model3D as BaseModel3D } from "../../types/models";
import type { ProductImportResult } from "../../types/ecommerce";

interface ExtendedModel3D extends BaseModel3D {
  integrationKey?: string;
}

// Define types for sync results
interface SyncResultItem {
  morphoId: string;
  shopifyId: string;
  name: string;
  action: "created" | "updated";
}

interface SyncFailedItem {
  morphoId: string;
  name: string;
  error: string;
}

interface SyncResults {
  success: SyncResultItem[];
  failed: SyncFailedItem[];
}

interface SyncResponse {
  success: boolean;
  message: string;
  data: SyncResults;
  error?: string;
}

interface ShopifySyncModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedModelIds: string[];
  onSuccess?: (result: ProductImportResult) => void;
}

const ShopifySyncModal: React.FC<ShopifySyncModalProps> = ({
  isOpen,
  onClose,
  selectedModelIds,
  onSuccess,
}) => {
  const { models, loading: loadingModels } = useModels();
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [shopName, setShopName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [syncResults, setSyncResults] = useState<SyncResults | null>(null);

  // Filter models based on search term
  const filteredModels = models.filter(
    (model) =>
      model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (model.sku && model.sku.toLowerCase().includes(searchTerm.toLowerCase()))
  ) as ExtendedModel3D[];

  // Load saved Shopify credentials if available
  useEffect(() => {
    const savedCredentials = storage.getShopifyCredentials();
    if (savedCredentials) {
      setShopName(savedCredentials.shopName || "");
      setApiKey(savedCredentials.apiKey || "");
      setPassword(savedCredentials.password || "");
    }
  }, []);

  const handleSelectAll = () => {
    if (selectedProducts.length === filteredModels.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredModels.map((model) => model._id));
    }
  };

  const handleProductSelect = (productId: string) => {
    if (selectedProducts.includes(productId)) {
      setSelectedProducts(selectedProducts.filter((id) => id !== productId));
    } else {
      setSelectedProducts([...selectedProducts, productId]);
    }
  };

  const handleSync = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (!shopName || !apiKey || !password) {
        setError("Please fill in all Shopify credentials");
        return;
      }

      const result = await ecommerceService.syncMorphoToShopify(
        { shopName, apiKey, password },
        selectedModelIds
      );

      if (result.success) {
        setSuccess(
          `Successfully synced ${result.data.success.length} products to Shopify`
        );
        setSyncResults(result.data);
        if (onSuccess) {
          onSuccess(result);
        }
        onClose();
      } else {
        setError(result.error || "Failed to sync products");
      }
    } catch (err: unknown) {
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div className="w-full max-w-4xl max-h-[90vh] overflow-hidden bg-dark-300 rounded-xl shadow-xl">
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <div className="flex items-center gap-3">
            <Store size={20} className="text-brand-300" />
            <h2 className="text-xl font-light text-gray-100">
              Sync Products to Shopify
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-dark-200 text-gray-400 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {error && (
            <div className="mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/20 flex items-start gap-3">
              <AlertCircle size={20} className="text-red-400 shrink-0 mt-0.5" />
              <p className="text-red-400">{error}</p>
            </div>
          )}

          {success && (
            <div className="mb-6 p-4 rounded-lg bg-green-500/10 border border-green-500/20 flex items-start gap-3">
              <CheckCircle
                size={20}
                className="text-green-400 shrink-0 mt-0.5"
              />
              <p className="text-green-400">{success}</p>
            </div>
          )}

          <form onSubmit={handleSync}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Shopify Credentials */}
              <div className="space-y-6">
                <h3 className="text-lg font-light text-gray-200">
                  Shopify Credentials
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-light text-gray-400 mb-2">
                      Shop Name
                    </label>
                    <input
                      type="text"
                      value={shopName}
                      onChange={(e) => setShopName(e.target.value)}
                      placeholder="Your shop name (without .myshopify.com)"
                      className="input w-full"
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Enter your Shopify store name (the part before
                      .myshopify.com)
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-light text-gray-400 mb-2">
                      API Key
                    </label>
                    <input
                      type="password"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="API Key from your private app"
                      className="input w-full"
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Generate a private app in Shopify Admin to get the API key
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-light text-gray-400 mb-2">
                      Password / Access Token
                    </label>
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Admin API access token"
                      className="input w-full"
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      The password or access token for your private app
                    </p>
                  </div>
                </div>
              </div>

              {/* Product Selection */}
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-light text-gray-200">
                    Select Products
                  </h3>
                  <button
                    type="button"
                    onClick={handleSelectAll}
                    className="text-sm text-brand-300 hover:text-brand-200"
                  >
                    {selectedProducts.length === filteredModels.length &&
                    filteredModels.length > 0
                      ? "Deselect All"
                      : "Select All"}
                  </button>
                </div>

                <div className="relative">
                  <Search
                    size={18}
                    strokeWidth={1.5}
                    className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                  />
                  <input
                    type="text"
                    placeholder="Search products by name or SKU..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input pl-10 w-full"
                  />
                </div>

                <div className="mt-2 space-y-2 max-h-[300px] overflow-y-auto border border-gray-800 rounded-lg p-2">
                  {loadingModels ? (
                    <div className="text-center py-4">
                      <Loader2
                        size={24}
                        className="animate-spin mx-auto text-brand-300"
                      />
                      <p className="text-gray-400 mt-2">Loading products...</p>
                    </div>
                  ) : filteredModels.length === 0 ? (
                    <div className="text-center py-4">
                      <p className="text-gray-400">No products found</p>
                    </div>
                  ) : (
                    filteredModels.map((model) => (
                      <label
                        key={model._id}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-dark-200/50 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={selectedProducts.includes(model._id)}
                          onChange={() => handleProductSelect(model._id)}
                          className="form-checkbox"
                        />
                        <div className="flex-1">
                          <p className="text-gray-300">{model.name}</p>
                          <p className="text-xs text-gray-500">
                            SKU: {model.sku || "N/A"} • $
                            {model.price.toFixed(2)}
                          </p>
                        </div>
                        {model.integrationKey && (
                          <span className="px-2 py-1 text-xs rounded-full bg-brand-500/10 text-brand-300">
                            On Shopify
                          </span>
                        )}
                      </label>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Results section - shown after successful sync */}
            {syncResults && (
              <div className="mt-8 border-t border-gray-800 pt-6">
                <h3 className="text-lg font-light text-gray-200 mb-4">
                  Sync Results
                </h3>

                {syncResults.success.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-green-400 mb-2">
                      Successfully Synced ({syncResults.success.length})
                    </h4>
                    <div className="max-h-[200px] overflow-y-auto border border-gray-800 rounded-lg p-2">
                      {syncResults.success.map((item: SyncResultItem) => (
                        <div
                          key={item.morphoId}
                          className="p-2 border-b border-gray-800 last:border-0"
                        >
                          <p className="text-gray-300">{item.name}</p>
                          <p className="text-xs text-gray-500">
                            {item.action === "created"
                              ? "Created new product"
                              : "Updated existing product"}{" "}
                            on Shopify
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {syncResults.failed.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-red-400 mb-2">
                      Failed to Sync ({syncResults.failed.length})
                    </h4>
                    <div className="max-h-[200px] overflow-y-auto border border-gray-800 rounded-lg p-2">
                      {syncResults.failed.map((item: SyncFailedItem) => (
                        <div
                          key={item.morphoId}
                          className="p-2 border-b border-gray-800 last:border-0"
                        >
                          <p className="text-gray-300">{item.name}</p>
                          <p className="text-xs text-red-400">{item.error}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="mt-8 flex justify-end gap-3">
              <button
                type="button"
                onClick={onClose}
                className="btn btn-secondary"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting || selectedProducts.length === 0}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 size={16} className="animate-spin mr-2" />
                    Syncing...
                  </>
                ) : (
                  `Sync ${selectedProducts.length} Products to Shopify`
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ShopifySyncModal;
