import React, { useRef } from 'react';
import { But<PERSON> } from '../ui/Button';
import { GlassContainer } from '../ui/GlassContainer';
import { useInView } from '../../hooks/useInView';
import { <PERSON><PERSON>ircle2, <PERSON><PERSON><PERSON>, <PERSON>ap, <PERSON><PERSON>hart3, Heart } from 'lucide-react';

export const CTA: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { threshold: 0.1 });
  
  return (
    <section ref={sectionRef} id="cta" className="py-24 relative">
      {/* Enhanced background glow effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-1/2 h-1/2 bg-blue-500/15 rounded-full filter blur-[120px] opacity-70 animate-pulse-slow"></div>
        <div className="absolute bottom-0 right-1/4 w-1/2 h-1/2 bg-purple-500/15 rounded-full filter blur-[120px] opacity-70 animate-pulse-slow" style={{animationDelay: "1.5s"}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1/3 h-1/3 bg-teal-400/15 rounded-full filter blur-[100px] opacity-70 animate-pulse-slow" style={{animationDelay: "0.8s"}}></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <GlassContainer className="max-w-4xl mx-auto border border-blue-400/30 rounded-lg shadow-glow-md animate-glow relative overflow-hidden">
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-400/10 to-transparent rounded-bl-full pointer-events-none"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-purple-400/10 to-transparent rounded-tr-full pointer-events-none"></div>
          
          <div className="absolute top-10 left-10 w-20 h-20 border border-blue-400/20 rounded-full animate-spin-slow opacity-20 pointer-events-none"></div>
          <div className="absolute bottom-10 right-10 w-16 h-16 border border-blue-400/20 rounded-full animate-spin-slow opacity-20" style={{animationDuration: "15s"}}></div>
          
          {/* Floating particles */}
          <div className="absolute top-1/4 right-1/4 w-2 h-2 bg-blue-400/50 rounded-full animate-pulse-slow"></div>
          <div className="absolute bottom-1/3 left-1/4 w-2 h-2 bg-purple-400/50 rounded-full animate-pulse-slow" style={{animationDelay: "1s"}}></div>
          <div className="absolute top-2/3 right-1/3 w-1.5 h-1.5 bg-teal-400/50 rounded-full animate-pulse-slow" style={{animationDelay: "2s"}}></div>
          
          <div className="py-16 px-4 sm:px-12 text-center relative z-10">
            <h2 className={`text-3xl md:text-4xl font-bold mb-6 transition-all duration-700 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <span className="relative inline-block">
                Ready to{" "}
                <span className="relative">
                  <span className="relative z-10 bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">Transform</span>
                  <span className="absolute -bottom-1 left-0 w-full h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 rounded-full opacity-70"></span>
                </span>
                {" "}Your Shopify Store with{" "}
              </span>
              <span className="relative inline-block bg-gradient-to-r from-blue-300 to-blue-500 bg-clip-text text-transparent">3D Product Visualization?</span>
            </h2>
            
            <p className={`text-xl text-gray-300 mb-8 transition-all duration-700 delay-150 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <span className="relative inline-block">
                Join <span className="font-semibold text-white">hundreds</span> of forward-thinking Shopify merchants already using our
                <span className="relative mx-2">
                  <span className="relative z-10 text-blue-300 font-semibold">3D product viewer</span>
                  <svg className="absolute -bottom-1 left-0 w-full" height="4" viewBox="0 0 100 4" fill="none">
                    <path d="M0 2C25 0 75 0 100 2" stroke="#0066CC" strokeWidth="2" strokeLinecap="round"/>
                  </svg>
                </span>.
              </span>
            </p>
            
            <ul className={`text-left max-w-xl mx-auto mb-12 space-y-6 transition-all duration-700 delay-300 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <li className="flex items-start transform transition-all duration-300 hover:translate-x-1.5 group">
                <div className="mt-0.5 mr-3 flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-400/10 group-hover:bg-blue-400/20 transition-colors duration-300 border border-blue-400/30">
                  <Zap size={16} className="text-blue-400" />
                </div>
                <div>
                  <span className="text-white group-hover:text-blue-300 transition-colors duration-300">
                    Increase conversion rates by an average of{" "}
                    <span className="font-bold text-blue-400 group-hover:text-blue-300 transition-colors duration-300 relative">
                      37%
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-400/30 group-hover:bg-blue-300/50 transition-colors duration-300"></span>
                    </span>{" "}
                    with 3D products
                  </span>
                </div>
              </li>
              
              <li className="flex items-start transform transition-all duration-300 hover:translate-x-1.5 group">
                <div className="mt-0.5 mr-3 flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-purple-400/10 group-hover:bg-purple-400/20 transition-colors duration-300 border border-purple-400/30">
                  <CheckCircle2 size={16} className="text-purple-400" />
                </div>
                <div>
                  <span className="text-white group-hover:text-purple-300 transition-colors duration-300">
                    Reduce product returns by up to{" "}
                    <span className="font-bold text-purple-400 group-hover:text-purple-300 transition-colors duration-300 relative">
                      27%
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-purple-400/30 group-hover:bg-purple-300/50 transition-colors duration-300"></span>
                    </span>{" "}
                    through better 3D visualization
                  </span>
                </div>
              </li>
              
              <li className="flex items-start transform transition-all duration-300 hover:translate-x-1.5 group">
                <div className="mt-0.5 mr-3 flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-teal-400/10 group-hover:bg-teal-400/20 transition-colors duration-300 border border-teal-400/30">
                  <BarChart3 size={16} className="text-teal-400" />
                </div>
                <div>
                  <span className="text-white group-hover:text-teal-300 transition-colors duration-300">
                    Boost average time on product pages by{" "}
                    <span className="font-bold text-teal-400 group-hover:text-teal-300 transition-colors duration-300 relative">
                      4x
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-teal-400/30 group-hover:bg-teal-300/50 transition-colors duration-300"></span>
                    </span>{" "}
                    with interactive 3D models
                  </span>
                </div>
              </li>
              
              <li className="flex items-start transform transition-all duration-300 hover:translate-x-1.5 group">
                <div className="mt-0.5 mr-3 flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-orange-400/10 group-hover:bg-orange-400/20 transition-colors duration-300 border border-orange-400/30">
                  <Heart size={16} className="text-orange-400" />
                </div>
                <div>
                  <span className="text-white group-hover:text-orange-300 transition-colors duration-300">
                    Improve customer satisfaction scores with{" "}
                    <span className="font-bold text-orange-400 group-hover:text-orange-300 transition-colors duration-300 relative">
                      immersive
                      <span className="absolute bottom-0 left-0 w-full h-0.5 bg-orange-400/30 group-hover:bg-orange-300/50 transition-colors duration-300"></span>
                    </span>{" "}
                    3D shopping experiences
                  </span>
                </div>
              </li>
            </ul>
            
            <div className={`transition-all duration-700 delay-500 ${
              isInView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              <p className="text-white mb-6 bg-gradient-to-r from-gray-300 via-white to-gray-300 bg-clip-text text-transparent font-medium">
                Start your risk-free 14-day trial of our Shopify 3D product viewer today.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
               <Button 
                  intent="primary" 
                  size="large"
                  className="group relative overflow-hidden"
                >
                  <span className="absolute flex -top-10 -left-10 w-20 h-20 bg-white/10 rotate-45 transform translate-x-1 translate-y-1 group-hover:translate-x-[400%] group-hover:translate-y-[400%] transition-all duration-1000 ease-out"></span>

                  <span className="relative z-10 flex items-center">
                    <Sparkles className="w-4 h-4 mr-2 text-yellow-300 animate-pulse" />
                    Start Your Free Trial
                  </span>
                </Button>

                
                <Button 
                  intent="secondary" 
                  size="large"
                  className="group"
                >
                  <span className="group-hover:text-blue-300 transition-colors duration-300">Book a Demo</span>
                </Button>
              </div>
              
              <p className="text-gray-400 mt-6 text-sm flex items-center justify-center group transition-all duration-300 hover:text-gray-300">
                <span className="relative overflow-hidden">
                  No credit card required
                  <span className="absolute bottom-0 left-0 w-full h-[1px] bg-blue-400/40 group-hover:bg-blue-400/60 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                </span>
                <span className="mx-2">•</span>
                <span className="relative overflow-hidden">
                  Installs in seconds from the Shopify App Store
                  <span className="absolute bottom-0 left-0 w-full h-[1px] bg-blue-400/40 group-hover:bg-blue-400/60 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                </span>
              </p>
            </div>
          </div>
        </GlassContainer>
      </div>
    </section>
  );
};

export default { CTA };