const expressError = require('../errors/expressError');
const User = require('../models/User');
const Project = require('../models/Project');
const GlbModel = require('../models/GlbModel');
const AssetLibrary = require('../models/AssetLibrary');

const handleProductUpdate = async (data) => {
  try {
    if (!data || !data.id) {
      throw new Error('Invalid product data received');
    }
    // Implement product update logic here
    console.log('Product updated:', data.id);
  } catch (error) {
    console.error('Error handling product update:', error);
    throw error;
  }
};

const handleProductDelete = async (data) => {
  try {
    if (!data || !data.id) {
      throw new Error('Invalid product data received');
    }
    // Implement product deletion logic here
    console.log('Product deleted:', data.id);
  } catch (error) {
    console.error('Error handling product deletion:', error);
    throw error;
  }
};

const handleOrderCreated = async (data) => {
  try {
    if (!data || !data.id) {
      throw new Error('Invalid order data received');
    }
    // Implement order creation logic here
    console.log('New order created:', data.id);
  } catch (error) {
    console.error('Error handling order creation:', error);
    throw error;
  }
};

const handleShopUninstall = async (data) => {
  try {
    if (!data || !data.shop_domain) {
      throw new Error('Invalid shop data received');
    }
    // Implement app uninstall logic here
    console.log('App uninstalled from shop:', data.shop_domain);
  } catch (error) {
    console.error('Error handling shop uninstall:', error);
    throw error;
  }
};

exports.handleWebhook = async (req, res, next) => {
  try {
    const topic = req.headers['x-shopify-topic'];
    const data = req.body;

    if (!topic) {
      console.error('Missing webhook topic');
      return res.status(200).json({ success: true }); // Still return 200 as per Shopify requirements
    }

    console.log(`Processing webhook: ${topic}`, {
      shop: req.shopifyDomain,
      topic,
      dataId: data?.id
    });

    switch (topic) {
      // --- Shopify Mandatory Compliance Webhooks ---
      case 'customers/data_request': {
        // Shopify sends an array of customer objects with emails
        // Example: { customers: [{ id, email, ... }] }
        const emails = (data.customers || []).map(c => c.email).filter(Boolean);
        if (emails.length === 0) {
          console.log('No customer emails provided for data request.');
          break;
        }
        // Find users by email
        const users = await User.find({ email: { $in: emails } });
        // Log or process the user data as required (for now, just log)
        console.log('Customer data for data_request:', users);
        // In a real implementation, you may need to send this data to the store owner
        break;
      }
      case 'customers/redact': {
        // Shopify sends an array of customer objects with emails
        // Example: { customers: [{ id, email, ... }] }
        const emails = (data.customers || []).map(c => c.email).filter(Boolean);
        if (emails.length === 0) {
          console.log('No customer emails provided for redaction.');
          break;
        }
        // Redact (delete) users by email
        const result = await User.deleteMany({ email: { $in: emails } });
        console.log(`Redacted (deleted) ${result.deletedCount} users for emails:`, emails);
        // You may also want to delete related data (projects, models, etc.)
        break;
      }
      case 'shop/redact': {
        // Shopify sends shop_domain in the payload
        const shopDomain = data.shop_domain;
        if (!shopDomain) {
          console.log('No shop_domain provided for shop redaction.');
          break;
        }
        // If you store shop_domain in your User or Project model, use it here.
        // For this example, assume users have a companyWebsite or similar field matching the shop domain.
        // Find all users for this shop
        const users = await User.find({ companyWebsite: { $regex: shopDomain, $options: 'i' } });
        const userIds = users.map(u => u._id);
        // Delete users
        const userResult = await User.deleteMany({ _id: { $in: userIds } });
        // Delete projects for these users
        const projectResult = await Project.deleteMany({ client: { $in: userIds } });
        // Delete 3D models for these projects
        const projects = await Project.find({ client: { $in: userIds } });
        const projectIds = projects.map(p => p._id);
        const glbResult = await GlbModel.deleteMany({ project: { $in: projectIds } });
        // Delete asset libraries for these projects
        const assetResult = await AssetLibrary.deleteMany({ project: { $in: projectIds } });
        console.log(`Redacted shop data for domain ${shopDomain}: users=${userResult.deletedCount}, projects=${projectResult.deletedCount}, models=${glbResult.deletedCount}, assets=${assetResult.deletedCount}`);
        break;
      }
      // --- Other Shopify webhooks ---
      case 'products/update':
        await handleProductUpdate(data);
        break;
      case 'products/delete':
        await handleProductDelete(data);
        break;
      case 'orders/create':
        await handleOrderCreated(data);
        break;
      case 'app/uninstalled':
        await handleShopUninstall(data);
        break;
      default:
        console.log(`Unhandled webhook topic: ${topic}`);
    }

    // Always return 200 to acknowledge receipt
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Webhook handling error:', {
      topic: req.headers['x-shopify-topic'],
      shop: req.shopifyDomain,
      error: error.message,
      stack: error.stack
    });
    // Still return 200 to prevent Shopify from retrying
    res.status(200).json({ success: true });
  }
}; 