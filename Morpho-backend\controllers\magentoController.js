const assetLibraryModel = require("../models/AssetLibrary");
const GlbModel = require("../models/GlbModel");
const projectSchema = require("../models/Project");
const expressError = require("../errors/expressError");
const resHandle = require("../utils/responseHandle");

exports.getAllProducts = async (req, res, next) => {
  // Validate magento client
  if (!req.magentoClient) {
    throw new expressError(
      "Magento client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Parse limit parameter
  const limit = parseInt(req.query.limit, 10) || 5;

  // Fetch products from Magento
  const response = await req.magentoClient.get("/rest/V1/products", {
    params: {
      searchCriteria: {
        pageSize: limit,
      },
    },
  });

  const products = response.data.items;

  // Check if products exist
  if (!products || products.length === 0) {
    throw new expressError(
      "No products found in Magento",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Return success response
  return resHandle.handleData(
    res,
    200,
    "Products retrieved successfully",
    true,
    products
  );
};

exports.getStatistics = async (req, res, next) => {
  // Validate magento client
  if (!req.magentoClient) {
    throw new expressError(
      "Magento client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Fetch all orders from Magento
  const response = await req.magentoClient.get("/rest/V1/orders", {
    params: {
      searchCriteria: {
        pageSize: 100, // Adjust as needed
      },
    },
  });

  const orders = response.data.items;

  // Check if orders exist
  if (!orders || orders.length === 0) {
    throw new expressError(
      "No orders found in Magento",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Calculate total orders
  const totalOrders = orders.length;

  // Prepare a list to track product sales and revenue
  const productStats = {};

  orders.forEach((order) => {
    order.items.forEach((item) => {
      const productId = item.product_id;
      const quantity = item.qty_ordered;
      const price = item.price * quantity;

      // If product already exists in stats, update it
      if (productStats[productId]) {
        productStats[productId].totalSold += quantity;
        productStats[productId].revenue += price;
      } else {
        productStats[productId] = {
          productId,
          name: item.name,
          totalSold: quantity,
          revenue: price,
        };
      }
    });
  });

  // Sort products by totalSold and revenue (descending)
  const topProducts = Object.values(productStats)
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5); // Limit to top 5 products

  // Return success response
  return resHandle.handleData(
    res,
    200,
    "Statistics retrieved successfully",
    true,
    {
      totalOrders,
      topProducts,
    }
  );
};
exports.saveAllProducts = async (req, res, next) => {
  const userId = req.user;

  // Validate user authentication
  if (!userId) {
    throw new expressError(
      "User authentication is required",
      401,
      expressError.CODES.AUTHENTICATION_REQUIRED
    );
  }

  // Validate magento client
  if (!req.magentoClient) {
    throw new expressError(
      "Magento client not initialized",
      500,
      expressError.CODES.SERVER_ERROR
    );
  }

  // Get the user's project
  const project = await projectSchema.findOne({ client: userId }).lean();
  if (!project) {
    throw new expressError(
      "Project not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Get the asset library
  const assetLibrary = await assetLibraryModel.findOne({
    project: project._id,
  });

  if (!assetLibrary) {
    throw new expressError(
      "Asset library not found",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  const assetLibraryId = assetLibrary._id;
  const projectId = project._id;

  // Define the Magento media base URL for the demo store
  const magentoMediaBaseUrl = req.body.magentoMediaBaseUrl;

  // Validate media base URL
  if (!magentoMediaBaseUrl) {
    throw new expressError(
      "Magento media base URL is required",
      400,
      expressError.CODES.MISSING_REQUIRED_FIELD
    );
  }

  // Fetch all products from Magento API
  const response = await req.magentoClient.get("/rest/V1/products", {
    params: {
      searchCriteria: { pageSize: 10, currentPage: 1 },
    },
  });

  const products = response.data.items;
  if (!products || products.length === 0) {
    throw new expressError(
      "No products found on Magento",
      404,
      expressError.CODES.RESOURCE_NOT_FOUND
    );
  }

  // Arrays to track new and skipped products
  const skippedProducts = [];
  const productPromises = products.map(async (product) => {
    // Check if the product already exists in the database
    const exists = await GlbModel.findOne({
      integrationKey: String(product.id),
    });
    if (exists) {
      skippedProducts.push({
        name: product.name,
        id: product.id,
        reason: "Product already exists.",
      });
      return null;
    }

    // Get the image URL and handle invalid URLs
    const imageUrl = product.media_gallery_entries?.[0]?.file
      ? `${magentoMediaBaseUrl}${product.media_gallery_entries[0].file}`
      : null;
    if (!imageUrl) {
      skippedProducts.push({
        name: product.name,
        id: product.id,
        reason: "Missing image URL.",
      });
      return null;
    }

    // Provide a fallback price if missing
    const price = product.price || 0;

    // Create product object
    return {
      name: product.name,
      description: product.description || "No description available",
      imageUrl, // Resolved image URL
      price: parseFloat(price),
      sku: product.sku || "",
      published: product.status === "1", // Magento status: 1 for enabled
      integrationKey: String(product.id),
      project: projectId,
      asset_library: assetLibraryId,
    };
  });

  // Resolve promises and filter out null values (skipped products)
  const formattedProducts = await Promise.all(productPromises);
  const newProducts = formattedProducts.filter(Boolean);

  // Insert new products into the database
  if (newProducts.length > 0) {
    await GlbModel.insertMany(newProducts);
  }

  // Return success response
  return resHandle.handleData(
    res,
    201,
    `${newProducts.length} new product(s) added`,
    true,
    {
      newProducts,
      skippedProducts,
    }
  );
};
