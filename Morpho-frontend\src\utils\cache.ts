interface CacheItem<T> {
  data: T;
  timestamp: number;
}

interface CacheConfig {
  ttl: number; // Time to live in milliseconds
}

class Cache {
  private storage: Map<string, CacheItem<any>>;
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.storage = new Map();
    this.config = config;
  }

  set<T>(key: string, data: T): void {
    this.storage.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  get<T>(key: string): T | null {
    const item = this.storage.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > this.config.ttl) {
      this.storage.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.storage.clear();
  }
}

// Create cache instance with 5 minute TTL
export const apiCache = new Cache({ ttl: 5 * 60 * 1000 });