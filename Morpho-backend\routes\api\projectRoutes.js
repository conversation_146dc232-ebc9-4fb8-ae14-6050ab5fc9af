const router = require("express").Router();
const controller = require("../../controllers/projectController");
const verifyRole = require("../../middleware/verifyRole");
const { designer, admin, client } = require("../../constants");
const { validateProjectSchema } = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

router.post("/get-project-by-id", asyncError<PERSON><PERSON><PERSON>(controller.getProjectById));
router.post(
  "/get-projects-by-client",
  async<PERSON>rro<PERSON><PERSON><PERSON><PERSON>(controller.getProjectsByClientId)
);
router.post(
  "/get-projects-by-artist",
  asyncError<PERSON>andler(controller.getProjectsByArtistId)
);
router.get(
  "/get-all-projects",
  verifyRoles([admin]),
  asyncError<PERSON><PERSON><PERSON>(controller.getAllProjects)
);
router.post(
  "/get-project-with-artists",
  async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.getProjectWithArtists)
);
router.post(
  "/get-project-with-template",
  asyncErrorHandler(controller.getProjectWithTemplate)
);
router.post(
  "/get-project-with-assets",
  asyncErrorHandler(controller.getProjectWithAsset)
);
router.post(
  "/get-project-with-information",
  asyncErrorHandler(controller.getProjectWithAllInformations)
);
router.route("/create-project").post(
  // validateProjectSchema,
  // verifyRole([admin, client]),
  asyncErrorHandler(controller.createProject)
);
router
  .route("/update-project")
  .put(
    validateProjectSchema,
    verifyRoles([admin, designer]),
    asyncErrorHandler(controller.updateProject)
  );
router.delete(
  "/delete-project",
  verifyRoles([admin, designer]),
  asyncErrorHandler(controller.deleteProject)
);

module.exports = router;
