const axios = require('axios');

const setMagentoClient = (req, res, next) => {
  const { baseUrl, accessToken } = req.body;

  if (!baseUrl || !accessToken) {
    return res.status(400).json({ success: false, message: 'Missing Magento credentials' });
  }

  // Attach Magento client to the request object
  req.magentoClient = axios.create({
    baseURL: baseUrl,
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
  });

  next();
};

module.exports = setMagentoClient;
