import React from 'react';
import { LucideIcon } from 'lucide-react';

interface TemplateCardProps {
  name: string;
  description: string;
  icon: LucideIcon;
  isSelected: boolean;
  onClick: () => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  name,
  description,
  icon: Icon,
  isSelected,
  onClick
}) => {
  return (
    <button
      onClick={onClick}
      className={`w-full p-4 rounded-xl border transition-all duration-300 text-left ${
        isSelected
          ? 'bg-brand-500/10 border-brand-500/30 shadow-glow'
          : 'bg-dark-200/50 border-gray-800/50 hover:border-brand-500/20'
      }`}
    >
      <div className="flex items-start gap-3">
        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
          isSelected ? 'bg-brand-500/20' : 'bg-dark-300/50'
        }`}>
          <Icon
            size={20}
            strokeWidth={1.5}
            className={isSelected ? 'text-brand-300' : 'text-gray-400'}
          />
        </div>
        <div>
          <h3 className={`text-sm font-light ${
            isSelected ? 'text-brand-300' : 'text-gray-300'
          }`}>
            {name}
          </h3>
          <p className="text-xs text-gray-500 mt-1">{description}</p>
        </div>
      </div>
    </button>
  );
};

export default TemplateCard;