const mongoose = require("mongoose");

const assetLibrarySchema = new mongoose.Schema(
  {
    serial_number: {
      type: String,
      required: true,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: "Project",
    },
  },
  { timestamps: true, collection: "assetlibraries" }
);

const assetLibraryModel = mongoose.model("AssetLibrary", assetLibrarySchema);
module.exports = assetLibraryModel;
