import { useState, useEffect } from "react";
import {
  subscriptionService,
  SubscriptionStatus,
} from "../services/subscription";
import { useAuth } from "./useAuth";

/**
 * Custom hook to manage subscription data
 * @returns Subscription status and loading state
 */
export const useSubscription = () => {
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { token } = useAuth();

  // Function to fetch subscription data
  const fetchSubscription = async () => {
    if (!token) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await subscriptionService.getUserSubscription();

      if (response.success) {
        setSubscription(response.data);
      } else {
        setError(response.message || "Failed to fetch subscription data");
      }
    } catch (err) {
      console.error("Error fetching subscription:", err);
      setError("An error occurred while fetching subscription data");
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch when component mounts or token changes
  useEffect(() => {
    if (!token) return;

    // Initial fetch
    fetchSubscription();
  }, [token]);

  // Set up polling for subscription updates
  useEffect(() => {
    if (!token) return;

    // Set up polling interval
    const intervalId = setInterval(() => {
      fetchSubscription();
    }, 30000); // Poll every 30 seconds

    // Clean up interval on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [token]);

  return {
    subscription,
    loading,
    error,
    refreshSubscription: fetchSubscription,
  };
};
