import React, { useState } from "react";
import { Box } from "lucide-react";
import { useAuth } from "../hooks/useAuth";
import { Navigate, Link } from "react-router-dom";

const Login = () => {
  const { login, loading, error, isAuthenticated } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login({ email, password });
    } catch (err) {
      // Error is handled by useAuth hook
      console.log(err);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-400 px-4">
      <div className="card rounded-xl w-full max-w-md p-8">
        <div className="flex items-center justify-center mb-8">
          <Box className="w-10 h-10 text-brand-300" strokeWidth={1.5} />
          <span className="ml-2 text-2xl font-light tracking-wider bg-gradient-to-r from-brand-300 to-brand-100 text-transparent bg-clip-text">
            morpho
          </span>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm">
              {error}
            </div>
          )}

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-light text-gray-400 mb-2"
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="input"
              placeholder="Enter your email"
              required
            />
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-sm font-light text-gray-400 mb-2"
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="input"
              placeholder="Enter your password"
              required
            />
          </div>

          <div className="flex flex-col md:flex-row items-center justify-between gap-4 mt-6">
            <Link
              to="/signup"
              className="text-brand-300 hover:text-brand-200 text-sm transition-colors"
            >
              Don't have an account? Sign up
            </Link>

            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary w-full md:w-auto flex items-center justify-center"
            >
              {loading ? (
                <span className="inline-block w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
              ) : (
                "Sign In"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
