import React from "react";
import { X, Folder, ExternalLink, Package } from "lucide-react";
import { useCollections } from "../../hooks/useCollection";
import { Collection } from "../../hooks/useCollection";

interface ViewCollectionsModalProps {
  onClose: () => void;
  onSelectCollection: (collection: Collection) => void;
}

const ViewCollectionsModal: React.FC<ViewCollectionsModalProps> = ({
  onClose,
  onSelectCollection,
}) => {
  const { collections, loading, error } = useCollections();

  return (
    <div className="fixed inset-0 bg-dark-400/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-dark-300 rounded-xl w-full max-w-4xl max-h-[80vh] flex flex-col">
        <div className="flex items-center justify-between p-6 border-b border-gray-800">
          <h2 className="text-xl font-light text-gray-100">Your Collections</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-dark-200 text-gray-400 transition-colors"
          >
            <X size={20} strokeWidth={1.5} />
          </button>
        </div>

        <div className="p-6 flex-1 overflow-y-auto">
          {error && (
            <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 mb-6">
              {error}
            </div>
          )}

          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-300"></div>
            </div>
          ) : collections.length === 0 ? (
            <div className="text-center py-20">
              <Folder
                className="mx-auto h-16 w-16 text-gray-500 mb-4"
                strokeWidth={1.5}
              />
              <p className="text-gray-400 font-light">
                You don't have any collections yet.
              </p>
              <p className="text-gray-500 text-sm mt-2">
                Start organizing your models by adding them to new collections.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {collections.map((collection) => (
                <div
                  key={collection._id}
                  onClick={() => onSelectCollection(collection)}
                  className="bg-dark-200 rounded-xl overflow-hidden border border-gray-800 hover:border-brand-500/30 
                           transition-all duration-300 cursor-pointer hover:shadow-lg hover:shadow-brand-500/5 group"
                >
                  <div className="h-32 bg-dark-300 flex items-center justify-center relative">
                    <Folder
                      size={48}
                      className="text-gray-500 group-hover:text-brand-300 transition-colors"
                    />
                    <div
                      className="absolute bottom-3 right-3 bg-dark-400/80 backdrop-blur-sm rounded-full px-3 py-1 
                                  flex items-center gap-1 text-xs text-gray-400 border border-gray-700/50"
                    >
                      <Package size={12} strokeWidth={1.5} />
                      <span>
                        {Array.isArray(collection.products)
                          ? collection.products.length
                          : 0}
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="text-gray-200 font-light text-lg group-hover:text-brand-300 transition-colors">
                      {collection.name}
                    </h3>
                    {collection.description && (
                      <p className="text-gray-500 text-sm mt-1 line-clamp-2">
                        {collection.description}
                      </p>
                    )}
                    <div className="flex items-center justify-end mt-4 text-brand-400 group-hover:text-brand-300 transition-colors">
                      <span className="text-xs">View Models</span>
                      <ExternalLink
                        size={14}
                        strokeWidth={1.5}
                        className="ml-1"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewCollectionsModal;
