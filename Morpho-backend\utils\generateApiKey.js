const { createHmac } = require("crypto");

generateRandomString = (length) => {
  const characters =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let randomString = "";
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters.charAt(randomIndex);
  }
  return randomString;
};
function generateRandomHash(secret, length) {
  const randomString = this.generateRandomString(length);
  const dataToHash = secret + randomString;
  const hash = createHmac("sha256", secret).update(dataToHash).digest("hex");
  return hash;
}

module.exports = { generateRandomHash };
