const modelCustomizationSchema = require("../models/ModelCustomisation");
const resHandle = require("../utils/responseHandle");
class Controller {
  createModelCustomization = async (req, res) => {
    try {
      const {
        model,
        meshes_name,
        model_rotation,
        model_position,
        model_scaling,
      } = req.body;
      let newModelCustomization = new modelCustomizationSchema({
        model: model,
        meshes_name: meshes_name,
        model_rotation: model_rotation,
        model_position: model_position,
        model_scaling: model_scaling,
      });
      await newModelCustomization.save();
      resHandle.handleData(
        res,
        200,
        "Model Customization Created Successfully",
        true,
        newModelCustomization,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getAllModelCustomization = async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;
      const totalCount = await modelCustomizationSchema.countDocuments();
      if (totalCount == 0)
        return resHandle.handleError(res, 404, "No Model Customizations Found");

      let ModelCustomizationToBeRetrieved = await modelCustomizationSchema
        .find()
        .skip(skip)
        .limit(limit);
      //Check with the front end before changing it
      return res.status(200).json({
        message: "Model Customization retrieved Successfully",
        success: true,
        data: ModelCustomizationToBeRetrieved,
        total: totalCount,
        page: page,
        perPage: limit,
      });
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getModelCustomizationById = async (req, res) => {
    try {
      const { id } = req.body;
      let ModelCustomizationToBeRetrieved =
        await modelCustomizationSchema.findOne({ _id: id });
      if (!ModelCustomizationToBeRetrieved)
        return resHandle.handleError(res, 404, `Model Customization Not Found`);

      resHandle.handleData(
        res,
        200,
        "Model Customization Retrieved Successfully",
        true,
        ModelCustomizationToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  getModelCustomizationWithModel = async (req, res) => {
    try {
      const { id } = req.body;
      let ModelCustomizationToBeRetrieved = await modelCustomizationSchema
        .findOne({ _id: id })
        .populate("model");
      if (!ModelCustomizationToBeRetrieved)
        return resHandle.handleError(res, 404, `Model Customization Not Found`);

      resHandle.handleData(
        res,
        200,
        "Model Customization Retrieved Successfully",
        true,
        ModelCustomizationToBeRetrieved,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
  deleteModelCustomization = async (req, res) => {
    try {
      const { id } = req.body;
      let modelCustomizationToBeDeleted =
        await modelCustomizationSchema.findByIdAndDelete(id);
      if (!modelCustomizationToBeDeleted)
        return resHandle.handleError(res, 404, `Model Customization Not Found`);
      resHandle.handleData(
        res,
        200,
        "Model Customization Deleted Successfully",
        true,
      );
    } catch (err) {
      resHandle.handleError(res, 500, `Error ${err}`);
    }
  };
}

const controller = new Controller();
module.exports = controller;
