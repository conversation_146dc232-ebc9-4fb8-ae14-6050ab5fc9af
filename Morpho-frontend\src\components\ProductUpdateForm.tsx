import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import { useModels } from "../hooks/useModels";
import { Model3D } from "../types/models";
import {
  Loader2,
  Upload,
  Image as ImageIcon,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
// No need to import api as we're using fetch directly
const API_URL = "https://api.modularcx.link/morpho";
interface ProductUpdateFormProps {
  productId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ProductUpdateForm: React.FC<ProductUpdateFormProps> = ({
  productId,
  onSuccess,
  onCancel,
}) => {
  const { refresh: refreshModels } = useModels();

  const [product, setProduct] = useState<Model3D | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form fields
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState("");
  const [sku, setSku] = useState("");
  const [published, setPublished] = useState(false);

  // File uploads
  const [modelFile, setModelFile] = useState<File | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [modelPreview, setModelPreview] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem("accessToken");
        const response = await fetch(`${API_URL}/glbmodel/get-product-by-id`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ id: productId }),
        });

        const data = await response.json();

        if (data && data.data && data.data.length > 0) {
          const productData = data.data[0];
          setProduct(productData);

          // Set form fields
          setName(productData.name || "");
          setDescription(productData.description || "");
          setPrice(productData.price?.toString() || "");
          setSku(productData.sku || "");
          setPublished(productData.published || false);

          // Set previews for existing files
          if (productData.url) {
            setModelPreview(productData.url);
          }

          if (productData.imageUrl) {
            setImagePreview(productData.imageUrl);
          }
        } else {
          setError("Product not found");
        }
      } catch (err) {
        console.error("Error fetching product:", err);
        setError("Failed to load product data");
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId]);

  // Handle model file change
  const handleModelFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setModelFile(file);

      // Create a preview URL for the file (not actually used for 3D models)
      const fileUrl = URL.createObjectURL(file);
      setModelPreview(fileUrl);
    }
  };

  // Handle image file change
  const handleImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);

      // Create a preview URL for the image
      const fileUrl = URL.createObjectURL(file);
      setImagePreview(fileUrl);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSubmitting(true);
      setError(null);

      // Validate form
      if (!name) {
        setError("Name is required");
        setSubmitting(false);
        return;
      }

      if (!price || isNaN(parseFloat(price))) {
        setError("Valid price is required");
        setSubmitting(false);
        return;
      }

      // Create form data
      const formData = new FormData();
      formData.append("name", name);
      formData.append("description", description || "");
      formData.append("price", price);
      formData.append("sku", sku || "");
      formData.append("published", published.toString());

      // Add files if selected
      if (modelFile) {
        formData.append("url", modelFile);
      }

      if (imageFile) {
        formData.append("imageUrl", imageFile);
      }

      // Send update request
      const token = localStorage.getItem("accessToken");
      const response = await fetch(
        `${API_URL}/glbmodel/update-product/${productId}`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            // Don't set Content-Type for FormData
          },
          body: formData,
        }
      );

      const responseData = await response.json();

      if (response.ok && responseData.success) {
        toast.success("Product updated successfully");

        // Refresh models list
        refreshModels();

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error(responseData.message || "Failed to update product");
      }
    } catch (err) {
      console.error("Error updating product:", err);
      const errorMessage =
        err instanceof Error ? err.message : "An unexpected error occurred";
      setError(errorMessage);
      toast.error(errorMessage || "Failed to update product");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <Loader2 className="w-8 h-8 text-brand-500 animate-spin" />
        <p className="mt-4 text-gray-400">Loading product data...</p>
      </div>
    );
  }

  if (error && !product) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <AlertCircle className="w-8 h-8 text-red-500" />
        <p className="mt-4 text-red-400">{error}</p>
        <button className="mt-4 btn btn-secondary" onClick={onCancel}>
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 flex items-start gap-3">
            <AlertCircle size={20} className="text-red-400 shrink-0 mt-0.5" />
            <p className="text-red-400">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-200">
              Basic Information
            </h3>

            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-400 mb-1"
              >
                Name *
              </label>
              <input
                id="name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="input w-full"
                required
              />
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-400 mb-1"
              >
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="input w-full min-h-[100px]"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="price"
                  className="block text-sm font-medium text-gray-400 mb-1"
                >
                  Price *
                </label>
                <input
                  id="price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  className="input w-full"
                  required
                />
              </div>

              <div>
                <label
                  htmlFor="sku"
                  className="block text-sm font-medium text-gray-400 mb-1"
                >
                  SKU
                </label>
                <input
                  id="sku"
                  type="text"
                  value={sku}
                  onChange={(e) => setSku(e.target.value)}
                  className="input w-full"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                id="published"
                type="checkbox"
                checked={published}
                onChange={(e) => setPublished(e.target.checked)}
                className="form-checkbox"
              />
              <label
                htmlFor="published"
                className="ml-2 text-sm font-medium text-gray-400"
              >
                Published
              </label>
            </div>
          </div>

          {/* File Uploads */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-200">Files</h3>

            {/* 3D Model Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                3D Model
              </label>
              <div className="border-2 border-dashed border-gray-700 rounded-lg p-4">
                <div className="flex flex-col items-center justify-center space-y-2">
                  {modelPreview ? (
                    <div className="flex items-center justify-center bg-dark-200 rounded-md p-2 w-full">
                      <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                      <span className="text-sm text-gray-300 truncate max-w-[200px]">
                        {modelFile ? modelFile.name : "3D Model"}
                      </span>
                    </div>
                  ) : (
                    <Upload className="w-8 h-8 text-gray-500" />
                  )}

                  <div className="text-center">
                    <p className="text-sm text-gray-400">
                      {modelPreview ? "Replace 3D model" : "Upload 3D model"}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Supported formats: GLB, GLTF, OBJ, FBX, STL, USDZ
                    </p>
                  </div>

                  <input
                    type="file"
                    id="model-upload"
                    accept=".glb,.gltf,.obj,.fbx,.stl,.usdz"
                    onChange={handleModelFileChange}
                    className="hidden"
                  />

                  <button
                    type="button"
                    onClick={() =>
                      document.getElementById("model-upload")?.click()
                    }
                    className="btn btn-secondary btn-sm mt-2"
                  >
                    Select File
                  </button>
                </div>
              </div>
            </div>

            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Product Image
              </label>
              <div className="border-2 border-dashed border-gray-700 rounded-lg p-4">
                <div className="flex flex-col items-center justify-center space-y-2">
                  {imagePreview ? (
                    <div className="w-full h-32 bg-dark-200 rounded-md overflow-hidden">
                      <img
                        src={imagePreview}
                        alt="Product preview"
                        className="w-full h-full object-contain"
                      />
                    </div>
                  ) : (
                    <ImageIcon className="w-8 h-8 text-gray-500" />
                  )}

                  <div className="text-center">
                    <p className="text-sm text-gray-400">
                      {imagePreview ? "Replace image" : "Upload product image"}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Recommended: 800x800px, JPG or PNG
                    </p>
                  </div>

                  <input
                    type="file"
                    id="image-upload"
                    accept="image/*"
                    onChange={handleImageFileChange}
                    className="hidden"
                  />

                  <button
                    type="button"
                    onClick={() =>
                      document.getElementById("image-upload")?.click()
                    }
                    className="btn btn-secondary btn-sm mt-2"
                  >
                    Select Image
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-800">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={submitting}
          >
            Cancel
          </button>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={submitting}
          >
            {submitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              "Update Product"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductUpdateForm;
