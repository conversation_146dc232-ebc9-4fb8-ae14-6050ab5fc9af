/**
 * Subscription middleware index file
 * Exports all subscription-related middleware for easy importing
 */

const verifySubscription = require('./verifySubscription');
const verifyPaidSubscription = require('./verifyPaidSubscription');
const verifyProSubscription = require('./verifyProSubscription');
const verifyEnterpriseSubscription = require('./verifyEnterpriseSubscription');
const verifyProductLimit = require('./verifyProductLimit');

module.exports = {
  verifySubscription,         // Allows free tier and paid users (basic access)
  verifyPaidSubscription,     // Requires any paid plan (basic, pro, enterprise)
  verifyProSubscription,      // Requires pro or enterprise plan
  verifyEnterpriseSubscription, // Requires enterprise plan only
  verifyProductLimit          // Checks if user is within their product limit
};
