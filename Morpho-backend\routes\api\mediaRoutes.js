const router = require("express").Router();
const controller = require("../../controllers/mediaController");
const { upload } = require("../../middleware/multer-middleware");
const { validateMediaSchema } = require("../../validation/middleware");
const verifyRoles = require("../../middleware/verifyRole");
const { admin, designer } = require("../../constants");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
router
  .route("/create-media")
  .post(validateMediaSchema, asyncError<PERSON>andler(controller.createMedia));
router
  .route("/upload-media")
  .post(upload.array("file"), asyncError<PERSON>andler(controller.uploadFiles));
router.get(
  "/get-all-medias",
  verifyRoles([admin]),
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getAllMedias)
);
router.get("/get-media-by-id", asyncError<PERSON><PERSON><PERSON>(controller.getMediaById));
router.post(
  "/delete-media-by-id",
  async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(controller.deleteMediaById)
);

module.exports = router;
