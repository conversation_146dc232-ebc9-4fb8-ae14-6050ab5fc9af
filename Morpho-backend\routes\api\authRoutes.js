const router = require("express").Router();
const controller = require("../../controllers/authController");
const verifyJWT = require("../../middleware/verifyJWT");
const verifyRole = require("../../middleware/verifyRole");
const { designer, admin, client } = require("../../constants");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");

// Apply asyncErrorHandler to all routes to handle async errors
router.post("/signup", asyncError<PERSON>andler(controller.signup));
router.post(
  "/admin-signup",
  verifyJWT,
  verifyRole([admin]),
  asyncError<PERSON>and<PERSON>(controller.signUpDeveloper)
);
router.post("/login", asyncError<PERSON>andler(controller.login));
router.post(
  "/logout-phone",
  verifyJWT,
  asyncError<PERSON>andler(controller.logoutPhone)
);
router.get("/refresh-token", asyncError<PERSON><PERSON><PERSON>(controller.handleRefeshToken));
router.post("/logout", async<PERSON>rror<PERSON><PERSON><PERSON>(controller.logout));
router.post(
  "/change-password",
  verifyJWT,
  asyncErrorHandler(controller.changePassword)
);
router.put("/forgot-password", asyncErrorHandler(controller.forgotPassword));
router.put("/reset-password", asyncErrorHandler(controller.resetPassword));

module.exports = router;
