const mongoose = require("mongoose");
const showroomTextureSchema = new mongoose.Schema(
  {
    customizationName: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    url: {
      type: String,
      required: true,
    },
    metallic: {
      type: Number,
      required: true,
    },
    roughness: {
      type: Number,
      required: true,
    },
    transparencyMode: {
      type: Number,
      required: true,
    },
    alpha: {
      type: Number,
      required: true,
    },
    uScale: {
      type: Number,
      required: true,
    },
    vScale: {
      type: Number,
      required: true,
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ShowroomCategory",
      required: true,
    },
    iconUrl: {
      type: String,
      match:
        /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/,
    },
    //category required iconUrl
  },
  { timestamps: true, collection: "showroomTexture" },
);

const showroomTextureModel = mongoose.model(
  "showroomTexture",
  showroomTextureSchema,
);

module.exports = showroomTextureModel;
