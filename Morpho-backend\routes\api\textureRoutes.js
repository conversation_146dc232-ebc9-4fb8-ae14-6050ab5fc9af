const router = require("express").Router();
const controller = require("../../controllers/textureController");
const verifyJWT = require("../../middleware/verifyJWT");
const verifyRoles = require("../../middleware/verifyRole");
const { designer, admin, client } = require("../../constants");
const { validateTextureSchema } = require("../../validation/middleware");
const asyncErrorHandler = require("../../errors/asyncErrorHandler");
router
  .route("/create-texture")
  .post(
    validateTextureSchema,
    verifyJWT,
    verifyRoles([admin, designer]),
    asyncError<PERSON>andler(controller.createTexture)
  );
router.get(
  "/get-all-textures",
  verifyJWT,
  verifyRoles([admin, designer, client]),
  asyncError<PERSON>andler(controller.getAllTextures)
);
router.get(
  "/get-texture-by-id",
  verifyJWT,
  verifyRoles([admin, designer, client]),
  async<PERSON>rror<PERSON><PERSON><PERSON>(controller.getTextureById)
);
router.get(
  "/get-texture-with-asset",
  verifyJWT,
  verifyRoles([admin, designer, client]),
  asyncErrorHandler(controller.getTextureWithAsset)
);
router.get(
  "/get-texture-with-customized-material",
  verifyJWT,
  verifyRoles([admin, designer, client]),
  asyncErrorHandler(controller.getTextureWithCustomizedMaterial)
);
router.get(
  "/get-texture-with-information",
  verifyJWT,
  verifyRoles([admin, designer, client]),
  asyncErrorHandler(controller.getTextureWithAllInformation)
);
router.delete(
  "/delete-texture",
  verifyJWT,
  verifyRoles([admin, designer]),
  asyncErrorHandler(controller.deleteTexture)
);
module.exports = router;
