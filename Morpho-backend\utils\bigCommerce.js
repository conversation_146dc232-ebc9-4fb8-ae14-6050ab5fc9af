const BigCommerce = require('node-bigcommerce');

const setBigCommerceClientv3 = (req, res, next) => {
  const { clientId, accessToken, storeHash } = req.body;

  if (!clientId || !accessToken || !storeHash) {
    return res.status(400).json({ success: false, message: 'Missing BigCommerce credentials' });
  }

  // Attach BigCommerce client to the request object
  req.bigCommerceClient = new BigCommerce({
    clientId,
    accessToken,
    storeHash,
    responseType: 'json',
    apiVersion: 'v3', // Default to v3; adjust if needed
  });

  next();
};
const setBigCommerceClientv2 = (req, res, next) => {
    const { clientId, accessToken, storeHash } = req.body;
  
    if (!clientId || !accessToken || !storeHash) {
      return res.status(400).json({ success: false, message: 'Missing BigCommerce credentials' });
    }
  
    // Attach BigCommerce client to the request object
    req.bigCommerceClient = new BigCommerce({
      clientId,
      accessToken,
      storeHash,
      responseType: 'json',
      apiVersion: 'v2', // Default to v2; adjust if needed
    });
  
    next();
  };

module.exports = {setBigCommerceClientv3, setBigCommerceClientv2};
