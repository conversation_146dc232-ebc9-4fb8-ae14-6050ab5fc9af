import React from "react";
import {
  Box,
  Library,
  Settings,
  ShoppingCart,
  Users,
  Store,
  BarChart2,
  Code,
  CreditCard,
} from "lucide-react";
import { useLocation, Link, Outlet } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import SubscriptionBanner from "./SubscriptionBanner";
import type { NavItem } from "../types/navigation";

const Layout = () => {
  const location = useLocation();
  const { logout } = useAuth();

  const navigation: NavItem[] = [
    { name: "Dashboard", href: "/dashboard", icon: Box },
    { name: "3D Library", href: "/dashboard/library", icon: Library },
    { name: "Analytics", href: "/dashboard/analytics", icon: BarChart2 },
    { name: "Embed", href: "/dashboard/embed", icon: Code },
    { name: "Integrations", href: "/dashboard/integrations", icon: Store },
    {
      name: "Modeling Requests",
      href: "/dashboard/modeling-request",
      icon: ShoppingCart,
    },
    { name: "Team", href: "/dashboard/team", icon: Users },
    { name: "Subscription", href: "/dashboard/subscription", icon: CreditCard },
    { name: "Settings", href: "/dashboard/settings", icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-dark-400 overflow-hidden">
      <div className="flex h-screen">
        {/* Sidebar */}
        <div className="hidden md:flex md:flex-shrink-0 relative">
          <div className="flex flex-col w-72">
            <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-dark-300/90 backdrop-blur-lg border-r border-gray-800/50">
              <div className="flex items-center flex-shrink-0 px-4">
                <Link to="/" className="flex items-center group cursor-pointer">
                  <Box className="w-8 h-8 text-brand-300" strokeWidth={1.5} />
                  <span className="ml-2 text-xl font-light tracking-wider bg-gradient-to-r from-brand-300 to-brand-100 text-transparent bg-clip-text">
                    morpho
                  </span>
                </Link>
              </div>
              <div className="mt-8 flex flex-col h-full">
                <nav className="flex-1 space-y-1">
                  {navigation.map((item) => {
                    const Icon = item.icon;
                    const isActive =
                      item.href === "/dashboard"
                        ? location.pathname === item.href
                        : location.pathname.startsWith(item.href);

                    return (
                      <Link
                        key={item.name}
                        to={item.href}
                        className={`${
                          isActive
                            ? "nav-link-active"
                            : "text-gray-400 nav-link"
                        } group flex items-center px-4 py-3 text-sm font-light tracking-wide`}
                      >
                        <Icon
                          className={`${
                            isActive
                              ? "text-brand-300"
                              : "text-gray-500 group-hover:text-gray-400"
                          } mr-3 h-5 w-5 flex-shrink-0`}
                          strokeWidth={1.5}
                        />
                        {item.name}
                      </Link>
                    );
                  })}
                </nav>
                <div className="mt-auto p-4">
                  <button
                    onClick={logout}
                    className="w-full btn btn-secondary text-sm"
                  >
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden relative backdrop-blur-sm">
          {/* Subscription Banner */}
          <SubscriptionBanner />

          <main className="flex-1 relative overflow-y-auto focus:outline-none">
            <div className="py-6">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                <Outlet />
              </div>
            </div>
          </main>
          <div className="absolute inset-0 pointer-events-none bg-gradient-mesh opacity-50" />
        </div>
      </div>
    </div>
  );
};

export default Layout;
